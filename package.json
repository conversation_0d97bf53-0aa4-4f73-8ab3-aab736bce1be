{"name": "play-comp-library", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "watch": "ng build --watch --configuration development", "test": "ng test play-comp-library --code-coverage", "playground-test": "ng test playground --code-coverage", "lint": "ng lint", "build-library": "ng build --project=play-comp-library", "watch-library": "ng build --project=play-comp-library --watch", "build-playground": "ng build --project=playground", "pack": "cd dist/play-comp-library && npm pack", "build": "npm run build-playground"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/forms": "^19.1.0", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "@iconify/iconify": "^3.1.1", "angular-eslint": "^19.0.2", "animate.css": "^4.1.1", "d3": "^7.9.0", "eslint": "^9.19.0", "eslint-plugin-angular": "^4.1.0", "lucide-angular": "^0.518.0", "ngx-mask": "^20.0.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "typescript-eslint": "^8.22.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.4", "@angular/build": "^19.1.4", "@angular/cli": "^19.1.4", "@angular/compiler-cli": "^19.1.0", "@types/d3": "^7.4.3", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.1.0", "typescript": "~5.7.2"}}