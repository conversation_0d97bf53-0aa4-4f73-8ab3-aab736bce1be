.user-table-wrapper {
  table-layout: auto;
  position: relative;
  overflow-x: hidden;

  .table-wrapper {
    position: relative;
    overflow-x: auto;
    min-height: 600px;
    border: 1px solid var(--table-border);
    border-radius: 8px;
    width: 100%;
    background-color: var(--table-background-color-odd);

    .ava-user-table {
      table-layout: auto;
      width: max-content;
      min-width: 100%;
      border-collapse: collapse;
      font-family: var(--table-font-family-body);

      border: 1px solid var(--table-border);
      border-radius: 8px;

      th,
      td {
        padding: 1rem;
        text-align: left;
      }

      thead {
        background-color: var(--table-header-background-color);
      }

      tbody tr {
        transition: background-color 0.3s ease;
        height: auto;
        vertical-align: middle;

        &:nth-child(odd) {
          background-color: var(--table-background-color-odd);
        }

        &:nth-child(even) {
          background: var(--table-background-color-even);
        }

        .cell-link {
          color: inherit;
          text-decoration: none;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .ava-icon {
        margin: 2px;
        padding: 2px;
      }
    }

    .dropdown {
      position: relative;

      [data-theme="dark"] .action-button {
        color: var(--table-background-color-odd);

        &:hover {
          color: var(--table-background-color-odd);
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 0.5rem;
        min-width: 13.75rem;
        padding: 0.25rem 0;
        background: var(--table-background-color-odd);
        border: 1px solid var(--table-border);
        border-radius: 0.5rem;
        box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.08);
        z-index: 1000;

        .dropdown-item {
          display: flex;
          align-items: center;
          gap: 0.625rem;
          width: 100%;
          padding: 0.625rem 1rem;
          font-size: 1rem;
          text-align: left;
          background: none;
          border: none;
          cursor: pointer;
          white-space: nowrap;
          color: var(--text-color);

          &:hover {
            background: var(--table-background-color-odd);
          }

          .dropdown-icon {
            font-size: 1rem;
          }
        }
      }
    }

    .actions-cell {
      width: 1%;
      white-space: nowrap;
      text-align: right;

      .action-button {
        background: none;
        border: none;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }

    .actions-wrapper {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      position: relative;

      .action-button,
      .action-icon {
        background: none;
        border: none;
        font-size: 1.125rem;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 0.375rem;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }

    th {
      position: relative;

      .th-content {
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .filter-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 1;

        &.active {
          color: #446dd4;
        }
      }

      .resize-handle {
        position: absolute;
        top: 0;
        right: 0;
        height: 100%;
        width: 0.5rem;
        cursor: col-resize;
        z-index: 10;
      }

      .resize-handle::after {
        content: "|";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var(--table-text-disabled);
        font-size: 12px;
        line-height: 1;
        width: 1px;
        opacity: 0;
        transition: opacity 0.2s;
        pointer-events: none;
      }
      .resize-handle:hover::after {
        opacity: 1;
      }
    }
  }
}

.default-filter-modal {
  position: absolute;
  z-index: 999;
  background-color: var(--table-background-color-odd);
  border: 1px solid var(--table-border);
  border-radius: 8px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  min-width: 240px;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.draggable-icon {
  position: absolute;
  top: 50px;
  right: 0px;
  width: 30px;
  height: 60px;
  background-color: #e4e4e4;
  border-top-left-radius: 30px;
  border-bottom-left-radius: 30px;
  border-top-right-radius: 0%;
  border-bottom-right-radius: 0%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
}

.draggable-icon ava-icon {
  pointer-events: auto;
  transition: transform 0.3s ease;
  max-width: 100%;
  max-height: 100%;
  display: block;
  box-sizing: border-box;
}

.draggable-icon:hover {
  background-color: var(--table-background-color-odd);
  opacity: 0.5;
}

.draggable-icon:hover ava-icon {
  transform: scale(1.1);
}

.draggable-icon.hidden {
  display: none;
}
.column-selector-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: var(--table-background-color-odd);
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
  padding: 1rem;
  z-index: 10;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.column-selector-panel.open {
  transform: translateX(0);
}

.column-selector-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--table-border);
  margin-bottom: 1rem;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.checkbox-col {
  width: 25px;
  max-width: 20px;
  min-width: 20px;
  text-align: center;
  padding: 0;
}
