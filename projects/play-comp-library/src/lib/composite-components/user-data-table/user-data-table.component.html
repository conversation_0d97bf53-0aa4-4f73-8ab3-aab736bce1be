<div class="user-table-wrapper" #tableRef>
  <!-- Column Slider Panel -->
  <div class="table-wrapper" #wrapper>
    <div
      #draggableWrapper
      class="draggable-icon"
      [class.hidden]="showColumnPanel"
      (click)="onToggleColumnPanel()"
    >
      <ava-icon
        iconName="ArrowLeft"
        [iconSize]="16"
        [cursor]="true"
        (userClick)="onToggleColumnPanel()"
      ></ava-icon>
    </div>

    <div #tableContentWrapper>
      <table class="ava-user-table" #actualTable>
        <thead>
          <tr
            (dragover)="columnDrag ? onColumnDragOver($event) : null"
            (drop)="columnDrag ? onColumnDrop($event) : null"
          >
            <ng-container *ngFor="let col of columns; let colIndex = index">
              <th
                *ngIf="col.visible"
                [attr.draggable]="
                  columnDrag && col.field !== 'select' ? true : null
                "
                (dragstart)="
                  columnDrag && col.field !== 'select'
                    ? onColumnDragStart($event, colIndex)
                    : null
                "
                (dragenter)="
                  columnDrag && col.field !== 'select'
                    ? onColumnDragEnter($event, colIndex)
                    : null
                "
                [class.drag-over]="
                  columnDrag &&
                  dragOverColIndex === colIndex &&
                  col.field !== 'select'
                "
                [class.sortable]="col.sortable"
                [class.filterable]="col.filterable"
                [class.sorted-asc]="
                  sortColumn === col.field && sortDirection === 'asc'
                "
                [class.sorted-desc]="
                  sortColumn === col.field && sortDirection === 'desc'
                "
                (mouseenter)="hoveredCol = col.field"
                (mouseleave)="hoveredCol = null"
                (click)="col.sortable ? onSort(col) : null"
                [ngClass]="{ 'checkbox-col': col.field === 'select' }"
              >
                <ng-container
                  *ngIf="
                    col.field === 'select' && showCheckbox;
                    else normalHeader
                  "
                >
                  <!-- Checkbox Header -->
                  <ava-checkbox
                    [isChecked]="parentChecked"
                    [indeterminate]="indeterminate"
                    (isCheckedChange)="onSelectAllChange($event)"
                  ></ava-checkbox>
                </ng-container>

                <ng-template #normalHeader>
                  <div class="th-content">
                    <span class="header-label">{{ col.label }}</span>
                    <!-- Sort Icon -->
                    <ava-icon
                      *ngIf="col.sortable && sortColumn === col.field"
                      [iconName]="
                        sortDirection === 'asc' ? sortAscIcon : sortDescIcon
                      "
                      [iconSize]="16"
                      class="sort-icon"
                    ></ava-icon>
                  </div>

                  <!-- Filter Icon -->
                  <ava-icon
                    *ngIf="
                      (col.filterable && useDefaultFilter) ||
                      (col.filterable && useCustomFilter)
                    "
                    class="filter-icon"
                    [iconName]="filterIcon"
                    [class.active]="isFilterActive(col.field)"
                    [iconSize]="16"
                    title="Filter"
                    (click)="onFilterIconClick($event, col.field)"
                  ></ava-icon>
                </ng-template>

                <div
                  class="resize-handle"
                  *ngIf="resizable && col.field !== 'select'"
                  (mousedown)="onResizeMouseDown($event, colIndex)"
                ></div>
              </th>
            </ng-container>
          </tr>
        </thead>

        <tbody *ngIf="displayedRows.length > 0">
          <tr
            *ngFor="let row of displayedRows; let rowIndex = index"
            [attr.draggable]="rowDrag ? true : null"
            (dragstart)="rowDrag ? onRowDragStart($event, rowIndex) : null"
            (dragenter)="rowDrag ? onRowDragEnter($event, rowIndex) : null"
            (dragover)="rowDrag ? onRowDragOver($event) : null"
            (drop)="rowDrag ? onRowDrop($event) : null"
            [class.drag-over]="rowDrag && dragOverRowIndex === rowIndex"
          >
            <ng-container *ngFor="let col of columns">
              <td
                *ngIf="col.visible"
                [ngClass]="{ 'actions-cell': col.field === 'actions' }"
                [ngClass]="{ 'checkbox-col': col.field === 'select' }"
              >
                <!-- Checkbox Cell -->
                <ng-container
                  *ngIf="
                    col.field === 'select' && showCheckbox;
                    else normalCell
                  "
                >
                  <ava-checkbox
                    [isChecked]="row.isSelected"
                    (isCheckedChange)="onRowCheckboxChange(row, $event)"
                  ></ava-checkbox>
                </ng-container>

                <ng-template #normalCell>
                  <ng-container *ngIf="col.field !== 'actions'; else actions">
                    <ng-container
                      *ngIf="
                        row[col.field]?.clickable === true;
                        else normalText
                      "
                    >
                      <a
                        href="javascript:void(0)"
                        (click)="handleCellClick(row, col.field)"
                        class="cell-link"
                      >
                        <ng-container *ngIf="row[col.field]?.iconName">
                          <ng-container
                            *ngIf="
                              isUrl(row[col.field]?.iconName);
                              else renderAvaIcon
                            "
                          >
                            <span
                              class="dynamic-icon"
                              [innerHTML]="getIcon(row[col.field]?.iconName)"
                            ></span>
                          </ng-container>
                          <ng-template #renderAvaIcon>
                            <ava-icon
                              [iconName]="row[col.field]?.iconName"
                              [iconSize]="'16'"
                              class="ava-icon"
                            ></ava-icon>
                          </ng-template>
                        </ng-container>
                        <span>{{ getCellValue(row, col.field) }}</span>
                      </a>
                    </ng-container>

                    <ng-template #normalText>
                      <ng-container *ngIf="row[col.field]?.iconName">
                        <ng-container
                          *ngIf="
                            isUrl(row[col.field]?.iconName);
                            else renderAvaIconText
                          "
                        >
                          <span
                            class="dynamic-icon"
                            [innerHTML]="getIcon(row[col.field]?.iconName)"
                          ></span>
                        </ng-container>
                        <ng-template #renderAvaIconText>
                          <ava-icon
                            [iconName]="row[col.field]?.iconName"
                            [iconSize]="'16'"
                            class="ava-icon"
                          ></ava-icon>
                        </ng-template>
                      </ng-container>
                      <span>{{ getCellValue(row, col.field) }}</span>
                    </ng-template>
                  </ng-container>

                  <ng-template #actions>
                    <div class="actions-wrapper">
                      <ng-container *ngFor="let entry of getInlineActions(row)">
                        <div
                          class="action-icon"
                          [title]="entry[1].label"
                          (click)="handleAction(row, entry[0])"
                        >
                          <span class="dynamic-icon" *ngIf="entry[1].icon">
                            <ng-container
                              *ngIf="
                                isUrl(entry[1].icon);
                                else renderActionAvaIcon
                              "
                            >
                              <span
                                [innerHTML]="getActionIconHtml(entry[1].icon)"
                              ></span>
                            </ng-container>
                            <ng-template #renderActionAvaIcon>
                              <ava-icon
                                [iconName]="entry[1].icon"
                                [iconSize]="'16'"
                                class="ava-icon"
                              ></ava-icon>
                            </ng-template>
                          </span>
                        </div>
                      </ng-container>

                      <div class="dropdown" *ngIf="shouldShowDropdown(row)">
                        <ava-icon
                          class="ava-icon"
                          iconName="ellipsis-vertical"
                          [iconSize]="16"
                          [cursor]="true"
                          (click)="toggleDropdown(row)"
                        ></ava-icon>
                        <div
                          class="dropdown-menu"
                          *ngIf="dropdownRow === row"
                          #dropdownMenu
                          (mouseleave)="dropdownRow = null"
                        >
                          <ng-container
                            *ngFor="let entry of getDropdownActions(row)"
                          >
                            <div
                              class="dropdown-item"
                              role="button"
                              tabindex="0"
                              (click)="handleAction(row, entry[0])"
                            >
                              <span class="dropdown-icon" *ngIf="entry[1].icon">
                                <ng-container
                                  *ngIf="
                                    isUrl(entry[1].icon);
                                    else renderDropdownAvaIcon
                                  "
                                >
                                  <span
                                    [innerHTML]="
                                      getActionIconHtml(entry[1].icon)
                                    "
                                  ></span>
                                </ng-container>
                                <ng-template #renderDropdownAvaIcon>
                                  <ava-icon
                                    [iconName]="entry[1].icon"
                                    [iconSize]="'16'"
                                    class="ava-icon"
                                  ></ava-icon>
                                </ng-template>
                              </span>
                              {{ entry[1].label }}
                            </div>
                          </ng-container>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </ng-template>
              </td>
            </ng-container>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="column-selector-panel" [class.open]="showColumnPanel">
    <div class="panel-header">
      <h3>Columns</h3>
      <ava-icon
        iconName="x"
        [iconSize]="16"
        [cursor]="true"
        (userClick)="onToggleColumnPanel()"
      ></ava-icon>
    </div>

    <ava-textbox
      label=""
      placeholder="Search columns..."
      [(ngModel)]="columnSearch"
    ></ava-textbox>

    <div *ngFor="let col of filteredColumns()">
      <ng-container class="slider-list" *ngIf="col.field !== 'select'">
        <ava-checkbox
          [label]="col.label"
          [isChecked]="col.visible"
          (isCheckedChange)="col.visible = $event"
        ></ava-checkbox>
      </ng-container>
    </div>
  </div>

  <!-- Default Filter Modal -->
  <div
    class="default-filter-modal"
    *ngIf="
      useDefaultFilter &&
      openFilterField &&
      defaultColumnFilters[openFilterField!]
    "
    [style.left.px]="filterDropdownPosition?.x"
    [style.top.px]="filterDropdownPosition?.y"
  >
    <ava-dropdown
      [dropdownTitle]="getSelectedFilterLabel(openFilterField!)"
      [options]="defaultFilterConditions"
      [(ngModel)]="defaultColumnFilters[openFilterField!].type"
    ></ava-dropdown>
    <ava-textbox
      *ngIf="!isFilterTypeEmpty(defaultColumnFilters[openFilterField!].type)"
      label=""
      placeholder="Enter value"
      [(ngModel)]="defaultColumnFilters[openFilterField!].value"
    ></ava-textbox>

    <div class="default-filter-actions">
      <ava-button
        label="Filter"
        variant="primary"
        (userClick)="applyAndCloseFilter()"
        pressedEffect="ripple"
      ></ava-button>
      <ava-button
        label="Clear"
        variant="secondary"
        (userClick)="clearDefaultFilter()"
        pressedEffect="ripple"
      ></ava-button>
    </div>
  </div>

  <!-- Custom filter modal -->
  <div
    class="default-filter-modal"
    *ngIf="useCustomFilter && openFilterField"
    [style.left.px]="filterDropdownPosition?.x"
    [style.top.px]="filterDropdownPosition?.y"
  >
    <ava-checkbox
      label="Select All"
      [isChecked]="isAllFilterSelected(openFilterField)"
      [indeterminate]="isIndeterminate(openFilterField)"
      (isCheckedChange)="toggleAllFilterValues(openFilterField, $event)"
    ></ava-checkbox>

    <label *ngFor="let val of columnFilters[openFilterField]">
      <ava-checkbox
        [label]="val"
        [isChecked]="tempSelectedFilters[openFilterField].has(val)"
        (isCheckedChange)="toggleFilterValue(openFilterField, val, $event)"
      ></ava-checkbox>
    </label>

    <ava-button
      label="Filter"
      variant="primary"
      (userClick)="updateFilter()"
    ></ava-button>
  </div>
</div>
<!-- Pagination Controls -->
<div class="pagination-container" *ngIf="displayedRows.length > 0">
  <ava-pagination-controls
    [type]="'basic'"
    [currentPage]="currentPage"
    [totalPages]="totalPages"
    (pageChange)="onPageChange($event)"
  ></ava-pagination-controls>
</div>
