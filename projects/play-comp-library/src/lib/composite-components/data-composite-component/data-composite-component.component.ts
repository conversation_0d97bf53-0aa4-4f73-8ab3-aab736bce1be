import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
  OnInit,
  OnChanges,
  ChangeDetectorRef,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../components/icon/icon.component';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { AvaTagComponent } from '../../components/tags/tags.component';
import { DataGridComponent } from '../../components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../components/data-grid/directive/ava-cell-def.directive';

export interface JournalColumn {
  id: string;
  label: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: unknown, row: Record<string, unknown>) => string;
}

export interface JournalDataGridEvent {
  type: 'selection-change' | 'journal-id-click';
  data?: unknown;
  selectedRows?: Record<string, unknown>[];
  row?: Record<string, unknown>;
}

@Component({
  selector: 'ava-data-composite-component',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    CheckboxComponent,
    AvaTagComponent,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
  ],
  templateUrl: './data-composite-component.component.html',
  styleUrl: './data-composite-component.component.scss',
  changeDetection: ChangeDetectionStrategy.Default,
  encapsulation: ViewEncapsulation.None,
})
export class DataCompositeComponent implements OnInit, OnChanges {
  @Input() data: Record<string, unknown>[] = [];
  @Input() columns: JournalColumn[] = [];
  @Input() disabled = false;
  @Input() emptyMessage = 'No journal entries found';
  @Input() filteredData: Record<string, unknown>[] = [];

  @Output() selectionChange = new EventEmitter<Record<string, unknown>[]>();
  @Output() journalIdClick = new EventEmitter<Record<string, unknown>>();
  @Output() dataGridEvent = new EventEmitter<JournalDataGridEvent>();

  selectedRows = new Set<Record<string, unknown>>();

  // Data grid properties
  displayedColumns: string[] = [];
  sortedData: Record<string, unknown>[] = [];
  dataTimestamp = Date.now();

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit() {
    console.log('🚀 Child component ngOnInit called');
    console.log('🚀 Initial data length:', this.data.length);
    console.log('🚀 Initial filteredData length:', this.filteredData.length);
    console.log('🚀 Initial data:', this.data);
    console.log('🚀 Initial filteredData:', this.filteredData);
    this.initializeDataGrid();
  }

  ngOnChanges(changes: SimpleChanges) {
    console.log('🔄 === Child component ngOnChanges called ===');
    console.log('Changes:', changes);

    // Check for any data-related changes
    if (changes['filteredData'] || changes['data']) {
      if (changes['filteredData']) {
        console.log('📊 filteredData changed!');
        console.log('  Previous value length:', changes['filteredData'].previousValue?.length || 0);
        console.log('  Current value length:', changes['filteredData'].currentValue?.length || 0);
        console.log('  Previous value:', changes['filteredData'].previousValue);
        console.log('  Current value:', changes['filteredData'].currentValue);
      }

      if (changes['data']) {
        console.log('📊 data changed!');
        console.log('  Previous data length:', changes['data'].previousValue?.length || 0);
        console.log('  Current data length:', changes['data'].currentValue?.length || 0);
        console.log('  Previous data:', changes['data'].previousValue);
        console.log('  Current data:', changes['data'].currentValue);
      }

      // Update timestamp to force re-render
      this.dataTimestamp = Date.now();
      console.log('⏰ Updated dataTimestamp:', this.dataTimestamp);

      // Re-initialize the data grid
      this.initializeDataGrid();

      // Force change detection
      this.cdr.markForCheck();
      this.cdr.detectChanges();
      console.log('✅ Forced change detection');
    }

    console.log('🏁 Final state:');
    console.log('  - filteredData length:', this.filteredData.length);
    console.log('  - data length:', this.data.length);
    console.log('  - sortedData length:', this.sortedData.length);
    console.log('🔄 === End ngOnChanges ===');
  }

  private initializeDataGrid() {
    // Set up displayed columns including selection column
    this.displayedColumns = ['selection', ...this.columns.map(col => col.id)];

    // ALWAYS use filteredData as the primary source - parent manages this
    this.sortedData = [...this.filteredData];

    console.log('=== initializeDataGrid called ===');
    console.log('- filteredData length:', this.filteredData.length);
    console.log('- data length:', this.data.length);
    console.log('- sortedData length:', this.sortedData.length);
    console.log('- displayedColumns:', this.displayedColumns);
    console.log('- filteredData:', this.filteredData);
    console.log('- data:', this.data);
    console.log('=== end initializeDataGrid ===');
  }

  // Public method to manually refresh the data grid
  public refreshDataGrid() {
    console.log('🔄 Manual refreshDataGrid called');
    this.dataTimestamp = Date.now();
    this.initializeDataGrid();
    this.cdr.markForCheck();
    this.cdr.detectChanges();
  }

  // Handle data grid sorting
  onDataSorted(sortedData: Record<string, unknown>[]) {
    this.sortedData = sortedData;
  }

  get selectedRowsArray(): Record<string, unknown>[] {
    return Array.from(this.selectedRows);
  }

  get hasSelection(): boolean {
    return this.selectedRows.size > 0;
  }

  get isAllSelected(): boolean {
    return this.sortedData.length > 0 && this.selectedRows.size === this.sortedData.length;
  }

  get isIndeterminate(): boolean {
    return (
      this.selectedRows.size > 0 && this.selectedRows.size < this.sortedData.length
    );
  }

  trackByRow(index: number): number {
    return index;
  }

  onRowSelectionChange(row: Record<string, unknown>, selected: boolean) {
    if (selected) {
      this.selectedRows.add(row);
    } else {
      this.selectedRows.delete(row);
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataGridEvent('selection-change', this.selectedRowsArray);
  }

  onSelectAllChange(selected: boolean) {
    if (selected) {
      this.sortedData.forEach((row) => this.selectedRows.add(row));
    } else {
      this.selectedRows.clear();
    }
    this.selectionChange.emit(this.selectedRowsArray);
    this.emitDataGridEvent('selection-change', this.selectedRowsArray);
  }

  onJournalIdClick(row: Record<string, unknown>) {
    this.journalIdClick.emit(row);
    this.emitDataGridEvent('journal-id-click', { row });
  }

  isRowSelected(row: Record<string, unknown>): boolean {
    return this.selectedRows.has(row);
  }

  isSpecialColumn(columnId: string): boolean {
    return ['journalStatus', 'documents', 'journalId'].includes(columnId);
  }

  getCellValue(row: Record<string, unknown>, column: JournalColumn): string {
    const value = row[column.id];
    if (column.render) {
      return column.render(value, row);
    }
    return String(value || '');
  }

  getStatusColor(
    status: string
  ): 'success' | 'warning' | 'info' | 'error' | 'default' {
    switch (status) {
      case 'posted':
        return 'success';
      case 'template':
        return 'warning';
      case 'rejected':
        return 'error';
      case 'ready to approve':
        return 'info';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  }

  getString(value: unknown): string {
    return String(value);
  }

  getIconName(row: Record<string, unknown>): string {
    // Get icon name from row data, fallback to 'file-text' if not provided
    return String(row['iconName'] || 'file-text');
  }

  private emitDataGridEvent(type: JournalDataGridEvent['type'], data?: unknown) {
    const event: JournalDataGridEvent = {
      type,
      data,
      selectedRows: this.selectedRowsArray,
    };
    this.dataGridEvent.emit(event);
  }
}
