<!-- Journal Data Grid Content - Header is now in parent component -->
<div class="journal-data-grid-content">


  <!-- Data Grid -->
  <div class="data-grid-container">
    <ava-data-grid [dataSource]="sortedData" [displayedColumns]="displayedColumns" [zerbaLine]="true"
      [isLoading]="false" [attr.data-timestamp]="dataTimestamp" (dataSorted)="onDataSorted($event)">

      <!-- Selection Column -->
      <ng-container avaColumnDef="selection" [sortable]="false" [filter]="false">
        <div *avaHeaderCellDef class="checkbox-column">
          <ava-checkbox [isChecked]="isAllSelected" [indeterminate]="isIndeterminate" [disable]="disabled"
            (isCheckedChange)="onSelectAllChange($event)">
          </ava-checkbox>
        </div>
        <div *avaCellDef="let row" class="checkbox-cell">
          <ava-checkbox [isChecked]="isRowSelected(row)" [disable]="disabled"
            (isCheckedChange)="onRowSelectionChange(row, $event)">
          </ava-checkbox>
        </div>
      </ng-container>

      <!-- Dynamic Columns -->
      <ng-container *ngFor="let column of columns" [avaColumnDef]="column.id" [sortable]="false" [filter]="false">
        <div *avaHeaderCellDef class="column-header" [style.text-align]="column.align || 'left'">
          {{ column.label }}
        </div>

        <div *avaCellDef="let row" class="data-cell" [style.text-align]="column.align || 'left'">
          <!-- Journal Status Column with Tags -->
          <ava-tag *ngIf="column.id === 'journalStatus'" [label]="getString(row[column.id])"
            [color]="getStatusColor(getString(row[column.id]).toLowerCase())" variant="outlined" size="sm">
          </ava-tag>

          <!-- Documents Column with Icon -->
          <div *ngIf="column.id === 'documents'" class="documents-cell">
            <ava-icon [iconName]="getIconName(row)" [iconSize]="16" iconColor="var(--grid-text-color)">
            </ava-icon>
          </div>

          <!-- Journal ID Column with Link Style -->
          <span *ngIf="column.id === 'journalId'" class="journal-id-link" (click)="onJournalIdClick(row)"
            (keydown.enter)="onJournalIdClick(row)" tabindex="0" role="button">
            {{ getCellValue(row, column) }}
          </span>

          <!-- Journal Description Column -->
          <span *ngIf="column.id === 'journalDescription'" class="journal-description-cell">
            {{ getCellValue(row, column) }}
          </span>

          <!-- Source Transaction Column -->
          <span *ngIf="column.id === 'sourceTransaction'" class="source-transaction-cell">
            {{ getCellValue(row, column) }}
          </span>

          <!-- Dr/Cr Totals Column -->
          <span *ngIf="column.id === 'drCrTotals'" class="dr-cr-totals-cell">
            {{ getCellValue(row, column) }}
          </span>

          <!-- Date/Month Column -->
          <span *ngIf="column.id === 'date'" class="month-date-cell">
            {{ getCellValue(row, column) }}
          </span>

          <!-- Regular Columns -->
          <span
            *ngIf="!isSpecialColumn(column.id) && column.id !== 'journalDescription' && column.id !== 'sourceTransaction' && column.id !== 'drCrTotals' && column.id !== 'date'">
            {{ getCellValue(row, column) }}
          </span>
        </div>
      </ng-container>
    </ava-data-grid>
  </div>

  <!-- Empty State -->
  <div *ngIf="sortedData.length === 0" class="empty-state">
    <p>{{ emptyMessage }}</p>
  </div>
</div>