import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, input, TemplateRef, ViewEncapsulation } from '@angular/core';
import { DefaultCardComponent } from '../../components/card/default-card/default-card.component';

export interface CardData {
  header?: {
    title: string,
    iconName: string,
    viewAll: boolean
  };
  contents?: [
    { session1: any }
  ];
  footer?: {};
}

@Component({
  selector: 'ava-approval-card',
  imports: [CommonModule, DefaultCardComponent],
  templateUrl: './approval-card.component.html',
  styleUrl: './approval-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ApprovalCardComponent {
  @Input() height: string = '300px';
  @Input() background: string = '';



}
