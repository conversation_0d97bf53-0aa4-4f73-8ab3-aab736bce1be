.ava-console-approval-card-container {
    .ava-default-card-container .ava-default-card {
        border: 1px solid #fff;

        ava-card-header {
            position: relative;
            display: flex;
    

            .header {
                display: flex;
            }

            h2 {
                color: #14161F;
                font-family: var(--card-font-family);
                font-weight: var(--card-font-weight);
                letter-spacing: 0%;
            }

            ava-icon {
                position: absolute;
                right: 0;
                top: 7px
            }

            ava-tag {
                margin-right: 7px;
                margin-top:0px;
            }
        }

        ava-card-content {
            color: var(--card-primary-color);

            .user-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;

                .user-info {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    flex: 1;
                }

                .priority-tag {
                    margin-left: auto;
                    flex-shrink: 0;
                }
            }

            .description {
                margin-top: 32px;
                margin-bottom: 20px;

                .paragraph {
                    font-size: 16px;
                    line-height: 1.6;
                    margin: 0;
                }
            }

            .timestamp {

                span {
                    font-size: 14px;
                    font-weight: 500;
                }
            }

            .tag-wrapper {
                position: relative;
                margin-bottom: 28px;

                ava-tag {
                    margin-right: 10px;
                }
            }

            .info-wrapper {
                display: flex;
                align-items: center;
                margin-bottom: 28px;

                .f {
                    margin-right: 75px;

                    ava-icon {
                        margin-right: 5px;

                        svg {
                            stroke-width: 4px;
                        }
                    }
                }

                .s {
                    ava-icon {
                        margin-right: 5px;

                        svg {
                            stroke-width: 2px;
                        }
                    }

                    span {
                        position: relative;
                        top: 3px;
                    }
                }
            }
        }

        ava-card-footer {
            .action-buttons {
                display: flex;
                gap: 12px;
                flex-wrap: wrap;

                ava-button {
                    flex: 1;
                    min-height: 44px;
                    min-width: 200px;
                }
            }

            .footer-content {
                color: var(--card-primary-color);
                display: flex;

                span {
                    font-family: var(--card-heading1-font);
                }

                .footer-left {
                    .ex {
                        font-size: 12px;
                        font-weight: bold;
                    }

                    ava-icon {
                        margin-right: 10px;
                    }
                }

                .footer-right {
                    margin-top: 1rem;
                    margin-left: auto;
                }

                ava-button {
                    margin-left: 5px;
                }
            }
        }
    }

    // Responsive Design
    @media (max-width: 768px) {
        .ava-default-card-container .ava-default-card {
            ava-card-header {
                .header-content {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 16px;

                    .card-title {
                        font-size: 20px;
                    }

                    .edit-icon {
                        margin-left: 0;
                        align-self: flex-end;
                    }
                }
            }

            ava-card-content {
                .user-row {
                    flex-direction: column;
                    align-items: stretch;
                    gap: 16px;

                    .user-info {
                        align-self: flex-start;
                    }

                    .priority-tag {
                        align-self: flex-end;
                        margin-left: 0;
                    }
                }

                .description .paragraph {
                    font-size: 14px;
                }
            }

            ava-card-footer {
                .action-buttons {
                    flex-direction: column;
                    gap: 12px;

                    ava-button {
                        min-width: 100%;
                    }
                }
            }
        }
    }

    @media (max-width: 480px) {
        .ava-default-card-container .ava-default-card {
            ava-card-header {
                .header-content .card-title {
                    font-size: 18px;
                }
            }

            ava-card-content {
                .user-info {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                }
            }
        }
    }
}