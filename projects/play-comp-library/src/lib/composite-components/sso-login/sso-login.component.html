<div class="sso-login-container" [ngClass]="getContainerClasses()">
  <!-- Header Section -->
  <div class="login-header">
    <h1 class="login-title">Get Started</h1>
    <p class="login-subtitle">Sign In To Your Account</p>
  </div>

  <!-- Login Form -->
  <div class="login-form" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
    <div class="username-field">
      <!-- Username/Email Field -->
      <ava-textbox label="Username or email" type="text" placeholder="Enter Text Here" [value]="username?.value || ''"
        [required]="true" [error]="submitted ? usernameError : ''" [disabled]="loading || disabled" [size]="variant" (textboxInput)="onUsernameChange($event)" (textboxBlur)="username?.markAsTouched()"
        width="100%">
      </ava-textbox>
    </div>

    <div class="passwod-field">
      <!-- Password Field -->
      <ava-textbox inputKind="password" label="Password"  placeholder="Enter Text Here"
        [value]="password?.value || ''" [required]="true" [error]="submitted ? passwordError : ''" labelPosition="end"
        [disabled]="loading || disabled" [size]="variant" (textboxInput)="onPasswordChange($event)" (textboxBlur)="password?.markAsTouched()"
        (iconEndClick)="togglePasswordVisibility()" width="100%">
      </ava-textbox>
    </div>

    <!-- Options Row -->
    <div class="form-options">
      <ava-checkbox label="Keep me signed in" [isChecked]="keepSignedIn?.value || false"
        (isCheckedChange)="onKeepSignedInChange($event)">
      </ava-checkbox>

      <ava-link label="Forgot Password?" color="primary" (userClick)="onForgotPassword()">
      </ava-link>

    </div>

    <!-- Sign In Button -->
    <div class="form-actions">
      <ava-button type="submit" variant="primary" size="large" label="Sign in" [disabled]="!isFormValid"
        [iconName]="'arrow-right'" [iconPosition]="'right'" width="100%">
      </ava-button>
    </div>

    <!-- Divider with "or" text -->
    <div class="divider-section">
      <ava-dividers variant="solid" color="#e0e0e0"></ava-dividers>
      <span class="divider-text">or</span>
      <ava-dividers variant="solid" color="#e0e0e0"></ava-dividers>
    </div>

    <!-- SSO Login Button -->
    <div class="sso-section">
      <ava-button variant="secondary" size="large" label="Login with SSO" (userClick)="onSSOLogin()"
        [disabled]="loading || disabled" width="100%">
        <span class="sso-highlight">SSO</span>
      </ava-button>
    </div>

    <!-- Trouble Signing In Link -->
    <div class="trouble-signin">
      <span class="trouble-text">Still having trouble signing in?</span>
      <ava-link label="Click Here" color="primary" (userClick)="onTroubleSigningIn()">
      </ava-link>
    </div>
  </div>
</div>