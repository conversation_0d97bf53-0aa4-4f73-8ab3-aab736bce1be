<div class="dashboard-widget-grid">
  <div class="dashboard-header">
    <div class="header-content">
      <div class="title-section">
        <h2 class="dashboard-title">{{ config.title }}</h2>
        <p *ngIf="config.description" class="dashboard-description">
          {{ config.description }}
        </p>
      </div>
      <div class="header-actions">
        <ava-button
          *ngIf="config.editable"
          (click)="toggleEditMode()"
          [variant]="isEditMode ? 'warning' : 'primary'"
          size="medium"
          [label]="isEditMode ? 'Exit Edit Mode' : 'Edit Dashboard'"
          [iconName]="isEditMode ? 'x' : 'edit'"
          [iconSize]="16"
        >
        </ava-button>
        <ava-button
          *ngIf="isEditMode"
          (click)="onSaveLayout()"
          variant="success"
          size="medium"
          label="Save Layout"
          iconName="save"
          [iconSize]="16"
        >
        </ava-button>
        <ava-button
          *ngIf="isEditMode"
          (click)="onResetLayout()"
          variant="secondary"
          size="medium"
          label="Reset Layout"
          iconName="rotate-ccw"
          [iconSize]="16"
        >
        </ava-button>
      </div>
    </div>
  </div>

  <div class="dashboard-content">
    <!-- Widget Type Selector -->
    <div *ngIf="isEditMode" class="widget-selector">
      <h3>Add Widget</h3>
      <div class="widget-types-grid">
        <ava-button
          *ngFor="let widgetType of widgetTypes"
          (click)="onAddWidget(widgetType.id)"
          variant="secondary"
          size="medium"
          [label]="widgetType.label"
          [iconName]="widgetType.icon"
          [iconSize]="16"
        >
        </ava-button>
      </div>
    </div>

    <!-- Widget Grid -->
    <div
      class="widget-grid"
      [ngStyle]="gridStyle"
      [class.show-grid]="config.showGrid"
    >
      <div
        *ngFor="let widget of widgets; trackBy: trackByWidget"
        class="widget-container"
        [ngStyle]="getWidgetStyle(widget)"
        [class.selected]="isSelected(widget.id)"
        [class.dragging]="isDragging(widget.id)"
        (click)="selectWidget(widget.id)"
        (keydown.enter)="selectWidget(widget.id)"
        (keydown.space)="selectWidget(widget.id)"
        (mousedown)="startDrag(widget.id)"
        (mouseup)="endDrag()"
        tabindex="0"
        role="button"
        [attr.aria-label]="'Select widget: ' + widget.title"
      >
        <ava-card>
          <div header class="widget-header">
            <div class="widget-title">
              <ava-icon
                [iconName]="getWidgetIcon(widget.type)"
                [iconSize]="16"
                iconColor="#666"
              ></ava-icon>
              <span>{{ widget.title }}</span>
            </div>
            <div *ngIf="config.showWidgetControls" class="widget-controls">
              <ava-button
                *ngIf="isEditMode"
                (click)="onEditWidget(widget.id); $event.stopPropagation()"
                variant="secondary"
                size="small"
                label="Edit"
                iconName="settings"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="onRefreshWidget(widget.id); $event.stopPropagation()"
                variant="secondary"
                size="small"
                label="Refresh"
                iconName="refresh-cw"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                *ngIf="isEditMode"
                (click)="onRemoveWidget(widget.id); $event.stopPropagation()"
                variant="secondary"
                size="small"
                label="Remove"
                iconName="trash"
                [iconSize]="14"
              >
              </ava-button>
            </div>
          </div>
          <div content class="widget-content">
            <div *ngIf="widget.loading" class="widget-loading">
              <ava-icon
                iconName="loader"
                [iconSize]="24"
                iconColor="#007bff"
              ></ava-icon>
              <span>Loading...</span>
            </div>
            <div *ngIf="widget.error" class="widget-error">
              <ava-icon
                iconName="alert-circle"
                [iconSize]="24"
                iconColor="#dc3545"
              ></ava-icon>
              <span>{{ widget.error }}</span>
            </div>
            <div
              *ngIf="!widget.loading && !widget.error"
              class="widget-body"
              [innerHTML]="getWidgetContent(widget)"
            ></div>
          </div>
        </ava-card>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="widgets.length === 0 && !loading" class="empty-state">
      <ava-icon iconName="grid" [iconSize]="64" iconColor="#6c757d"></ava-icon>
      <h3>No Widgets Added</h3>
      <p>Click "Edit Dashboard" to start adding widgets</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="loading-state">
      <ava-icon
        iconName="loader"
        [iconSize]="32"
        iconColor="#007bff"
      ></ava-icon>
      <span>Loading dashboard...</span>
    </div>
  </div>
</div>
