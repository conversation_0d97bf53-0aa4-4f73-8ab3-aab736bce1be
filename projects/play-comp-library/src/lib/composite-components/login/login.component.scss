.login-container {
  --login-bg: #ffffff;
  --login-border: #e9ecef;
  --login-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --login-border-radius: 12px;
  --login-spacing: 24px;
  --login-max-width: 400px;
  --login-transition: all 0.3s ease-in-out;

  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: var(--login-transition);

  // Theme Variations
  &.theme-minimal {
    --login-shadow: none;
    --login-border-radius: 4px;
    --login-spacing: 16px;
    background: #f8f9fa;
  }

  &.theme-modern {
    --login-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    --login-border-radius: 16px;
    --login-spacing: 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.theme-professional {
    --login-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    --login-border-radius: 8px;
    --login-spacing: 20px;
    background: #f5f5f5;
  }

  // Layout Variations
  &.layout-centered {
    justify-content: center;
    align-items: center;
  }

  &.layout-left-aligned {
    justify-content: flex-start;
    align-items: center;
    padding-left: 10%;
  }

  &.layout-split {
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .login-card {
      width: 50%;
      max-width: none;
      border-radius: 0;
      box-shadow: none;
    }
  }

  // States
  &.loading {
    pointer-events: none;
    opacity: 0.8;
  }

  &.disabled {
    pointer-events: none;
    opacity: 0.6;
  }

  // Login Card
  ava-card {
    width: 100%;
    max-width: var(--login-max-width);
    background: var(--login-bg);
    border-radius: var(--login-border-radius);
    box-shadow: var(--login-shadow);
    overflow: hidden;
  }

  // Login Header
  .login-header {
    text-align: center;
    padding: var(--login-spacing);
    border-bottom: 1px solid var(--login-border);
  }

  .logo-section {
    margin-bottom: 16px;

    .logo {
      height: 48px;
      width: auto;
      object-fit: contain;
    }
  }

  .header-content {
    .login-title {
      margin: 0 0 8px 0;
      font-size: 1.75rem;
      font-weight: 700;
      color: #212529;
      line-height: 1.2;
    }

    .login-subtitle {
      margin: 0;
      font-size: 1rem;
      color: #6c757d;
      line-height: 1.5;
    }
  }

  // Login Content
  .login-content {
    padding: var(--login-spacing);
  }

  // Form
  .login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  // Error Message
  .error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    color: #721c24;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--login-transition);

    &:hover {
      background: #f1b0b7;
    }

    .close-icon {
      margin-left: auto;
      cursor: pointer;
    }
  }

  // Form Fields
  .form-field {
    position: relative;

    .password-toggle {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      padding: 4px;
      cursor: pointer;
      border-radius: 4px;
      transition: var(--login-transition);

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  // Form Options
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    margin-top: -8px;
  }

  .remember-me {
    flex: 1;
  }

  .forgot-password {
    flex-shrink: 0;
  }

  // Form Actions
  .form-actions {
    margin-top: 8px;

    .login-button {
      width: 100%;
      height: 48px;
      font-size: 1rem;
      font-weight: 600;
    }
  }

  // Divider
  .login-divider {
    margin: 24px 0;
  }

  // Social Login
  .social-login {
    text-align: center;

    .social-login-text {
      margin: 0 0 16px 0;
      font-size: 0.875rem;
      color: #6c757d;
    }

    .social-providers {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .social-provider-button {
      width: 100%;
      height: 44px;
      border: 1px solid #e9ecef;
      background: #ffffff;
      color: #495057;
      transition: var(--login-transition);

      &:hover {
        background: #f8f9fa;
        border-color: var(--provider-color, #007bff);
        color: var(--provider-color, #007bff);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  // Sign Up Section
  .sign-up-section {
    text-align: center;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--login-border);

    .sign-up-link {
      font-size: 0.875rem;
      color: #6c757d;

      &:hover {
        color: #007bff;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 16px;
    min-height: 100vh;

    &.layout-left-aligned {
      padding-left: 16px;
    }

    &.layout-split {
      flex-direction: column;

      .login-card {
        width: 100%;
        max-width: var(--login-max-width);
      }
    }

    .login-header {
      padding: 20px;
    }

    .login-content {
      padding: 20px;
    }

    .form-options {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .social-providers {
      .social-provider-button {
        height: 48px;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 12px;

    .login-header {
      padding: 16px;
    }

    .login-content {
      padding: 16px;
    }

    .header-content {
      .login-title {
        font-size: 1.5rem;
      }

      .login-subtitle {
        font-size: 0.875rem;
      }
    }

    .form-actions {
      .login-button {
        height: 44px;
        font-size: 0.875rem;
      }
    }
  }

  // Loading State
  &.loading {
    .login-form {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        border-radius: var(--login-border-radius);
        z-index: 1;
      }
    }
  }

  // Focus States for Accessibility
  .password-toggle:focus,
  .error-message:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  // Keyboard Navigation
  .password-toggle,
  .error-message {
    &:focus-visible {
      outline: 2px solid #007bff;
      outline-offset: 2px;
    }
  }

  // Animation for form submission
  .login-form {
    &.submitting {
      animation: form-submit 0.3s ease-in-out;
    }
  }

  @keyframes form-submit {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.98);
    }
    100% {
      transform: scale(1);
    }
  }

  // Custom scrollbar for error messages
  .error-message {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
}
