import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { PopupComponent } from '../../components/popup/popup.component';
import { CommonModule } from '@angular/common';
import { AvaTextareaComponent } from '../../components/textarea/ava-textarea.component';

@Component({
  selector: 'ava-confirmation-popup',
  standalone: true,
  imports: [CommonModule, PopupComponent, AvaTextareaComponent],
  templateUrl: './confirmation-popup.component.html',
  styleUrl: './confirmation-popup.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfirmationPopupComponent {
  @Input() confirmationLabel = 'Yes';
  @Input() title = 'title';
  @Input() message = 'message';
  @Input() show = false;
  @Input() showForm = false; // New input to control form visibility
  @Input() label = 'label';
  @ViewChild('feedbackText') feedbackTextRef!: AvaTextareaComponent;
  @Output() confirmAction = new EventEmitter<string>();
  @Output() cancelAction = new EventEmitter<void>();
  @Output() popupClosed = new EventEmitter<void>();

  handleConfirm(): void {
    // If form is shown, emit the textarea value, otherwise emit empty string
    const feedbackValue =
      this.showForm && this.feedbackTextRef ? this.feedbackTextRef.value : '';
    this.confirmAction.emit(feedbackValue);
  }

  handleCancel(): void {
    this.show = false;
    this.cancelAction.emit();
  }

  handleClose(): void {
    this.popupClosed.emit();
    this.show = false;
  }
}
