<div class="ava-confirmation-popup-container" *ngIf="show">
  <ava-popup
    [show]="show"
    [popupWidth]="'500px'"
    [title]="title"
    [message]="message"
    [showHeaderIcon]="false"
    [showClose]="true"
    [showConfirm]="true"
    [showCancel]="true"
    [confirmButtonLabel]="confirmationLabel"
    (closed)="handleClose()"
    (confirm)="handleConfirm()"
    (cancel)="handleCancel()"
    role="dialog"
    aria-modal="true"
    aria-labelledby="popup-title"
    aria-describedby="popup-message"
    [cancelButtonSize]="'large'"
    [confirmButtonSize]="'large'"
  >
    <div class="popup-form" *ngIf="showForm">
      <ava-textarea
        [label]="label"
        placeholder="Type here..."
        name="feedback"
        #feedbackText
      >
      </ava-textarea>
    </div>
  </ava-popup>
</div>
