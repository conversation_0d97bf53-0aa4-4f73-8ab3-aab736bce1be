import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { LoaderComponent } from '../../components/loader/loader.component';

@Component({
  selector: 'ava-cubical-loading',
  imports: [LoaderComponent],
  templateUrl: './cubical-loading.component.html',
  styleUrl: './cubical-loading.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CubicalLoadingComponent {
  @Input() background: string = 'black'; 
}
