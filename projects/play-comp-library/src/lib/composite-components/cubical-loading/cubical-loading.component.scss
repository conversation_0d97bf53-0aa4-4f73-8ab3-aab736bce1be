.cube {
	font-size: 24px;
	height: 1em;
	width: 1em;
	position: relative;
	transform: rotatex(30deg) rotatey(45deg) ;
	transform-style: preserve-3d;
	animation: cube-spin 1.5s infinite ease-in-out alternate;
	--cube-background: black; // Default fallback
}

.side {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	transform-style: preserve-3d;
	&::before {
		content: "";
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		background: var(--cube-background, black);
		transform: translatez(.5em);
		animation: cube-explode 1.5s infinite ease-in-out;
		opacity: .5;
	}
}

@for $i from 1 through 4 {
	.side:nth-child(#{$i}) {
		transform: rotatey(90deg * $i);
	}
}

.side:nth-child(5) {
	transform: rotatex(90deg);
}

.side:nth-child(6) {
	transform: rotatex(270deg);
}

@keyframes cube-spin {
	0% {
		transform: rotatex(30deg) rotatey(45deg);
	}
	100% {
		transform: rotatex(30deg) rotatey(45deg + 360deg);
	}
}

@keyframes cube-explode {
	0% {
		transform: translatez(.5em);
	}
	50% {
		transform: translatez(.5em * 1.5);
	}
	100% {
		transform: translatez(.5em);
	}
}