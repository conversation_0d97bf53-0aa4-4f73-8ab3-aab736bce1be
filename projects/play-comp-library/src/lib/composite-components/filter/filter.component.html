<div class="ava-filter" [ngClass]="getSizeClasses()">
  <!-- Filter Trigger Button -->
  <button 
    class="filter-trigger"
    [class.active]="isOpen"
    [class.has-filters]="getActiveFiltersCount() > 0"
    [disabled]="disabled"
    (click)="toggleFilterPanel()">
    
    <ava-icon
      iconName="list-filter"
      [iconSize]="getIconSize()"
      [cursor]="false"
      iconColor="#9C27B0">
    </ava-icon>
    
    <span class="filter-text">{{ title }}</span>
    
    <!-- Active Filters Count Badge -->
    <span class="filter-badge" *ngIf="getActiveFiltersCount() > 0">
      {{ getActiveFiltersCount() }}
    </span>
  </button>

  <!-- Filter Panel -->
  <div 
    class="filter-panel"
    [class.open]="isOpen"
    [class.position-right]="position === 'right'"
    [style.max-height]="maxHeight"
    [style.width]="width"
    *ngIf="isOpen">
    
    <!-- Filter Header -->
    <div class="filter-header" *ngIf="showClearAll">
      <h3 class="filter-title">{{ title }}</h3>
      <button 
        class="clear-all-btn"
        (click)="clearAllFilters()"
        [disabled]="getActiveFiltersCount() === 0">
        Clear All
      </button>
    </div>

    <!-- Filter Groups -->
    <div class="filter-content">
      <div 
        class="filter-group" 
        *ngFor="let group of filterGroups">
        
        <h4 class="group-title">{{ group.title }}</h4>
        
        <div class="options-container">
          <div 
            class="filter-option"
            *ngFor="let option of group.options">
            
            <ava-checkbox
              [label]="option.label"
              [isChecked]="option.selected || false"
              [size]="getCheckboxSize()"
              (isCheckedChange)="onOptionChange(group.id, option, $event)">
            </ava-checkbox>
          </div>
        </div>
      </div>
    </div>

    <!-- Filter Footer -->
    <div class="filter-footer" *ngIf="showApplyButton">
      <button 
        class="apply-btn"
        (click)="applyFilters()">
        Apply Filters
      </button>
    </div>
  </div>

  <!-- Backdrop -->
  <div 
    class="filter-backdrop"
    *ngIf="isOpen"
    (click)="toggleFilterPanel()">
  </div>
</div>