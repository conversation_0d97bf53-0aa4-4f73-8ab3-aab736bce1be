.ava-filter {
  position: relative;
  display: inline-block;

  .filter-trigger {
    display: flex;
    align-items: center;
    gap: var(--global-spacing-2);
    background: var(--filter-background-primary);
    border: var(--filter-border);
    border-radius: var(--filter-border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
    outline: none;

    &:hover {
      border-color: var(--filter-border-hover);
      background-color: var(--filter-background-hover);
    }

    &:focus {
      border-color: var(--filter-border-focus);
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }

    &.active {
      border-color: var(--filter-border-focus);
      background-color: var(--filter-background-hover);
    }

    &.has-filters {
      border-color: var(--filter-border-focus);

      .filter-text {
        color: var(--filter-text-active);
        font-weight: 500;
      }
    }

    &:disabled {
      opacity: var(--filter-disabled-opacity);
      cursor: not-allowed;

      &:hover {
        border-color: var(--filter-border);
        background-color: var(--filter-background-primary);
      }
    }

    .filter-text {
      color: var(--filter-text-color);
      font-weight: 400;
      white-space: nowrap;
    }

    .filter-badge {
      background: var(--filter-badge-background);
      color: var(--filter-badge-text);
      font-size: var(--global-spacing-3);
      font-weight: var(--global-font-weight-semibold);
      padding: 2px 6px;
      border-radius: 10px;
      min-width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .filter-panel {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    background: var(--filter-panel-background);
    border: var(--filter-panel-border);
    border-radius: var(--global-spacing-3);
    box-shadow: var(--filter-panel-shadow);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    overflow: hidden;

    &.open {
      opacity: 1;
      transform: translateY(0);
    }

    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: var(--filter-header-border);
      background: var(--filter-header-background);

      .filter-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--filter-text-color);
      }

      .clear-all-btn {
        background: none;
        border: none;
        color: var(--filter-clear-text);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f0f0ff;
        }

        &:disabled {
          color: #9ca3af;
          cursor: not-allowed;

          &:hover {
            background-color: transparent;
          }
        }
      }
    }

    .filter-content {
      overflow-y: auto;
      max-height: inherit;

      .filter-group {
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }

        .group-title {
          margin: 0;
          padding: 16px 20px 8px 20px;
          font-size: 14px;
          font-weight: 600;
          color: #374151;
          border-bottom: none;
        }

        .options-container {
          padding: 0 20px 16px 20px;

          .filter-option {
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }

    .filter-footer {
      padding: 16px 20px;
      border-top: 1px solid #f3f4f6;
      background: #fafafa;

      .apply-btn {
        width: 100%;
        background: var(--filter-apply-background);
        color: var(--filter-apply-text);
        border: none;
        padding: 10px 16px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background: #5b5df1;
        }
      }
    }
  }

  .filter-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
  }

  // Size variants
  &.filter-sm {
    .filter-trigger {
      padding: 8px 12px;
      font-size: 12px;
      gap: 6px;

      .filter-badge {
        font-size: 9px;
        padding: 1px 4px;
        min-width: 14px;
        height: 14px;
      }
    }

    .filter-panel {
      min-width: 200px;

      .filter-header {
        padding: 12px 16px;

        .filter-title {
          font-size: 14px;
        }

        .clear-all-btn {
          font-size: 12px;
        }
      }

      .filter-content {
        .group-title {
          padding: 12px 16px 6px 16px;
          font-size: 12px;
        }

        .options-container {
          padding: 0 16px 12px 16px;
        }
      }
    }
  }

  &.filter-md {
    .filter-trigger {
      padding: 10px 16px;
      font-size: 14px;
      gap: 8px;
    }

    .filter-panel {
      min-width: 250px;
    }
  }

  &.filter-lg {
    .filter-trigger {
      padding: 12px 20px;
      font-size: 16px;
      gap: 10px;

      .filter-badge {
        font-size: 11px;
        padding: 3px 7px;
        min-width: 20px;
        height: 20px;
      }
    }

    .filter-panel {
      min-width: 300px;

      .filter-header {
        padding: 20px 24px;

        .filter-title {
          font-size: 18px;
        }
      }

      .filter-content {
        .group-title {
          padding: 18px 24px 10px 24px;
          font-size: 15px;
        }

        .options-container {
          padding: 0 24px 18px 24px;
        }
      }
    }
  }

  &.filter-xlg {
    .filter-trigger {
      padding: 16px 24px;
      font-size: 18px;
      gap: 12px;

      .filter-badge {
        font-size: 12px;
        padding: 4px 8px;
        min-width: 22px;
        height: 22px;
      }
    }

    .filter-panel {
      min-width: 350px;

      .filter-header {
        padding: 24px 28px;

        .filter-title {
          font-size: 20px;
        }

        .clear-all-btn {
          font-size: 16px;
        }
      }

      .filter-content {
        .group-title {
          padding: 20px 28px 12px 28px;
          font-size: 16px;
        }

        .options-container {
          padding: 0 28px 20px 28px;

          .filter-option {
            margin-bottom: 12px;
          }
        }
      }

      .filter-footer {
        padding: 20px 28px;

        .apply-btn {
          padding: 12px 20px;
          font-size: 16px;
        }
      }
    }
  }
}