import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DefaultCardComponent } from '../../components/card/default-card/default-card.component';
import { AvaTagComponent } from '../../components/tags/tags.component';
import { IconComponent } from '../../components/icon/icon.component';
import { AvatarsComponent } from '../../components/avatars/avatars.component';
import { DividersComponent } from '../../components/dividers/dividers.component';
import { ButtonComponent } from '../../components/button/button.component';

export interface ImageCardTag {
  label: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' | 'custom';
  shape?: 'pill' | 'square';
}

export interface ImageCardAvatar {
  url: string;
  size?: 'ultra-small' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large' | 'ultra-large';
  text: string;
}

export interface ImageCardSubText {
  icon: {
    name: string;
    size?: number;
    color?: string;
  };
  text: string;
}

export interface ImageCardDivider {
  variant?: 'solid' | 'dashed' | 'dotted';
  color?: string;
}

export interface ImageCardSubDescription {
  left: string;
  right: string;
}

export interface ImageCardButton {
  text: string;
  action?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info' | 'purple' | 'emerald';
  size?: 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge';
  width?: string;
  height?: string;
}

export interface ImageCardLayout {
  orientation: 'vertical' | 'horizontal';
  imageGrid: 'top' | 'bottom' | 'left' | 'right';
  infoGrid: 'top' | 'bottom' | 'left' | 'right';
}

export interface ImageCardData {
  variant: 'withActions' | 'withoutActions';
  title: string;
  image: string;
  divider?: ImageCardDivider;
  avatar?: ImageCardAvatar;
  subText?: ImageCardSubText;
  tags?: ImageCardTag[];
  subDescription?: ImageCardSubDescription;
  description?: string;
  buttons?: ImageCardButton[];
  layout: ImageCardLayout;
}

@Component({
  selector: 'ava-image-card',
  standalone: true,
  imports: [
    CommonModule,
    DefaultCardComponent,
    AvaTagComponent,
    IconComponent,
    AvatarsComponent,
    DividersComponent,
    ButtonComponent
  ],
  templateUrl: './image-card.component.html',
  styleUrl: './image-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ImageCardComponent {
  @Input() data: ImageCardData = {
    variant: 'withActions',
    title: '',
    image: '',
    layout: {
      orientation: 'vertical',
      imageGrid: 'top',
      infoGrid: 'bottom'
    }
  };

  @Output() cardClick = new EventEmitter<void>();
  @Output() buttonClicked = new EventEmitter<{ action: string | undefined, button: ImageCardButton }>();

  onCardClick() {
    this.cardClick.emit();
  }

  onButtonClick(action: string | undefined, button: ImageCardButton) {
    this.buttonClicked.emit({ action, button });
  }

  get isVertical(): boolean {
    return this.data.layout.orientation === 'vertical';
  }

  get isHorizontal(): boolean {
    return this.data.layout.orientation === 'horizontal';
  }

  get isWithActions(): boolean {
    return this.data.variant === 'withActions';
  }

  get isWithoutActions(): boolean {
    return this.data.variant === 'withoutActions';
  }

  get maxWidth(): string {
    if (this.isWithActions) {
      return this.isVertical ? '320px' : '487px'; // 160px image + 24px gap + 327px content
    } else {
      return this.isVertical ? '320px' : '564px'; // 180px image + 24px gap + 360px content
    }
  }

  get imageWidth(): string {
    if (this.isWithActions) {
      return this.isVertical ? '100%' : '160px';
    } else {
      return this.isVertical ? '100%' : '180px';
    }
  }

  get contentWidth(): string {
    if (this.isWithActions) {
      return this.isVertical ? '100%' : '327px';
    } else {
      return this.isVertical ? '100%' : '360px';
    }
  }
}
