// image-card.component.spec.ts
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ImageCardComponent } from './image-card.component';
import { CardComponent } from '../../components/card/card.component';
import { By } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';

describe('ImageCardComponent', () => {
  let component: ImageCardComponent;
  let fixture: ComponentFixture<ImageCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ImageCardComponent, CardComponent, CommonModule]
    }).compileComponents();

    fixture = TestBed.createComponent(ImageCardComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should use vertical wrapper class when imagePosition is top', () => {
    component.imagePosition = 'top';
    expect(component.getWrapperClass()).toBe('img-card-wrapper-vertical');
  });

  it('should use vertical wrapper class when imagePosition is bottom', () => {
    component.imagePosition = 'bottom';
    expect(component.getWrapperClass()).toBe('img-card-wrapper-vertical');
  });

  it('should use horizontal wrapper class for left', () => {
    component.imagePosition = 'left';
    expect(component.getWrapperClass()).toBe('img-card-wrapper-horizontal');
  });

  it('should use horizontal wrapper class for right', () => {
    component.imagePosition = 'right';
    expect(component.getWrapperClass()).toBe('img-card-wrapper-horizontal');
  });

  it('should render image and text in correct order when position is left', () => {
    component.imageUrl = 'test.jpg';
    component.title = 'Test Title';
    component.name = 'John';
    component.imagePosition = 'left';
    fixture.detectChanges();

    const img = fixture.debugElement.query(By.css('.left-wrapper img'));
    const title = fixture.debugElement.query(By.css('.right-wrapper p'));
    const name = fixture.debugElement.query(By.css('.right-wrapper span'));

    expect(img.nativeElement.src).toContain('test.jpg');
    expect(title.nativeElement.textContent).toContain('Test Title');
    expect(name.nativeElement.textContent).toContain('John');
  });

  it('should render image and text in correct order when position is right', () => {
    component.imageUrl = 'test.jpg';
    component.title = 'Test Title';
    component.name = 'Jane';
    component.imagePosition = 'right';
    fixture.detectChanges();

    const img = fixture.debugElement.query(By.css('.left-wrapper img'));
    const title = fixture.debugElement.query(By.css('.right-wrapper p'));
    const name = fixture.debugElement.query(By.css('.right-wrapper span'));

    expect(img.nativeElement.src).toContain('test.jpg');
    expect(title.nativeElement.textContent).toContain('Test Title');
    expect(name.nativeElement.textContent).toContain('Jane');
  });

  it('should render image and text in vertical layout for top position', () => {
    component.imageUrl = 'test.jpg';
    component.title = 'Top Title';
    component.name = 'Top Name';
    component.imagePosition = 'top';
    fixture.detectChanges();

    const img = fixture.debugElement.query(By.css('.top-wrapper img'));
    const title = fixture.debugElement.query(By.css('.bottom-wrapper p'));
    const name = fixture.debugElement.query(By.css('.bottom-wrapper span'));

    expect(img.nativeElement.src).toContain('test.jpg');
    expect(title.nativeElement.textContent).toContain('Top Title');
    expect(name.nativeElement.textContent).toContain('Top Name');
  });

  it('should render image and text in vertical layout for bottom position', () => {
    component.imageUrl = 'test.jpg';
    component.title = 'Bottom Title';
    component.name = 'Bottom Name';
    component.imagePosition = 'bottom';
    fixture.detectChanges();

    const img = fixture.debugElement.query(By.css('.top-wrapper img'));
    const title = fixture.debugElement.query(By.css('.bottom-wrapper p'));
    const name = fixture.debugElement.query(By.css('.bottom-wrapper span'));

    expect(img.nativeElement.src).toContain('test.jpg');
    expect(title.nativeElement.textContent).toContain('Bottom Title');
    expect(name.nativeElement.textContent).toContain('Bottom Name');
  });
});
