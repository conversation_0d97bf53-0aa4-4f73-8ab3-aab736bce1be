.ava-image-card-wrapper {
    display: block;
    box-sizing: border-box;
    width: 100%;

    ava-default-card {
        width: 100%;

        .ava-default-card {
            padding: 0;
            margin: 0;
        }
    }

    // Vertical Layout
    &.vertical {
        .vertical-layout {
            display: flex;
            flex-direction: column;

            .image-section {
                width: 100%;
                overflow: hidden;
                border-radius: 12px;

                img {
                    width: 100%;
                    height: 200px;
                    object-fit: cover;
                    border-radius: 12px;
                    display: block;
                }
            }

            .content-section {
                display: flex;
                flex-direction: column;
            }
        }

        // Without Actions variant max height
        &.without-actions {
            max-height: 340px;
        }
    }

    // Horizontal Layout
    &.horizontal {
        .horizontal-layout {
            display: flex;
            flex-direction: row;
            gap: 24px;
            min-height: 200px;
            align-items: stretch;

            .image-section {
                flex: 0 0 auto;
                display: flex;
                align-self: stretch;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 12px;
                }
            }

            .content-section {
                display: flex;
                flex-direction: column;
                flex: 1;
            }
        }

        // With Actions variant
        &.with-actions {
            .horizontal-layout {
                .content-section {
                    min-height: 200px;
                }

                .image-section img {
                    min-height: 200px;
                }
            }
        }

        // Without Actions variant
        &.without-actions {
            .horizontal-layout {
                .content-section {
                    padding-bottom: 12px;
                }
            }
        }
    }

    // Common styles for both layouts
    .top-section {
        .tag-icon-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .left-tags {
                display: flex;
                gap: 8px;
                flex-wrap: wrap;
            }

            .right-icon {
                display: flex;
                align-items: center;
            }
        }
    }

    .title {
        overflow: hidden;
        color: #3B3F46;
        text-overflow: ellipsis;
        white-space: nowrap;

        /* Desktop/Heading/H4 */
        font-family: Mulish;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 28px;
        /* 116.667% */

        // Truncate after 1st line
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal;
    }

    .description {
        overflow: hidden;
        color: var(--Brand-Neutral-n-800, #3B3F46);
        text-overflow: ellipsis;

        font-family: var(--Global-v1-Family-Body, Inter);
        font-size: var(--Global-v1-Size-16, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: var(--Global-v1-Line-height-20, 20px);
        /* 125% */

        // Truncate after 2 lines
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal;
    }

    .divider-section {
        margin-bottom: 0;

        ava-dividers {
            margin-bottom: 0;
        }
    }

    .sub-description {
        .sub-desc-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .sub-desc-left {
                color: #858AAD;
                font-family: Mulish;
                font-size: 12px;
                font-style: normal;
                font-weight: 600;
                line-height: 150%;
                /* 18px */

                // Truncate after 145px
                max-width: 145px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .sub-desc-right {
                color: #666D99;
                text-align: center;
                font-family: Mulish;
                font-size: 12px;
                font-style: normal;
                font-weight: 600;
                line-height: 150%;
                /* 18px */

                // Truncate after 100px
                max-width: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .avatar-subtext-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .avatar-text {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;

            ava-avatars {
                padding: 0;
                margin: 0;
                display: flex;
                align-items: center;

                .avatar-wrapper {
                    padding: 0;
                    display: flex;
                    align-items: center;
                    gap: 0;
                }
            }

            .avatar-name {
                font-family: Inter;
                font-size: 14px;
                font-weight: 400;
                color: #3B3F46;
                line-height: 1;
                display: flex;
                align-items: center;

                // Truncate after 150px
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .subtext {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 8px;
            flex-shrink: 0;

            ava-icon {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .subtext-label {
                font-family: Inter;
                font-size: 14px;
                font-weight: 400;
                color: #3B3F46;
                text-align: center;

                // Truncate after 50px
                max-width: 50px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .buttons-section {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
        width: 100%;

        ava-button {
            display: inline-flex;
            flex: 1;
            max-width: calc(50% - 6px);
        }
    }
}