<div class="ava-image-card-wrapper" [ngClass]="{
       'vertical': isVertical,
       'horizontal': isHorizontal,
       'with-actions': isWithActions,
       'without-actions': isWithoutActions
     }" [style.max-width]="maxWidth" role="region" aria-labelledby="cardTitle" (click)="onCardClick()">

  <ava-default-card>

    <!-- Vertical Layout -->
    <div *ngIf="isVertical" class="vertical-layout">

      <!-- Image Top (for withActions or withoutActions with imageGrid: 'top') -->
      <div
        *ngIf="(isWithActions && data.layout.imageGrid === 'top') || (isWithoutActions && data.layout.imageGrid === 'top')"
        class="image-section">
        <img [src]="data.image" [alt]="data.title" [style.width]="imageWidth" [style.height]="'200px'">
      </div>

      <!-- Without Actions Variant - Top Section (tags and icon) -->
      <div *ngIf="isWithoutActions" class="top-section"
        [style.margin-top]="data.layout.imageGrid === 'top' ? '24px' : '0'">
        <div class="tag-icon-row">
          <div class="left-tags">
            <ava-tag *ngFor="let tag of data.tags" [label]="tag.label" [size]="tag.size || 'xs'"
              [color]="tag.color || 'primary'" [pill]="tag.shape === 'pill'">
            </ava-tag>
          </div>
          <div class="right-icon">
            <ava-icon iconName="more-vertical" [iconSize]="24" iconColor="#000000">
            </ava-icon>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="content-section"
        [style.margin-top]="(isWithActions && data.layout.imageGrid === 'top') ? '24px' : (isWithoutActions ? '24px' : '0')">

        <!-- Title -->
        <div class="title" [style.margin-bottom]="'12px'">
          {{ data.title }}
        </div>

        <!-- Description -->
        <div *ngIf="data.description" class="description" [style.margin-bottom]="'12px'">
          {{ data.description }}
        </div>

        <!-- Sub Description (Without Actions only) -->
        <div *ngIf="isWithoutActions && data.subDescription" class="sub-description" [style.margin-bottom]="'12px'">
          <div class="sub-desc-row">
            <div class="sub-desc-left">{{ data.subDescription.left }}</div>
            <div class="sub-desc-right">{{ data.subDescription.right }}</div>
          </div>
        </div>

        <!-- Divider (With Actions only) -->
        <div *ngIf="isWithActions" class="divider-section">
          <ava-dividers [variant]="data.divider?.variant || 'solid'" [color]="data.divider?.color || '#E5E7EB'">
          </ava-dividers>
        </div>

        <!-- Avatar and SubText Row (With Actions only) -->
        <div *ngIf="isWithActions" class="avatar-subtext-row" [style.margin-bottom]="'12px'">
          <div class="avatar-text" *ngIf="data.avatar">
            <ava-avatars [imageUrl]="data.avatar.url" [size]="data.avatar.size || 'medium'" shape="pill">
            </ava-avatars>
            <span class="avatar-name">{{ data.avatar.text }}</span>
          </div>

          <div class="subtext" *ngIf="data.subText">
            <ava-icon [iconName]="data.subText.icon.name" [iconSize]="data.subText.icon.size || 14"
              [iconColor]="data.subText.icon.color || '#BBBEC5'">
            </ava-icon>
            <span class="subtext-label">{{ data.subText.text }}</span>
          </div>
        </div>

        <!-- Buttons (With Actions only) -->
        <div *ngIf="isWithActions && data.buttons && data.buttons.length > 0" class="buttons-section">
          <ava-button *ngFor="let button of data.buttons" [label]="button.text" [variant]="button.variant || 'primary'"
            [size]="button.size || 'medium'" [style.width]="button.width || 'auto'"
            [style.height]="button.height || 'auto'" (userClick)="onButtonClick(button.action, button)">
          </ava-button>
        </div>
      </div>

      <!-- Image Bottom (for withoutActions with imageGrid: 'bottom') -->
      <div *ngIf="isWithoutActions && data.layout.imageGrid === 'bottom'" class="image-section"
        [style.margin-top]="'24px'">
        <img [src]="data.image" [alt]="data.title" [style.width]="imageWidth" [style.height]="'200px'">
      </div>
    </div>

    <!-- Horizontal Layout -->
    <div *ngIf="isHorizontal" class="horizontal-layout">

      <!-- Image Left -->
      <div *ngIf="data.layout.imageGrid === 'left'" class="image-section" [style.width]="imageWidth">
        <img [src]="data.image" [alt]="data.title" [style.width]="imageWidth" [style.height]="'100%'">
      </div>

      <!-- Content Section -->
      <div class="content-section" [style.width]="contentWidth">

        <!-- Without Actions Variant - Top Section -->
        <div *ngIf="isWithoutActions" class="top-section" [style.margin-bottom]="'24px'">
          <div class="tag-icon-row">
            <div class="left-tags">
              <ava-tag *ngFor="let tag of data.tags" [label]="tag.label" [size]="tag.size || 'xs'"
                [color]="tag.color || 'primary'" [pill]="tag.shape === 'pill'">
              </ava-tag>
            </div>
            <div class="right-icon">
              <ava-icon iconName="more-vertical" [iconSize]="24" iconColor="#000000">
              </ava-icon>
            </div>
          </div>
        </div>

        <!-- Title -->
        <div class="title" [style.margin-bottom]="'12px'">
          {{ data.title }}
        </div>

        <!-- Description -->
        <div *ngIf="data.description" class="description" [style.margin-bottom]="'12px'">
          {{ data.description }}
        </div>

        <!-- Sub Description (Without Actions only) -->
        <div *ngIf="isWithoutActions && data.subDescription" class="sub-description" [style.margin-bottom]="'12px'">
          <div class="sub-desc-row">
            <div class="sub-desc-left">{{ data.subDescription.left }}</div>
            <div class="sub-desc-right">{{ data.subDescription.right }}</div>
          </div>
        </div>

        <!-- Divider (With Actions only) -->
        <div *ngIf="isWithActions" class="divider-section">
          <ava-dividers [variant]="data.divider?.variant || 'solid'" [color]="data.divider?.color || '#E5E7EB'">
          </ava-dividers>
        </div>

        <!-- Avatar and SubText Row (With Actions only) -->
        <div *ngIf="isWithActions" class="avatar-subtext-row" [style.margin-bottom]="'12px'">
          <div class="avatar-text" *ngIf="data.avatar">
            <ava-avatars [imageUrl]="data.avatar.url" [size]="data.avatar.size || 'medium'" shape="pill">
            </ava-avatars>
            <span class="avatar-name">{{ data.avatar.text }}</span>
          </div>

          <div class="subtext" *ngIf="data.subText">
            <ava-icon [iconName]="data.subText.icon.name" [iconSize]="data.subText.icon.size || 14"
              [iconColor]="data.subText.icon.color || '#BBBEC5'">
            </ava-icon>
            <span class="subtext-label">{{ data.subText.text }}</span>
          </div>
        </div>

        <!-- Buttons (With Actions only) -->
        <div *ngIf="isWithActions && data.buttons && data.buttons.length > 0" class="buttons-section">
          <ava-button *ngFor="let button of data.buttons" [label]="button.text" [variant]="button.variant || 'primary'"
            [size]="button.size || 'medium'" [style.width]="button.width || 'auto'"
            [style.height]="button.height || 'auto'" (userClick)="onButtonClick(button.action, button)">
          </ava-button>
        </div>
      </div>

      <!-- Image Right -->
      <div *ngIf="data.layout.imageGrid === 'right'" class="image-section" [style.width]="imageWidth">
        <img [src]="data.image" [alt]="data.title" [style.width]="imageWidth" [style.height]="'100%'">
      </div>
    </div>

  </ava-default-card>
</div>