.custom-data-grid-wrapper {
  width: 100%;

  .account-number-cell,
  .account-name-cell {
    cursor: pointer;
    transition: color 0.2s ease;
    
    &:hover {
      color: var(--grid-background-color-odd);
      text-decoration: underline;
    }

    &:focus {
      outline: 2px solid var(--grid-background-color-odd);
      outline-offset: 2px;
      border-radius: 2px;
    }
  }

  .amount-cell {
    font-weight: 500;
    text-align: right;
  }

  .actions-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .delete-icon {
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
        opacity: 0.8;
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    padding: 1rem;
    
    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      text-align: center;
      
      .empty-message {
        margin: 0;
        color: var(--grid-header-background-color);
        font-size: 1rem;
      }
    }
  }
}
