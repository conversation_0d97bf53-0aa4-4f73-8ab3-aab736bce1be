import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../../components/card/card.component';
import { AvaAutocompleteComponent } from '../../components/autocomplete/ava-autocomplete.component';
import { DropdownComponent } from '../../components/dropdown/dropdown.component';
import { ButtonComponent } from '../../components/button/button.component';
import { IconComponent } from '../../components/icon/icon.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';

export interface FilterOption {
  id: string;
  label: string;
  value: string;
  type: 'dropdown' | 'autocomplete' | 'date' | 'range';
  options?: { name: string; value: string }[];
  autocompleteOptions?: { label: string; value: string }[];
  placeholder?: string;
  multiple?: boolean;
}

export interface ActionButton {
  id: string;
  label: string;
  icon?: string;
  variant?:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'info'
    | 'purple'
    | 'emerald';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

export interface SearchFilterConfig {
  searchPlaceholder?: string;
  filters: FilterOption[];
  actions: ActionButton[];
  showClearAll?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export interface SearchFilterEvent {
  searchTerm: string;
  filters: Record<string, string | string[]>;
  actionId?: string;
}

@Component({
  selector: 'ava-search-filter-panel',
  standalone: true,
  imports: [
    CommonModule,
    CardComponent,
    AvaAutocompleteComponent,
    DropdownComponent,
    ButtonComponent,
    IconComponent,
    AvaTextboxComponent,
  ],
  templateUrl: './search-filter-panel.component.html',
  styleUrl: './search-filter-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class SearchFilterPanelComponent {
  @Input() config: SearchFilterConfig = {
    searchPlaceholder: 'Search...',
    filters: [],
    actions: [],
    showClearAll: true,
    collapsible: false,
    defaultCollapsed: false,
  };

  @Input() loading = false;
  @Input() disabled = false;

  @Output() searchChange = new EventEmitter<string>();
  @Output() filterChange = new EventEmitter<
    Record<string, string | string[]>
  >();
  @Output() actionClick = new EventEmitter<string>();
  @Output() clearAll = new EventEmitter<void>();
  @Output() searchFilterChange = new EventEmitter<SearchFilterEvent>();

  searchTerm = '';
  filterValues: Record<string, string | string[]> = {};
  isCollapsed = false;

  ngOnInit() {
    this.isCollapsed = this.config.defaultCollapsed || false;
    this.initializeFilterValues();
  }

  private initializeFilterValues() {
    this.config.filters.forEach((filter) => {
      this.filterValues[filter.id] = filter.multiple ? [] : '';
    });
  }

  onSearchChange(term: string | string[]) {
    this.searchTerm = Array.isArray(term) ? term[0] || '' : term;
    this.searchChange.emit(this.searchTerm);
    this.emitSearchFilterEvent();
  }

  onFilterChange(filterId: string, value: string | string[]) {
    this.filterValues[filterId] = value;
    this.filterChange.emit({ [filterId]: value });
    this.emitSearchFilterEvent();
  }

  onActionClick(actionId: string) {
    this.actionClick.emit(actionId);
    this.emitSearchFilterEvent(actionId);
  }

  onClearAll() {
    this.searchTerm = '';
    this.initializeFilterValues();
    this.clearAll.emit();
    this.emitSearchFilterEvent();
  }

  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
  }

  private emitSearchFilterEvent(actionId?: string) {
    this.searchFilterChange.emit({
      searchTerm: this.searchTerm,
      filters: this.filterValues,
      actionId,
    });
  }

  getActiveFiltersCount(): number {
    let count = 0;
    if (this.searchTerm) count++;

    Object.values(this.filterValues).forEach((value) => {
      if (Array.isArray(value)) {
        if (value.length > 0) count++;
      } else {
        if (value) count++;
      }
    });

    return count;
  }

  hasActiveFilters(): boolean {
    return this.getActiveFiltersCount() > 0;
  }
}
