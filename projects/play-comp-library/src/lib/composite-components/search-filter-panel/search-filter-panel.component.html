<ava-card>
  <div header class="search-filter-header">
    <div class="header-content">
      <div class="header-actions">
        <span *ngIf="hasActiveFilters()" class="active-filters-badge">
          {{ getActiveFiltersCount() }} active
        </span>
        <button
          *ngIf="config.collapsible"
          (click)="toggleCollapse()"
          class="collapse-button"
          [attr.aria-expanded]="!isCollapsed"
          aria-label="Toggle filters"
        >
          <ava-icon
            [iconName]="isCollapsed ? 'chevron-down' : 'chevron-up'"
            [iconSize]="16"
          >
          </ava-icon>
        </button>
      </div>
    </div>
  </div>

  <div content class="search-filter-content" [class.collapsed]="isCollapsed">
    <!-- Search Section -->
    <div class="search-section">
      <ava-autocomplete
        [placeholder]="config.searchPlaceholder || 'Search...'"
        [options]="[]"
        [loading]="loading"
        [disabled]="disabled"
        (valueChange)="onSearchChange($event)"
        startIcon="search"
        startIconColor="#666"
      >
      </ava-autocomplete>
    </div>

    <!-- Filters Section -->
    <div *ngIf="config.filters.length > 0" class="filters-section">
      <div class="filters-grid">
        <div
          *ngFor="let filter of config.filters"
          class="filter-item"
          [class.multiple]="filter.multiple"
        >
          <span class="filter-label">{{ filter.label }}</span>

          <!-- Dropdown Filter -->
          <ava-dropdown
            *ngIf="filter.type === 'dropdown'"
            [options]="filter.options || []"
            [dropdownTitle]="filter.placeholder || 'Select...'"
            [disabled]="disabled"
            (selectionChange)="onFilterChange(filter.id, $event)"
          >
          </ava-dropdown>

          <!-- Autocomplete Filter -->
          <ava-autocomplete
            *ngIf="filter.type === 'autocomplete'"
            [options]="filter.autocompleteOptions || []"
            [placeholder]="filter.placeholder || 'Type to search...'"
            [multi]="filter.multiple || false"
            [loading]="loading"
            [disabled]="disabled"
            (valueChange)="onFilterChange(filter.id, $event)"
          >
          </ava-autocomplete>

          <!-- Date Filter (placeholder for future implementation) -->
          <div *ngIf="filter.type === 'date'" class="date-filter-placeholder">
            <ava-textbox
              [placeholder]="filter.placeholder || 'Select date...'"
              [disabled]="disabled"
              startIcon="calendar"
            >
            </ava-textbox>
          </div>

          <!-- Range Filter (placeholder for future implementation) -->
          <div *ngIf="filter.type === 'range'" class="range-filter-placeholder">
            <ava-textbox
              [placeholder]="filter.placeholder || 'Enter range...'"
              [disabled]="disabled"
              startIcon="sliders"
            >
            </ava-textbox>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions Section -->
    <div *ngIf="config.actions.length > 0" class="actions-section">
      <div class="actions-grid">
        <ava-button
          *ngFor="let action of config.actions"
          [variant]="action.variant || 'primary'"
          [size]="action.size || 'medium'"
          [disabled]="action.disabled || disabled"
          [label]="action.label"
          [iconName]="action.icon || ''"
          [iconSize]="16"
          (click)="onActionClick(action.id)"
        >
        </ava-button>
      </div>
    </div>

    <!-- Clear All Section -->
    <div
      *ngIf="config.showClearAll && hasActiveFilters()"
      class="clear-section"
    >
      <ava-button
        variant="secondary"
        size="small"
        label="Clear All Filters"
        iconName="x"
        [iconSize]="14"
        (click)="onClearAll()"
      >
      </ava-button>
    </div>
  </div>
</ava-card>
