.search-filter-header {
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .panel-title {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1rem;

      .active-filters-badge {
        background-color: #007bff;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .collapse-button {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 4px;
        color: #666;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
          color: #333;
        }

        &:focus {
          outline: 2px solid #007bff;
          outline-offset: 2px;
        }
      }
    }
  }
}

.search-filter-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  transition: all 0.3s ease;

  &.collapsed {
    display: none;
  }

  .search-section {
    margin-top: 1rem;
    width: 100%;
  }

  .filters-section {
    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;

      .filter-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .filter-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #555;
          margin-bottom: 0.25rem;
        }

        &.multiple {
          .filter-label::after {
            content: " (Multiple)";
            font-size: 0.75rem;
            color: #666;
            font-weight: normal;
          }
        }
      }
    }
  }

  .actions-section {
    .actions-grid {
      display: flex;
      gap: 0.75rem;
      flex-wrap: wrap;
      align-items: center;
    }
  }

  .clear-section {
    display: flex;
    justify-content: flex-end;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .search-filter-content {
    .filters-section {
      .filters-grid {
        grid-template-columns: 1fr;
      }
    }

    .actions-section {
      .actions-grid {
        flex-direction: column;
        align-items: stretch;

        ava-button {
          width: 100%;
        }
      }
    }
  }
}

// Loading state
.search-filter-content.loading {
  opacity: 0.7;
  pointer-events: none;
}

// Disabled state
.search-filter-content.disabled {
  opacity: 0.5;
  pointer-events: none;
}
