import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../../components/card/card.component';
import { ButtonComponent } from '../../components/button/button.component';
import { IconComponent } from '../../components/icon/icon.component';
import { AvaTextboxComponent } from '../../components/textbox/ava-textbox.component';
import { AvaTextareaComponent } from '../../components/textarea/ava-textarea.component';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { RadioButtonComponent } from '../../components/radio-button/radio-button.component';
import { DropdownComponent } from '../../components/dropdown/dropdown.component';
import {
  AvaStepperComponent,
  StepperStep,
} from '../../components/stepper/stepper.component';

export interface FormStep {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  required?: boolean;
  completed?: boolean;
  disabled?: boolean;
  validationRules?: ValidationRule[];
  fields: FormField[];
}

export interface FormField {
  id: string;
  type:
    | 'text'
    | 'email'
    | 'password'
    | 'number'
    | 'textarea'
    | 'select'
    | 'checkbox'
    | 'radio'
    | 'date'
    | 'file';
  label: string;
  placeholder?: string;
  value?: unknown;
  required?: boolean;
  disabled?: boolean;
  validationRules?: ValidationRule[];
  options?: { value: string; label: string; name?: string }[];
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  errorMessage?: string;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
  message: string;
  validator?: (value: unknown) => boolean;
}

export interface WizardConfig {
  title?: string;
  description?: string;
  showProgress?: boolean;
  showStepNumbers?: boolean;
  allowStepNavigation?: boolean;
  validateOnStepChange?: boolean;
  autoSave?: boolean;
  saveInterval?: number;
  theme?: 'default' | 'minimal' | 'modern';
  stepLayout?: 'horizontal' | 'vertical' | 'tabs';
  buttonPosition?: 'bottom' | 'top' | 'both';
  showCancelButton?: boolean;
  showResetButton?: boolean;
  confirmBeforeExit?: boolean;
}

export interface WizardEvent {
  type:
    | 'step-change'
    | 'step-complete'
    | 'step-validate'
    | 'form-submit'
    | 'form-reset'
    | 'form-save'
    | 'form-cancel';
  data: unknown;
  stepId?: string;
  currentStep?: number;
  totalSteps?: number;
}

@Component({
  selector: 'ava-multi-step-form-wizard',
  standalone: true,
  imports: [
    CommonModule,
    CardComponent,
    ButtonComponent,
    IconComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    CheckboxComponent,
    RadioButtonComponent,
    DropdownComponent,
    AvaStepperComponent,
  ],
  templateUrl: './multi-step-form-wizard.component.html',
  styleUrl: './multi-step-form-wizard.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class MultiStepFormWizardComponent {
  @Input() config: WizardConfig = {
    title: 'Multi-Step Form',
    description: 'Complete the form by following the steps below',
    showProgress: true,
    showStepNumbers: true,
    allowStepNavigation: true,
    validateOnStepChange: true,
    autoSave: false,
    saveInterval: 30000,
    theme: 'default',
    stepLayout: 'horizontal',
    buttonPosition: 'bottom',
    showCancelButton: true,
    showResetButton: true,
    confirmBeforeExit: true,
  };

  @Input() steps: FormStep[] = [];
  @Input() loading = false;
  @Input() disabled = false;

  @Output() stepChange = new EventEmitter<{
    stepId: string;
    stepIndex: number;
    direction: 'next' | 'prev';
  }>();
  @Output() stepComplete = new EventEmitter<{
    stepId: string;
    stepIndex: number;
    data: Record<string, unknown>;
  }>();
  @Output() stepValidate = new EventEmitter<{
    stepId: string;
    stepIndex: number;
    isValid: boolean;
    errors: string[];
  }>();
  @Output() formSubmit = new EventEmitter<Record<string, unknown>>();
  @Output() formReset = new EventEmitter<void>();
  @Output() formSave = new EventEmitter<Record<string, unknown>>();
  @Output() formCancel = new EventEmitter<void>();
  @Output() wizardEvent = new EventEmitter<WizardEvent>();

  currentStepIndex = 0;
  formData: Record<string, unknown> = {};
  validationErrors: Record<string, string[]> = {};
  isSubmitting = false;
  autoSaveTimer: number | null = null;
  passwordVisibility: Record<string, boolean> = {};

  constructor(private cdr: ChangeDetectorRef) {}

  get currentStep(): FormStep | null {
    return this.steps[this.currentStepIndex] || null;
  }

  get totalSteps(): number {
    return this.steps.length;
  }

  get progressPercentage(): number {
    return this.totalSteps > 0
      ? ((this.currentStepIndex + 1) / this.totalSteps) * 100
      : 0;
  }

  get canGoNext(): boolean {
    if (this.currentStepIndex >= this.totalSteps - 1) return false;
    if (this.disabled) return false;
    // Always show the Next button, validation will be handled in onNextStep
    return true;
  }

  get canGoPrev(): boolean {
    return this.currentStepIndex > 0 && !this.disabled;
  }

  get canSubmit(): boolean {
    return (
      this.currentStepIndex === this.totalSteps - 1 &&
      this.isCurrentStepValid() &&
      !this.disabled
    );
  }

  get isStepCompleted(): boolean {
    return this.currentStep?.completed || false;
  }

  get stepperSteps(): StepperStep[] {
    return this.steps.map((step) => ({
      label: step.title,
      iconName: step.icon,
    }));
  }

  get disabledStepIndices(): number[] {
    return this.steps
      .map((step, index) => (step.disabled ? index : -1))
      .filter((index) => index !== -1);
  }

  ngOnInit() {
    this.initializeFormData();
    this.startAutoSave();
  }

  ngOnDestroy() {
    this.stopAutoSave();
  }

  onNextStep() {
    console.log('onNextStep called - canGoNext:', this.canGoNext);

    if (!this.canGoNext) {
      console.log('Cannot go next - returning early');
      return;
    }

    console.log('validateOnStepChange:', this.config.validateOnStepChange);
    console.log('isCurrentStepValid:', this.isCurrentStepValid());

    // Validate the current step
    this.validateCurrentStep();

    // Check if validation passed
    if (!this.isCurrentStepValid()) {
      console.log('Validation failed - cannot proceed to next step');
      return;
    }

    console.log('Proceeding to next step');
    this.completeCurrentStep();
    this.currentStepIndex++;

    // Ensure the stepper updates immediately
    console.log('Moving to next step:', this.currentStepIndex);

    // Force change detection to update the stepper immediately
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 0);

    this.emitStepChange('next');
  }

  onPrevStep() {
    if (!this.canGoPrev) return;

    this.currentStepIndex--;

    // Ensure the stepper updates immediately
    console.log('Moving to previous step:', this.currentStepIndex);

    // Force change detection to update the stepper immediately
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 0);

    this.emitStepChange('prev');
  }

  onStepClick(stepIndex: number) {
    if (!this.config.allowStepNavigation || this.disabled) return;

    if (stepIndex < this.currentStepIndex) {
      // Going back is always allowed
      this.currentStepIndex = stepIndex;
      this.emitStepChange('prev');
    } else if (stepIndex > this.currentStepIndex) {
      // Going forward requires validation
      if (this.config.validateOnStepChange && !this.isCurrentStepValid()) {
        this.validateCurrentStep();
        return;
      }
      this.completeCurrentStep();
      this.currentStepIndex = stepIndex;
      this.emitStepChange('next');
    }
  }

  onStepperStepChange(stepIndex: number) {
    this.onStepClick(stepIndex);
  }

  onSubmit() {
    if (!this.canSubmit) return;

    this.isSubmitting = true;
    this.validateCurrentStep();

    if (this.isCurrentStepValid()) {
      this.completeCurrentStep();
      this.formSubmit.emit(this.formData);
      this.emitWizardEvent('form-submit', this.formData);
    }

    this.isSubmitting = false;
  }

  onReset() {
    if (
      this.config.confirmBeforeExit &&
      !confirm(
        'Are you sure you want to reset the form? All data will be lost.'
      )
    ) {
      return;
    }

    this.currentStepIndex = 0;
    this.formData = {};
    this.validationErrors = {};
    this.initializeFormData();
    this.formReset.emit();
    this.emitWizardEvent('form-reset', {});
  }

  onCancel() {
    if (
      this.config.confirmBeforeExit &&
      !confirm(
        'Are you sure you want to cancel? All unsaved changes will be lost.'
      )
    ) {
      return;
    }

    this.formCancel.emit();
    this.emitWizardEvent('form-cancel', {});
  }

  onFieldChange(fieldId: string, event: Event | boolean | string) {
    let value: unknown;
    if (typeof event === 'boolean') {
      // Handle checkbox events
      value = event;
    } else if (typeof event === 'string') {
      // Handle radio button events
      value = event;
    } else {
      // Handle input events
      const target = event.target as HTMLInputElement | HTMLTextAreaElement;
      if (target instanceof HTMLInputElement && target.type === 'file') {
        value = target.files;
      } else {
        value = target.value;
      }
    }
    this.formData[fieldId] = value;

    // Validate the field and update validation errors
    const fieldErrors = this.validateField(fieldId, value);
    this.updateFieldErrors(fieldId, fieldErrors);

    this.emitWizardEvent('form-save', this.formData);
  }

  onFieldBlur(fieldId: string) {
    const value = this.formData[fieldId];
    const fieldErrors = this.validateField(fieldId, value);
    this.updateFieldErrors(fieldId, fieldErrors);
  }

  isFieldValid(fieldId: string): boolean {
    const errors = this.getFieldErrors(fieldId);
    return errors.length === 0;
  }

  getFieldErrors(fieldId: string): string[] {
    const stepId = this.currentStep?.id;
    if (!stepId) return [];

    // Get field-specific errors from the validation errors storage
    const fieldErrors = this.validationErrors[`${stepId}.${fieldId}`] || [];
    return fieldErrors;
  }

  private updateFieldErrors(fieldId: string, errors: string[]): void {
    const stepId = this.currentStep?.id;
    if (!stepId) return;

    const fieldErrorKey = `${stepId}.${fieldId}`;

    if (errors.length > 0) {
      this.validationErrors[fieldErrorKey] = errors;
    } else {
      delete this.validationErrors[fieldErrorKey];
    }
  }

  isStepAccessible(stepIndex: number): boolean {
    if (stepIndex <= this.currentStepIndex) return true;

    // Check if all previous steps are completed
    for (let i = 0; i < stepIndex; i++) {
      if (!this.steps[i]?.completed) return false;
    }

    return true;
  }

  getStepStatus(
    stepIndex: number
  ): 'completed' | 'current' | 'pending' | 'disabled' {
    if (stepIndex < this.currentStepIndex) return 'completed';
    if (stepIndex === this.currentStepIndex) return 'current';
    if (!this.isStepAccessible(stepIndex)) return 'disabled';
    return 'pending';
  }

  private initializeFormData() {
    this.steps.forEach((step) => {
      step.fields.forEach((field) => {
        if (field.value !== undefined) {
          this.formData[field.id] = field.value;
        }
      });
    });
  }

  private completeCurrentStep() {
    if (this.currentStep) {
      this.currentStep.completed = true;
      const stepData = this.getStepData(this.currentStep.id);
      this.stepComplete.emit({
        stepId: this.currentStep.id,
        stepIndex: this.currentStepIndex,
        data: stepData,
      });
      this.emitWizardEvent('step-complete', {
        stepId: this.currentStep.id,
        stepIndex: this.currentStepIndex,
        data: stepData,
      });
    }
  }

  private validateCurrentStep() {
    if (!this.currentStep) return;

    const errors: string[] = [];
    this.currentStep.fields.forEach((field) => {
      const fieldErrors = this.validateField(field.id, this.formData[field.id]);
      this.updateFieldErrors(field.id, fieldErrors);
      errors.push(...fieldErrors);
    });

    const isValid = errors.length === 0;

    this.stepValidate.emit({
      stepId: this.currentStep.id,
      stepIndex: this.currentStepIndex,
      isValid,
      errors,
    });

    this.emitWizardEvent('step-validate', {
      stepId: this.currentStep.id,
      stepIndex: this.currentStepIndex,
      isValid,
      errors,
    });
  }

  private validateField(fieldId: string, value: unknown): string[] {
    const field = this.currentStep?.fields.find((f) => f.id === fieldId);
    if (!field) return [];

    const errors: string[] = [];

    // Required validation - always check required fields
    if (field.required) {
      if (value === null || value === undefined || value === '') {
        errors.push(`${field.label} is required`);
        return errors; // Return early for required fields that are empty
      }
    }

    // Only validate other rules if the field has a value
    if (value !== null && value !== undefined && value !== '') {
      // Type-specific validations
      switch (field.type) {
        case 'email':
          if (!this.isValidEmail(value as string)) {
            errors.push(`${field.label} must be a valid email address`);
          }
          break;
        case 'number':
          if (isNaN(Number(value))) {
            errors.push(`${field.label} must be a valid number`);
          }
          break;
      }

      // Length validations
      if (typeof value === 'string') {
        if (field.minLength && value.length < field.minLength) {
          errors.push(
            `${field.label} must be at least ${field.minLength} characters`
          );
        }
        if (field.maxLength && value.length > field.maxLength) {
          errors.push(
            `${field.label} must be no more than ${field.maxLength} characters`
          );
        }
      }

      // Pattern validation
      if (field.pattern && typeof value === 'string') {
        const regex = new RegExp(field.pattern);
        if (!regex.test(value)) {
          errors.push(field.errorMessage || `${field.label} format is invalid`);
        }
      }

      // Custom validation
      if (field.validationRules) {
        field.validationRules.forEach((rule) => {
          if (rule.validator && !rule.validator(value)) {
            errors.push(rule.message);
          }
        });
      }
    }

    return errors;
  }

  isCurrentStepValid(): boolean {
    if (!this.currentStep) return false;

    const isValid = this.currentStep.fields.every((field) => {
      const errors = this.getFieldErrors(field.id);
      if (errors.length > 0) {
        console.log(`Field ${field.id} (${field.label}) has errors:`, errors);
      }
      return errors.length === 0;
    });

    console.log('isCurrentStepValid result:', isValid);
    return isValid;
  }

  private getStepData(stepId: string): Record<string, unknown> {
    const step = this.steps.find((s) => s.id === stepId);
    if (!step) return {};

    const stepData: Record<string, unknown> = {};
    step.fields.forEach((field) => {
      stepData[field.id] = this.formData[field.id];
    });

    return stepData;
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private emitStepChange(direction: 'next' | 'prev') {
    const stepId = this.currentStep?.id || '';
    this.stepChange.emit({
      stepId,
      stepIndex: this.currentStepIndex,
      direction,
    });
    this.emitWizardEvent('step-change', {
      stepId,
      stepIndex: this.currentStepIndex,
      direction,
    });
  }

  private emitWizardEvent(type: WizardEvent['type'], data: unknown) {
    const event: WizardEvent = {
      type,
      data,
      currentStep: this.currentStepIndex + 1,
      totalSteps: this.totalSteps,
    };
    this.wizardEvent.emit(event);
  }

  private startAutoSave() {
    if (this.config.autoSave && this.config.saveInterval) {
      this.autoSaveTimer = window.setInterval(() => {
        this.formSave.emit(this.formData);
        this.emitWizardEvent('form-save', this.formData);
      }, this.config.saveInterval);
    }
  }

  private stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  trackByField(index: number, field: FormField): string {
    return field.id;
  }

  getDropdownOptions(
    options: { value: string; label: string; name?: string }[]
  ): { name: string; value: string }[] {
    return options.map((option) => ({
      name: option.name || option.label,
      value: option.value,
    }));
  }

  // Helper methods for safe type casting
  getStringValue(fieldId: string): string {
    const value = this.formData[fieldId];
    return typeof value === 'string' ? value : '';
  }

  getBooleanValue(fieldId: string): boolean {
    const value = this.formData[fieldId];
    return typeof value === 'boolean' ? value : false;
  }

  // Password visibility methods
  isPasswordVisible(fieldId: string): boolean {
    return this.passwordVisibility[fieldId] || false;
  }

  togglePasswordVisibility(fieldId: string): void {
    this.passwordVisibility[fieldId] = !this.passwordVisibility[fieldId];
    this.cdr.markForCheck();
  }

  getPasswordInputType(fieldId: string): string {
    return this.isPasswordVisible(fieldId) ? 'text' : 'password';
  }

  getPasswordIconName(fieldId: string): string {
    return this.isPasswordVisible(fieldId) ? 'eye' : 'eye-off';
  }

  onPasswordIconClick(event: Event, fieldId: string): void {
    console.log(
      'MultiStepWizard: onPasswordIconClick called for field:',
      fieldId
    );
    // Prevent event from bubbling up to the textbox
    event.preventDefault();
    event.stopPropagation();
    this.togglePasswordVisibility(fieldId);
  }

  onPasswordIconKeydown(event: KeyboardEvent, fieldId: string): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.stopPropagation();
      this.togglePasswordVisibility(fieldId);
    }
  }
}
