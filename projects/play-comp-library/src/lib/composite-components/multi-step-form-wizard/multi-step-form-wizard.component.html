<div
  class="multi-step-form-wizard"
  [class.theme-minimal]="config.theme === 'minimal'"
  [class.theme-modern]="config.theme === 'modern'"
>
  <div class="wizard-header" *ngIf="config.title || config.description">
    <div class="header-content">
      <h2 *ngIf="config.title" class="wizard-title">{{ config.title }}</h2>
      <p *ngIf="config.description" class="wizard-description">
        {{ config.description }}
      </p>
    </div>
  </div>

  <!-- Step Navigation using Stepper Component -->
  <div
    class="step-navigation"
    [class.layout-vertical]="config.stepLayout === 'vertical'"
    [class.layout-tabs]="config.stepLayout === 'tabs'"
  >
    <ava-stepper
      [steps]="stepperSteps"
      [currentStep]="currentStepIndex"
      [orientation]="
        config.stepLayout === 'vertical' ? 'vertical' : 'horizontal'
      "
      [showNavigation]="config.allowStepNavigation ?? true"
      [interactive]="(config.allowStepNavigation ?? true) && !disabled"
      [disabledSteps]="disabledStepIndices"
      [stepVariant]="config.showStepNumbers ? 'default' : 'icon'"
      (stepChange)="onStepperStepChange($event)"
    >
    </ava-stepper>
  </div>

  <!-- Form Content -->
  <div class="form-content">
    <ava-card>
      <div header class="step-header">
        <div class="step-info">
          <h3 class="current-step-title">{{ currentStep?.title }}</h3>
          <p *ngIf="currentStep?.description" class="current-step-description">
            {{ currentStep?.description }}
          </p>
        </div>
        <div class="step-status">
          <span *ngIf="isStepCompleted" class="step-completed-badge">
            <ava-icon
              iconName="check-circle"
              [iconSize]="16"
              iconColor="#28a745"
            ></ava-icon>
            Completed
          </span>
        </div>
      </div>

      <div content class="step-fields">
        <!-- Form Fields -->
        <div *ngIf="currentStep" class="fields-container">
          <div
            *ngFor="let field of currentStep.fields; trackBy: trackByField"
            class="field-wrapper"
            [class.has-error]="!isFieldValid(field.id)"
          >
            <!-- Text Input -->
            <ava-textbox
              *ngIf="
                field.type === 'text' ||
                field.type === 'email' ||
                field.type === 'number'
              "
              [label]="field.label"
              [placeholder]="field.placeholder || ''"
              [value]="getStringValue(field.id)"
              [type]="field.type"
              [required]="field.required || false"
              [disabled]="field.disabled || disabled"
              [error]="getFieldErrors(field.id).join(', ')"
              [maxlength]="field.maxLength"
              [minlength]="field.minLength"
              (textboxChange)="onFieldChange(field.id, $event)"
              (textboxBlur)="onFieldBlur(field.id)"
            >
            </ava-textbox>

            <!-- Password Input with Eye Icon -->
            <ava-textbox
              *ngIf="field.type === 'password'"
              [label]="field.label"
              [placeholder]="field.placeholder || ''"
              [value]="getStringValue(field.id)"
              [type]="getPasswordInputType(field.id)"
              [required]="field.required || false"
              [disabled]="field.disabled || disabled"
              [error]="getFieldErrors(field.id).join(', ')"
              [maxlength]="field.maxLength"
              [minlength]="field.minLength"
              (textboxChange)="onFieldChange(field.id, $event)"
              (textboxBlur)="onFieldBlur(field.id)"
              (iconEndClick)="onPasswordIconClick($event, field.id)"
            >
              <div
                slot="icon-end"
                (click)="onPasswordIconClick($event, field.id)"
                (keydown)="onPasswordIconKeydown($event, field.id)"
                style="cursor: pointer; display: flex; align-items: center"
                title="Toggle password visibility"
                tabindex="0"
                role="button"
                [attr.aria-label]="'Toggle password visibility'"
              >
                <ava-icon
                  [iconName]="getPasswordIconName(field.id)"
                  [iconSize]="16"
                  iconColor="black"
                ></ava-icon>
              </div>
            </ava-textbox>

            <!-- Textarea -->
            <ava-textarea
              *ngIf="field.type === 'textarea'"
              [label]="field.label"
              [placeholder]="field.placeholder || ''"
              [required]="field.required || false"
              [disabled]="field.disabled || disabled"
              [error]="getFieldErrors(field.id).join(', ')"
              [maxlength]="field.maxLength"
              [minlength]="field.minLength"
              (textareaChange)="onFieldChange(field.id, $event)"
              (textareaBlur)="onFieldBlur(field.id)"
            >
            </ava-textarea>

            <!-- Select Dropdown -->
            <ava-dropdown
              *ngIf="field.type === 'select'"
              [label]="field.label"
              [selectedValue]="getStringValue(field.id)"
              [options]="getDropdownOptions(field.options || [])"
              [required]="field.required || false"
              [disabled]="field.disabled || disabled"
              [error]="getFieldErrors(field.id).join(', ')"
              (selectionChange)="onFieldChange(field.id, $event)"
            >
            </ava-dropdown>

            <!-- Checkbox -->
            <ava-checkbox
              *ngIf="field.type === 'checkbox'"
              [label]="field.label"
              [isChecked]="getBooleanValue(field.id)"
              [disable]="field.disabled || disabled"
              (isCheckedChange)="onFieldChange(field.id, $event)"
            >
            </ava-checkbox>

            <!-- Radio Button -->
            <ava-radio-button
              *ngIf="field.type === 'radio'"
              [options]="field.options || []"
              [selectedValue]="getStringValue(field.id)"
              (selectedValueChange)="onFieldChange(field.id, $event)"
            >
            </ava-radio-button>

            <!-- Date Input -->
            <ava-textbox
              *ngIf="field.type === 'date'"
              [label]="field.label"
              [placeholder]="field.placeholder || ''"
              [value]="getStringValue(field.id)"
              type="date"
              [required]="field.required || false"
              [disabled]="field.disabled || disabled"
              [error]="getFieldErrors(field.id).join(', ')"
              (textboxChange)="onFieldChange(field.id, $event)"
              (textboxBlur)="onFieldBlur(field.id)"
            >
            </ava-textbox>

            <!-- File Input -->
            <ava-textbox
              *ngIf="field.type === 'file'"
              [label]="field.label"
              [placeholder]="field.placeholder || 'Choose file'"
              type="file"
              [required]="field.required || false"
              [disabled]="field.disabled || disabled"
              [error]="getFieldErrors(field.id).join(', ')"
              (textboxChange)="onFieldChange(field.id, $event)"
              (textboxBlur)="onFieldBlur(field.id)"
            >
            </ava-textbox>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="loading" class="loading-state">
          <ava-icon
            iconName="loader"
            [iconSize]="32"
            iconColor="#007bff"
          ></ava-icon>
          <span>Loading...</span>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Navigation Buttons -->
  <div
    class="wizard-actions"
    [class.position-top]="config.buttonPosition === 'top'"
    [class.position-both]="config.buttonPosition === 'both'"
  >
    <div class="actions-left">
      <!-- <ava-button
        *ngIf="config.showCancelButton"
        (click)="onCancel()"
        variant="secondary"
        size="medium"
        label="Cancel"
        iconName="x"
        [iconSize]="16"
      >
      </ava-button> -->

      <ava-button
        *ngIf="config.showResetButton"
        (click)="onReset()"
        variant="warning"
        size="medium"
        label="Reset"
        iconName="rotate-ccw"
        [iconSize]="16"
      >
      </ava-button>
    </div>

    <div class="actions-right">
      <ava-button
        *ngIf="canGoPrev"
        (click)="onPrevStep()"
        variant="secondary"
        size="medium"
        label="Previous"
        iconName="chevron-left"
        [iconSize]="16"
      >
      </ava-button>

      <ava-button
        *ngIf="canGoNext"
        (click)="onNextStep()"
        variant="primary"
        size="medium"
        label="Next"
        iconName="chevron-right"
        [iconSize]="16"
        [disabled]="!isCurrentStepValid()"
        [iconPosition]="'right'"
      >
      </ava-button>

      <ava-button
        *ngIf="canSubmit"
        (click)="onSubmit()"
        variant="success"
        size="medium"
        [label]="isSubmitting ? 'Submitting...' : 'Submit'"
        [iconName]="isSubmitting ? 'loader' : 'check'"
        [iconSize]="16"
        [disabled]="isSubmitting"
      >
      </ava-button>
    </div>
  </div>
</div>
