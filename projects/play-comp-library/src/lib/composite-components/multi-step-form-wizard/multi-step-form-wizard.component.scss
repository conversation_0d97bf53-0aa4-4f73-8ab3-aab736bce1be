.multi-step-form-wizard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;

  .wizard-header {
    text-align: center;
    margin-bottom: 1rem;

    .wizard-title {
      font-size: 2rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.5rem 0;
    }

    .wizard-description {
      color: #666;
      font-size: 1.1rem;
      margin: 0;
    }
  }

  .progress-section {
    .progress-bar {
      width: 100%;
      height: 8px;
      background-color: #e9ecef;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 0.5rem;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #0056b3);
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      text-align: center;
      font-size: 0.875rem;
      color: #666;
      font-weight: 500;
    }
  }

  .step-navigation {
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;

    &.layout-vertical {
      // Vertical layout styles for stepper
    }

    &.layout-tabs {
      border-bottom: 2px solid #e9ecef;
      margin-bottom: 1rem;
    }
  }

  .form-content {
    .step-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1.5rem;

      .step-info {
        .current-step-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: #333;
          margin: 0 0 0.5rem 0;
        }

        .current-step-description {
          color: #666;
          margin: 0;
        }
      }

      .step-status {
        .step-completed-badge {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #d4edda;
          color: #155724;
          border-radius: 20px;
          font-size: 0.875rem;
          font-weight: 500;
        }
      }
    }

    .step-fields {
      .fields-container {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        .field-wrapper {
          &.has-error {
            .ava-textbox,
            .ava-textarea,
            .ava-dropdown,
            .ava-checkbox,
            .ava-radio-button {
              border-color: #dc3545;
            }
          }
        }
      }

      .loading-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        color: #666;
        gap: 1rem;
      }
    }
  }

  .wizard-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 0;
    border-top: 1px solid #e9ecef;

    &.position-top {
      order: -1;
      border-top: none;
      border-bottom: 1px solid #e9ecef;
    }

    &.position-both {
      .actions-top {
        order: -1;
        border-top: none;
        border-bottom: 1px solid #e9ecef;
      }
    }

    .actions-left,
    .actions-right {
      display: flex;
      gap: 0.75rem;
    }

    .actions-right {
      margin-left: auto;
    }
  }
}

// Theme variations
.theme-minimal {
  .step-navigation {
    .step-item {
      background-color: transparent;
      border: 1px solid #e9ecef;
      padding: 0.75rem;

      &.completed {
        background-color: transparent;
        border-color: #28a745;
      }

      &.current {
        background-color: transparent;
        border-color: #007bff;
      }
    }
  }

  .wizard-actions {
    border-top: none;
    padding-top: 0;
  }
}

.theme-modern {
  .step-navigation {
    .step-item {
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .step-indicator {
        border-radius: 8px;
      }
    }
  }

  .form-content {
    .ava-card {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .multi-step-form-wizard {
    padding: 0.5rem;
    gap: 1.5rem;

    .wizard-header {
      .wizard-title {
        font-size: 1.5rem;
      }

      .wizard-description {
        font-size: 1rem;
      }
    }

    .step-navigation {
      flex-direction: column;
      gap: 0.5rem;

      .step-item {
        min-width: auto;
        padding: 0.75rem;
      }
    }

    .form-content {
      .step-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;

        .step-status {
          align-self: flex-end;
        }
      }
    }

    .wizard-actions {
      flex-direction: column;
      gap: 1rem;

      .actions-left,
      .actions-right {
        width: 100%;
        justify-content: center;
      }

      .actions-right {
        margin-left: 0;
      }
    }
  }
}
