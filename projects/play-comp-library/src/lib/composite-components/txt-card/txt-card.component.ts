import { ChangeDetectionStrategy, Component, EventEmitter, Output, ViewEncapsulation } from '@angular/core';
import { DefaultCardComponent } from '../../components/card/default-card/default-card.component';
@Component({
  selector: 'ava-txt-card',
  imports: [DefaultCardComponent],
  templateUrl: './txt-card.component.html',
  styleUrl: './txt-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class TxtCardComponent {
  @Output() cardClick = new EventEmitter<void>();


  onCardClick() {
    this.cardClick.emit();
  }
}
