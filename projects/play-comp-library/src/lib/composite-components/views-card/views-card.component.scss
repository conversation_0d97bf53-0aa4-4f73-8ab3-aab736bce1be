.outer {
  width: var(--card-width, 300px);
  height: var(--card-height, 250px);
  border-radius: 10px;
  padding: 1px;
  background: radial-gradient(
    circle 230px at 0% 0%,
    var(--outer-gradient-start, #ffffff),
    var(--outer-gradient-end, #0c0d0d)
  );
  position: relative;
}

.dot {
  width: 5px;
  aspect-ratio: 1;
  position: absolute;
  background-color: var(--dot-color, #fff);
  box-shadow: 0 0 10px var(--dot-shadow-color, #ffffff);
  border-radius: 100px;
  z-index: 2;
  right: 10%;
  top: 10%;
  animation: moveDot 6s linear infinite;
}

@keyframes moveDot {
  0%,
  100% {
    top: 10%;
    right: 10%;
  }
  25% {
    top: 10%;
    right: calc(100% - 35px);
  }
  50% {
    top: calc(100% - 30px);
    right: calc(100% - 35px);
  }
  75% {
    top: calc(100% - 30px);
    right: 10%;
  }
}

.card {
  z-index: 1;
  width: 100%;
  height: 100%;
  border-radius: 9px;
  border: solid 1px #202222;
  background-size: 20px 20px;
  background: radial-gradient(
    circle 280px at 0% 0%,
    var(--card-gradient-start, #444444),
    var(--card-gradient-end, #0c0d0d)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-direction: column;
  color: #fff;
}

.ray {
  width: 220px;
  height: 45px;
  border-radius: 100px;
  position: absolute;
  background-color: var(--ray-color, #c7c7c7);
  opacity: 0.4;
  box-shadow: 0 0 50px var(--ray-shadow-color, #fff);
  filter: blur(10px);
  transform-origin: 10%;
  top: 0%;
  left: 0;
  transform: rotate(40deg);
}

.card .text {
  font-weight: bolder;
  font-size: 4rem;
  background: linear-gradient(
    45deg,
    var(--text-gradient-start, #000000) 4%,
    var(--text-gradient-middle, #fff),
    var(--text-gradient-end, #000)
  );
  background-clip: text;
  color: transparent;
}

.line {
  width: 100%;
  height: 1px;
  position: absolute;
  background-color: var(--line-color, #2c2c2c);
}

.topl {
  top: 10%;
  background: linear-gradient(
    90deg,
    var(--top-line-gradient-start, #888888) 30%,
    var(--top-line-gradient-end, #1d1f1f) 70%
  );
}

.bottoml {
  bottom: 10%;
}

.leftl {
  left: 10%;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    180deg,
    var(--left-line-gradient-start, #747474) 30%,
    var(--left-line-gradient-end, #222424) 70%
  );
}

.rightl {
  right: 10%;
  width: 1px;
  height: 100%;
}
