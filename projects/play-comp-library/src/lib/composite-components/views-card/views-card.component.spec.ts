import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ViewsCardComponent } from './views-card.component';

describe('ViewsCardComponent', () => {
  let component: ViewsCardComponent;
  let fixture: ComponentFixture<ViewsCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ViewsCardComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ViewsCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default value and label', () => {
    expect(component.value).toBe('750k');
    expect(component.label).toBe('Views');
  });

  it('should apply correct color class', () => {
    component.color = 'green';
    expect(component.colorClass).toBe('views-card-green');
  });

  it('should apply correct animation class', () => {
    component.animationSpeed = 'fast';
    expect(component.animationClass).toBe('animation-fast');
  });
});
