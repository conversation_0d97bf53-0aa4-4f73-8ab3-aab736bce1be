.flip-card {
  background-color: transparent;
  width: var(--card-width, 190px);
  height: var(--card-height, 254px);
  perspective: 1000px;
  font-family: sans-serif;
}

.title {
  font-size: 1.5em;
  font-weight: 900;
  text-align: center;
  margin: 0;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  box-shadow: 0 8px 14px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border: 1px solid var(--border-color, coral);
  border-radius: 1rem;
}

.flip-card-front {
  background: linear-gradient(
    120deg,
    var(--front-gradient-color1, bisque) 60%,
    var(--front-gradient-color2, rgb(255, 231, 222)) 88%,
    var(--front-gradient-color3, rgb(255, 211, 195)) 40%,
    var(--front-gradient-color4, rgba(255, 127, 80, 0.603)) 48%
  );
  color: var(--front-text-color, coral);
}

.flip-card-back {
  background: linear-gradient(
    120deg,
    var(--back-gradient-color1, rgb(255, 174, 145)) 30%,
    var(--back-gradient-color2, coral) 88%,
    var(--back-gradient-color3, bisque) 40%,
    var(--back-gradient-color4, rgb(255, 185, 160)) 78%
  );
  color: var(--back-text-color, white);
  transform: rotateY(180deg);
}
