.data-table-header {
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .table-title {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
    }

    .header-actions {
      .selection-badge {
        background-color: #007bff;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }
    }
  }
}

.data-table-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .search-section {
    width: 100%;
    max-width: 400px;
  }

  .bulk-actions-section {
    .bulk-actions-grid {
      display: flex;
      gap: 0.75rem;
      flex-wrap: wrap;
      align-items: center;
    }
  }

  .table-section {
    position: relative;
    min-height: 200px;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: #666;

        ava-icon {
          animation: spin 1s linear infinite;
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      color: #666;
      text-align: center;

      p {
        margin: 1rem 0 0 0;
        font-size: 1rem;
      }
    }

    .table-container {
      overflow-x: auto;
      border: 1px solid #e9ecef;
      border-radius: 4px;

      .data-table {
        width: 100%;
        border-collapse: collapse;
        background-color: white;

        th,
        td {
          padding: 0.75rem;
          border-bottom: 1px solid #e9ecef;
          text-align: left;
        }

        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #495057;
          position: sticky;
          top: 0;
          z-index: 5;

          &.select-column {
            width: 50px;
            text-align: center;
          }

          &.actions-column {
            width: 100px;
            text-align: center;
          }

          &.data-column {
            &.sortable {
              cursor: pointer;
              user-select: none;

              &:hover {
                background-color: #e9ecef;
              }

              .column-header {
                display: flex;
                align-items: center;
                gap: 0.5rem;

                .column-label {
                  flex: 1;
                }
              }
            }
          }
        }

        tbody {
          tr {
            transition: background-color 0.2s ease;

            &:hover {
              background-color: #f8f9fa;
            }

            &.selected {
              background-color: #e3f2fd;
            }

            td {
              &.select-column {
                text-align: center;
                width: 50px;
              }

              &.actions-cell {
                text-align: center;
                width: 120px;

                .row-actions {
                  display: flex;
                  gap: 0.5rem;
                  justify-content: center;
                  flex-wrap: wrap;
                }
              }

              &.data-cell {
                vertical-align: middle;
              }
            }
          }
        }
      }
    }
  }

  .pagination-section {
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .data-table-content {
    .search-section {
      max-width: 100%;
    }

    .bulk-actions-section {
      .bulk-actions-grid {
        flex-direction: column;
        align-items: stretch;

        ava-button {
          width: 100%;
        }
      }
    }

    .table-section {
      .table-container {
        .data-table {
          th,
          td {
            padding: 0.5rem;
            font-size: 0.875rem;
          }

          th.actions-column,
          td.actions-cell {
            width: 100px;
          }
        }
      }
    }
  }
}

// Loading animation
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Disabled state
.data-table-content.disabled {
  opacity: 0.5;
  pointer-events: none;
}
