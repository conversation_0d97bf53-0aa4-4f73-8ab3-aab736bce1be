.console-sidebar {
  display: flex;
  width: 425px;
  height: 584px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 24px;
  flex-shrink: 0;
  border-radius: 24px;
  background: #FAFAFA;
  box-sizing: border-box;

  .console-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .console-title {
      color: #000;
      font-family: Mulish;
      font-size: var(--Global-Typography-size-xl, 24px);
      font-style: normal;
      font-weight: 700;
      line-height: 150%; /* 36px */
    }
  }

  .console-nav-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .console-nav-item {
      height:68px;
      display: flex;
      padding: 24px 8px;
      align-items: center;
      gap: 16px !important;
      flex: 1 0 0;
      align-self: stretch;
      border-radius: 200px;
      border: 1px solid #C4C4C4;
      background: #E2E2E2;
    &:hover,
    &.active {
      background: #E0E0E0;
    }

    .console-icon-circle {
      display: flex;
      width: 50px;
      height: 50px;
      padding: 2px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      aspect-ratio: 1/1;
      border-radius: 200px;
      border: 1px solid #CBCBCB;
      background: #FFF;
      
    }

    .console-nav-text {
      font-weight: 700;
      font-size: 20px;
      color: #181A1C;
      white-space: nowrap;
    }
  }

  // Collapsed state
  &.collapsed {
    width: 126px;
    align-items: center;

    .console-header {
      justify-content: center;

      .console-title {
        display: none;
      }
    }

    .console-nav-section {
      align-items: center;
    }

    .console-nav-item {
      border-radius: 200px;
      border: 1px solid #CBCBCB;
      background: #E2E2E2;
      padding: 0; // Remove padding to center properly
        min-height: unset;
        justify-content: center;
        align-items: center; 
        width: 60px; 
        height: 60px;
        flex: none; 
        align-self: center; 
        gap: 0; 

      .console-nav-text {
        display: none;
      }

      .console-icon-circle {
        margin-right: 0;
      }
    }
  }
}

// Additional responsive adjustments
@media (max-width: 768px) {
  .console-sidebar {
    width: 100%;
    max-width: 425px;
    height: auto;
    min-height: 584px;

    &.collapsed {
      width: 126px;
      height: auto;
      min-height: 584px;
    }
  }
}

.console-sidebar .sidebar-footer {
    padding: 0px !important;
    border-top:none !important; 
  }

.console-sidebar {
  border-radius: 24px !important;
  border: 1px solid #DCDCDC !important;
}
.console-sidebar .sidebar-header {
  border-bottom: none !important;
  padding: 20px 16px 0px 20px;
}

.console-sidebar.collapsed .sidebar-header {
  padding: 20px 16px 0px 20px !important;
}
.console-sidebar .sidebar-content {
  padding: 24px 16px 20px 16px !important;
}
.console-sidebar.collapsed .sidebar-content {
  padding: 24px 16px 20px 16px;
}
.console-sidebar .sidebar-content 
{
  overflow: hidden ;
}
.ava-sidebar-container{
  border:none !important;
}