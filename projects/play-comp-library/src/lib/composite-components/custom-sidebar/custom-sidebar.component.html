<ava-sidebar width="425px" collapsedWidth="126px" [showCollapseButton]="true" [class.collapsed]="isCollapsed"
  (collapseToggle)="onCollapseToggle($event)" class="console-sidebar" height="584px" [showFooter]="false">
  <!-- Header Content -->
  <div slot="header" class="console-header">
    <span class="console-title">Quick Actions</span>

  </div>

  <!-- Main Content -->
  <div slot="content">
    <div class="console-nav-section">
      <div *ngFor="let item of sidebarItems" class="console-nav-item" [class.active]="item.active"
        (click)="onItemClick(item)">
        <span class="console-icon-circle">
          <ava-icon [iconName]="item.icon"></ava-icon>
        </span>
        <span class="console-nav-text">{{ item.text }}</span>
      </div>
    </div>
  </div>
</ava-sidebar>