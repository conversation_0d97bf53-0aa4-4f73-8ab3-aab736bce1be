<div class="ava-rating-card-container">
  <!-- Rating Card -->
  <ava-default-card *ngIf="!isSubmitted">
    <div header>
      <ng-content select="div[header]"></ng-content>
      <div class="star-icon">
        <ava-icon
          *ngFor="let star of stars"
          iconName="star"
          [iconSize]="24"
         [iconColor]="star.filled ? '#dc3545' : '#e9ecef'"
         (click)="selectRating(star.index + 1)">
        </ava-icon>
      </div>
      <h2 class="title">{{ config.title || 'How did we do?' }}</h2>
    </div>
    <div content>
      <ng-content select="div[content]"></ng-content>
      <ava-textarea
        placeholder="{{ config.description || 'Please let us know how we did by giving us your valuable feedback. Your feedback matters a lot to us to help us improve our offering!' }}"
        [rows]="4">
      </ava-textarea>
      <div class="rating-buttons" *ngIf="showRatingButtons">
        <ava-button
          variant="secondary"
          *ngFor="let rating of ratingOptions"
          [class.selected]="selectedRating === rating"
          class="rating-btn"
          (click)="selectRating(rating)"
          [label]="rating.toString()">
        </ava-button>
      </div>
    </div>
    <div footer>
      <ng-content select="div[footer]"></ng-content>
      <ava-button
        width="90%"
        [label]="config.submitButtonText || 'Submit'"
        variant="primary"
        (userClick)="submitRating()"
        [pill]="true"
        [disabled]="!selectedRating"
        class="submit-btn-wrapper">
      </ava-button>
    </div>
  </ava-default-card>
 
  <!-- Thank You Card -->
  <ava-default-card *ngIf="isSubmitted">
    <div class="thank-you-container">
      <div *ngIf="config.showIllustration !== false" class="illustration">
      </div>
      <div class="selected-rating">
        You selected {{ selectedRating }} out of {{ ratingOptions.length }}
      </div>
      <h2 class="thank-you-title">{{ config.thankYouTitle || 'Thank You' }}</h2>
      <p class="thank-you-message">
        {{ config.thankYouMessage || 'We appreciate you taking the time to give a rating. If you ever need more support, don\'t hesitate to get in touch.' }}
      </p>
    </div>
  </ava-default-card>
</div>