import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { DefaultCardComponent } from '../../components/card/default-card/default-card.component';
import { ButtonComponent } from '../../components/button/button.component';
import { IconComponent } from '../../components/icon/icon.component';
import { AvaTextareaComponent } from '../../components/textarea/ava-textarea.component';


 
export interface RatingConfig {
  title?: string;
  description?: string;
  minRating?: number;
  maxRating?: number;
  submitButtonText?: string;
  thankYouTitle?: string;
  thankYouMessage?: string;
  showIllustration?: boolean;
}
 
@Component({
  selector: 'ava-rating-card',
  standalone: true,
  imports: [CommonModule, DefaultCardComponent, ButtonComponent, IconComponent, AvaTextareaComponent],
  templateUrl: './rating-card.component.html',
  styleUrls: ['./rating-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
 
export class RatingCardComponent {
  @Input() config: RatingConfig = {};
  @Input() isSubmitted: boolean = false;
  @Input() showRatingButtons: boolean = true; // New input property to control the visibility of rating buttons
  @Output() ratingSubmitted = new EventEmitter<number>();
 
  selectedRating: number | null = null;
  ratingOptions: number[] = [];
  stars: { index: number; filled: boolean }[] = [];
 
  ngOnInit() {
    const minRating = this.config.minRating || 1;
    const maxRating = this.config.maxRating || 5;
    this.ratingOptions = Array.from({ length: maxRating - minRating + 1 }, (_, i) => minRating + i);
    this.stars = this.ratingOptions.map((_, index) => ({ index, filled: false }));
  }
 
  selectRating(rating: number) {
    this.selectedRating = rating;
    this.stars = this.stars.map(star => ({
      ...star,
      filled: star.index < rating
    }));
  }
 
  submitRating() {
    if (this.selectedRating) {
      this.isSubmitted = true;
      this.ratingSubmitted.emit(this.selectedRating);
    }
  }
 
  reset() {
    this.selectedRating = null;
    this.isSubmitted = false;
    this.stars = this.stars.map(star => ({
      ...star,
      filled: false
    }));
  }
 
}