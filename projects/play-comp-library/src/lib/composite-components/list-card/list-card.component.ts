import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DefaultCardComponent } from '../../components/card/default-card/default-card.component';
import { IconComponent } from '../../components/icon/icon.component';
import { AvatarsComponent } from '../../components/avatars/avatars.component';
import { LinkComponent } from '../../components/link/link.component';
import { ButtonComponent } from '../../components/button/button.component';
import { DividersComponent } from '../../components/dividers/dividers.component';

export interface ListCardColumn1 {
  columnItem: 'avatar' | 'icon';
  columnItemProps: {
    // For avatar
    imageUrl?: string;
    profileText?: string;
    size?: 'ultra-small' | 'extra-small' | 'small' | 'medium' | 'large' | 'extra-large' | 'ultra-large';
    shape?: 'pill' | 'square';
    // For icon
    iconName?: string;
    iconColor?: string;
    iconSize?: number | string;
  };
}

export interface ListCardColumn2 {
  heading?: string;
  description?: string;
  link?: {
    text: string;
    toLocation?: string;
    href?: string;
    color?: string;
  };
}

export interface ListCardColumn3 {
  button?: {
    text: string;
    action?: string;
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
    color?: string;
  };
}

export interface ListCardItem {
  column1?: ListCardColumn1;
  column2?: ListCardColumn2;
  column3?: ListCardColumn3;
}

export interface ListCardData {
  heading: string;
  listItems: ListCardItem[];
}

@Component({
  selector: 'ava-list-card',
  imports: [
    CommonModule,
    DefaultCardComponent,
    IconComponent,
    AvatarsComponent,
    LinkComponent,
    ButtonComponent,
    DividersComponent
  ],
  templateUrl: './list-card.component.html',
  styleUrl: './list-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ListCardComponent {
  @Input() data: ListCardData = {
    heading: '',
    listItems: []
  };

  @Output() buttonClicked = new EventEmitter<{ action: string | undefined, item: ListCardItem }>();
  @Output() linkClicked = new EventEmitter<{ link: any, item: ListCardItem }>();

  onButtonClick(action: string | undefined, item: ListCardItem) {
    if (action) {
      // Emit event for parent component to handle
      this.buttonClicked.emit({ action, item });
    }
  }

  onLinkClick(link: any, item: ListCardItem) {
    // Emit event for parent component to handle
    this.linkClicked.emit({ link, item });
  }
}
