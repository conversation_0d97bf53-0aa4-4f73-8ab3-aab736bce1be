.ava-list-card-container {
    width: 438px;

    .list-card-heading {
        overflow: hidden;
        color: #3B3F46;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: Mulish;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height: 28px;
        margin-bottom: 24px;
    }

    .list-container {
        max-height: 390px;
        overflow-y: auto;

        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE and Edge

        &::-webkit-scrollbar {
            display: none;
        }

        height: auto;
        min-height: auto;
    }

    .list-items {
        .list-item-row {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 10px;

            &.has-icon-has-button {
                .column-1 {
                    flex-shrink: 0;
                    width: auto;
                }

                .column-2 {
                    flex: 1;
                    min-width: 0;
                }

                .column-3 {
                    flex-shrink: 0;
                }
            }

            &.no-icon-no-button {
                .column-2 {
                    flex: 1;
                    width: 100%;
                    min-width: 0;
                }
            }

            &.no-icon-has-button {
                .column-2 {
                    flex: 1;
                    min-width: 0;
                }

                .column-3 {
                    flex-shrink: 0;
                }
            }

            &.has-icon-no-button {
                .column-1 {
                    flex-shrink: 0;
                    width: auto;
                }

                .column-2 {
                    flex: 1;
                    min-width: 0;
                }
            }

            .column-1 {
                display: flex;
                align-items: flex-start;
                justify-content: flex-start;
                padding-top: 0;
                margin-top: 0;

                ava-icon {
                    align-self: flex-start;
                    margin-top: 0;
                    padding-top: 0;

                    .ava-icon-container {
                        align-items: flex-start;
                        justify-content: flex-start;
                        padding: 0;
                        margin: 0;
                    }

                    lucide-icon {
                        vertical-align: top;
                        margin-top: 0;
                        padding-top: 0;
                    }
                }

                ava-avatars {
                    align-self: flex-start;
                    margin-top: 0;
                    padding-top: 0;

                    .avatar-wrapper {
                        padding: 0;
                    }
                }
            }

            .column-2 {
                .item-heading {
                    overflow: hidden;
                    color: #000;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-family: Inter;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 20px;
                    margin-bottom: 0;
                }

                .item-description {
                    overflow: hidden;
                    color: #000;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    font-family: Inter;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 16px;
                    margin-bottom: 0;
                }

                .item-link {
                    margin-top: 0;
                }
            }

            .column-3 {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 8px;
            }
        }

        .divider-container {
            margin: 10px 0;
        }
    }

    .list-items:not(:last-child) {
        margin-bottom: 24px;
    }

    .action-link.small {
        font-size: 12px;
    }
}