:root {
  // Define some base spacing variables used later (Gutters)
  --mdb-gutter-x: 1.5rem; // Default horizontal gutter for rows/cols
  --mdb-gutter-y: 0; // Default vertical gutter for rows/cols

  // Body defaults affecting spacing/layout indirectly
  --mdb-body-line-height: 1.6;
}

body {
  margin: 0;
  line-height: var(--mdb-body-line-height); // Spacing related
}

hr {
  margin: 1rem 0; // Spacing
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0; // Spacing
  margin-bottom: 0.5rem; // Spacing
  line-height: 1.2; // Spacing related
}

p {
  margin-top: 1rem; // Spacing
  margin-bottom: 1rem; // Spacing
}

address {
  margin-bottom: 1rem; // Spacing
  line-height: inherit; // Spacing related
}

ol,
ul {
  padding-left: 2rem; // Spacing
}

dl,
ol,
ul {
  margin-top: 0; // Spacing
  margin-bottom: 1rem; // Spacing
}

ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0; // Spacing
}

dd {
  margin-bottom: 0.5rem; // Spacing
  margin-left: 0; // Spacing
}

blockquote {
  margin: 0 0 1rem; // Spacing
}

.mark,
mark {
  padding: 0.2em; // Spacing
}

sub,
sup {
  line-height: 0; // Spacing related
}

sub {
  bottom: -0.25em; // Spacing (positioning)
}

sup {
  top: -0.5em; // Spacing (positioning)
}

pre {
  margin-top: 0; // Spacing
  margin-bottom: 1rem; // Spacing
}

kbd {
  padding: 0.2rem 0.4rem; // Spacing
}

kbd kbd {
  padding: 0; // Spacing
}

figure {
  margin: 0 0 1rem; // Spacing
}

caption {
  padding-top: 1rem; // Spacing
  padding-bottom: 1rem; // Spacing
}

button,
input,
optgroup,
select,
textarea {
  margin: 0; // Spacing reset
  line-height: inherit; // Spacing related
}

::-moz-focus-inner {
  padding: 0; // Spacing reset
}

fieldset {
  padding: 0; // Spacing reset
  margin: 0; // Spacing reset
}

legend {
  padding: 0; // Spacing reset
  margin-bottom: 0.5rem; // Spacing
  line-height: inherit; // Spacing related
}

legend + * {
  clear: left; // Layout related, often interacts with spacing/floats
}

::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-year-field {
  padding: 0; // Spacing reset
}

::-webkit-color-swatch-wrapper {
  padding: 0; // Spacing reset
}

.lead {
  line-height: 1.2; // Spacing related (though primarily font-size driven)
}

.display-1,
.display-2,
.display-3,
.display-4,
.display-5,
.display-6 {
  line-height: 1.2; // Spacing related
}

.list-inline,
.list-unstyled {
  padding-left: 0; // Spacing reset
}

.list-inline-item {
  display: inline-block; // Flex/layout related
}

.list-inline-item:not(:last-child) {
  margin-right: 0.5rem; // Spacing
}

.blockquote {
  margin-bottom: 1rem; // Spacing
}

.blockquote > :last-child {
  margin-bottom: 0; // Spacing
}

.blockquote-footer {
  margin-top: -1rem; // Spacing
  margin-bottom: 1rem; // Spacing
}

.img-thumbnail {
  padding: 0.25rem; // Spacing
}

.figure-img {
  margin-bottom: 0.5rem; // Spacing
  line-height: 1; // Spacing related
}

/*------------------------------------*\
  #CONTAINERS & GRID (ROW/COL)
\*------------------------------------*/

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  padding-right: var(--mdb-gutter-x, 0.75rem); // Spacing
  padding-left: var(--mdb-gutter-x, 0.75rem); // Spacing
  margin-right: auto; // Spacing (centering)
  margin-left: auto; // Spacing (centering)
}

.row {
  --mdb-gutter-x: 1.5rem; // Spacing variable specific to rows
  --mdb-gutter-y: 0; // Spacing variable specific to rows
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  margin-top: calc(var(--mdb-gutter-y) * -1); // Spacing (negative margin trick)
  margin-right: calc(
    var(--mdb-gutter-x) * -0.5
  ); // Spacing (negative margin trick)
  margin-left: calc(
    var(--mdb-gutter-x) * -0.5
  ); // Spacing (negative margin trick)
}

.row > * {
  flex-shrink: 0; // Flexbox
  padding-right: calc(var(--mdb-gutter-x) * 0.5); // Spacing (column padding)
  padding-left: calc(var(--mdb-gutter-x) * 0.5); // Spacing (column padding)
  margin-top: var(--mdb-gutter-y); // Spacing (vertical gutter)
}

.col {
  flex: 1 0 0%; // Flexbox
}

.row-cols-auto > * {
  flex: 0 0 auto; // Flexbox
  width: auto;
}
.row-cols-1 > * {
  flex: 0 0 auto; // Flexbox
  width: 100%;
}
.row-cols-2 > * {
  flex: 0 0 auto; // Flexbox
  width: 50%;
}
.row-cols-3 > * {
  flex: 0 0 auto; // Flexbox
  width: 33.3333333333%;
}
.row-cols-4 > * {
  flex: 0 0 auto; // Flexbox
  width: 25%;
}
.row-cols-5 > * {
  flex: 0 0 auto; // Flexbox
  width: 20%;
}
.row-cols-6 > * {
  flex: 0 0 auto; // Flexbox
  width: 16.6666666667%;
}

.col-auto {
  flex: 0 0 auto; // Flexbox
  width: auto;
}
.col-1 {
  flex: 0 0 auto; // Flexbox
  width: 8.33333333%;
}
.col-2 {
  flex: 0 0 auto; // Flexbox
  width: 16.66666667%;
}
.col-3 {
  flex: 0 0 auto; // Flexbox
  width: 25%;
}
.col-4 {
  flex: 0 0 auto; // Flexbox
  width: 33.33333333%;
}
.col-5 {
  flex: 0 0 auto; // Flexbox
  width: 41.66666667%;
}
.col-6 {
  flex: 0 0 auto; // Flexbox
  width: 50%;
}
.col-7 {
  flex: 0 0 auto; // Flexbox
  width: 58.33333333%;
}
.col-8 {
  flex: 0 0 auto; // Flexbox
  width: 66.66666667%;
}
.col-9 {
  flex: 0 0 auto; // Flexbox
  width: 75%;
}
.col-10 {
  flex: 0 0 auto; // Flexbox
  width: 83.33333333%;
}
.col-11 {
  flex: 0 0 auto; // Flexbox
  width: 91.66666667%;
}
.col-12 {
  flex: 0 0 auto; // Flexbox
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
} // Spacing
.offset-2 {
  margin-left: 16.66666667%;
} // Spacing
.offset-3 {
  margin-left: 25%;
} // Spacing
.offset-4 {
  margin-left: 33.33333333%;
} // Spacing
.offset-5 {
  margin-left: 41.66666667%;
} // Spacing
.offset-6 {
  margin-left: 50%;
} // Spacing
.offset-7 {
  margin-left: 58.33333333%;
} // Spacing
.offset-8 {
  margin-left: 66.66666667%;
} // Spacing
.offset-9 {
  margin-left: 75%;
} // Spacing
.offset-10 {
  margin-left: 83.33333333%;
} // Spacing
.offset-11 {
  margin-left: 91.66666667%;
} // Spacing

.g-0,
.gx-0 {
  --mdb-gutter-x: 0;
} // Spacing variable reset
.g-0,
.gy-0 {
  --mdb-gutter-y: 0;
} // Spacing variable reset
.g-1,
.gx-1 {
  --mdb-gutter-x: 0.25rem;
} // Spacing variable set
.g-1,
.gy-1 {
  --mdb-gutter-y: 0.25rem;
} // Spacing variable set
.g-2,
.gx-2 {
  --mdb-gutter-x: 0.5rem;
} // Spacing variable set
.g-2,
.gy-2 {
  --mdb-gutter-y: 0.5rem;
} // Spacing variable set
.g-3,
.gx-3 {
  --mdb-gutter-x: 1rem;
} // Spacing variable set
.g-3,
.gy-3 {
  --mdb-gutter-y: 1rem;
} // Spacing variable set
.g-4,
.gx-4 {
  --mdb-gutter-x: 1.5rem;
} // Spacing variable set
.g-4,
.gy-4 {
  --mdb-gutter-y: 1.5rem;
} // Spacing variable set
.g-5,
.gx-5 {
  --mdb-gutter-x: 3rem;
} // Spacing variable set
.g-5,
.gy-5 {
  --mdb-gutter-y: 3rem;
} // Spacing variable set

// Responsive Grid Classes
@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  } // Flexbox
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  } // Flexbox
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  } // Flexbox
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  } // Flexbox
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  } // Flexbox
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  } // Flexbox
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  } // Flexbox
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  } // Flexbox
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  } // Flexbox
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  } // Flexbox
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  } // Flexbox
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  } // Flexbox
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  } // Flexbox
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .offset-sm-0 {
    margin-left: 0;
  } // Spacing
  .offset-sm-1 {
    margin-left: 8.33333333%;
  } // Spacing
  .offset-sm-2 {
    margin-left: 16.66666667%;
  } // Spacing
  .offset-sm-3 {
    margin-left: 25%;
  } // Spacing
  .offset-sm-4 {
    margin-left: 33.33333333%;
  } // Spacing
  .offset-sm-5 {
    margin-left: 41.66666667%;
  } // Spacing
  .offset-sm-6 {
    margin-left: 50%;
  } // Spacing
  .offset-sm-7 {
    margin-left: 58.33333333%;
  } // Spacing
  .offset-sm-8 {
    margin-left: 66.66666667%;
  } // Spacing
  .offset-sm-9 {
    margin-left: 75%;
  } // Spacing
  .offset-sm-10 {
    margin-left: 83.33333333%;
  } // Spacing
  .offset-sm-11 {
    margin-left: 91.66666667%;
  } // Spacing
  .g-sm-0,
  .gx-sm-0 {
    --mdb-gutter-x: 0;
  } // Spacing variable reset
  .g-sm-0,
  .gy-sm-0 {
    --mdb-gutter-y: 0;
  } // Spacing variable reset
  .g-sm-1,
  .gx-sm-1 {
    --mdb-gutter-x: 0.25rem;
  } // Spacing variable set
  .g-sm-1,
  .gy-sm-1 {
    --mdb-gutter-y: 0.25rem;
  } // Spacing variable set
  .g-sm-2,
  .gx-sm-2 {
    --mdb-gutter-x: 0.5rem;
  } // Spacing variable set
  .g-sm-2,
  .gy-sm-2 {
    --mdb-gutter-y: 0.5rem;
  } // Spacing variable set
  .g-sm-3,
  .gx-sm-3 {
    --mdb-gutter-x: 1rem;
  } // Spacing variable set
  .g-sm-3,
  .gy-sm-3 {
    --mdb-gutter-y: 1rem;
  } // Spacing variable set
  .g-sm-4,
  .gx-sm-4 {
    --mdb-gutter-x: 1.5rem;
  } // Spacing variable set
  .g-sm-4,
  .gy-sm-4 {
    --mdb-gutter-y: 1.5rem;
  } // Spacing variable set
  .g-sm-5,
  .gx-sm-5 {
    --mdb-gutter-x: 3rem;
  } // Spacing variable set
  .g-sm-5,
  .gy-sm-5 {
    --mdb-gutter-y: 3rem;
  } // Spacing variable set
}

@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  } // Flexbox
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  } // Flexbox
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  } // Flexbox
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  } // Flexbox
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  } // Flexbox
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  } // Flexbox
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  } // Flexbox
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  } // Flexbox
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  } // Flexbox
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  } // Flexbox
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  } // Flexbox
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  } // Flexbox
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  } // Flexbox
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .offset-md-0 {
    margin-left: 0;
  } // Spacing
  .offset-md-1 {
    margin-left: 8.33333333%;
  } // Spacing
  .offset-md-2 {
    margin-left: 16.66666667%;
  } // Spacing
  .offset-md-3 {
    margin-left: 25%;
  } // Spacing
  .offset-md-4 {
    margin-left: 33.33333333%;
  } // Spacing
  .offset-md-5 {
    margin-left: 41.66666667%;
  } // Spacing
  .offset-md-6 {
    margin-left: 50%;
  } // Spacing
  .offset-md-7 {
    margin-left: 58.33333333%;
  } // Spacing
  .offset-md-8 {
    margin-left: 66.66666667%;
  } // Spacing
  .offset-md-9 {
    margin-left: 75%;
  } // Spacing
  .offset-md-10 {
    margin-left: 83.33333333%;
  } // Spacing
  .offset-md-11 {
    margin-left: 91.66666667%;
  } // Spacing
  .g-md-0,
  .gx-md-0 {
    --mdb-gutter-x: 0;
  } // Spacing variable reset
  .g-md-0,
  .gy-md-0 {
    --mdb-gutter-y: 0;
  } // Spacing variable reset
  .g-md-1,
  .gx-md-1 {
    --mdb-gutter-x: 0.25rem;
  } // Spacing variable set
  .g-md-1,
  .gy-md-1 {
    --mdb-gutter-y: 0.25rem;
  } // Spacing variable set
  .g-md-2,
  .gx-md-2 {
    --mdb-gutter-x: 0.5rem;
  } // Spacing variable set
  .g-md-2,
  .gy-md-2 {
    --mdb-gutter-y: 0.5rem;
  } // Spacing variable set
  .g-md-3,
  .gx-md-3 {
    --mdb-gutter-x: 1rem;
  } // Spacing variable set
  .g-md-3,
  .gy-md-3 {
    --mdb-gutter-y: 1rem;
  } // Spacing variable set
  .g-md-4,
  .gx-md-4 {
    --mdb-gutter-x: 1.5rem;
  } // Spacing variable set
  .g-md-4,
  .gy-md-4 {
    --mdb-gutter-y: 1.5rem;
  } // Spacing variable set
  .g-md-5,
  .gx-md-5 {
    --mdb-gutter-x: 3rem;
  } // Spacing variable set
  .g-md-5,
  .gy-md-5 {
    --mdb-gutter-y: 3rem;
  } // Spacing variable set
}

@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  } // Flexbox
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  } // Flexbox
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  } // Flexbox
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  } // Flexbox
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  } // Flexbox
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  } // Flexbox
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  } // Flexbox
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  } // Flexbox
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  } // Flexbox
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  } // Flexbox
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  } // Flexbox
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  } // Flexbox
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  } // Flexbox
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .offset-lg-0 {
    margin-left: 0;
  } // Spacing
  .offset-lg-1 {
    margin-left: 8.33333333%;
  } // Spacing
  .offset-lg-2 {
    margin-left: 16.66666667%;
  } // Spacing
  .offset-lg-3 {
    margin-left: 25%;
  } // Spacing
  .offset-lg-4 {
    margin-left: 33.33333333%;
  } // Spacing
  .offset-lg-5 {
    margin-left: 41.66666667%;
  } // Spacing
  .offset-lg-6 {
    margin-left: 50%;
  } // Spacing
  .offset-lg-7 {
    margin-left: 58.33333333%;
  } // Spacing
  .offset-lg-8 {
    margin-left: 66.66666667%;
  } // Spacing
  .offset-lg-9 {
    margin-left: 75%;
  } // Spacing
  .offset-lg-10 {
    margin-left: 83.33333333%;
  } // Spacing
  .offset-lg-11 {
    margin-left: 91.66666667%;
  } // Spacing
  .g-lg-0,
  .gx-lg-0 {
    --mdb-gutter-x: 0;
  } // Spacing variable reset
  .g-lg-0,
  .gy-lg-0 {
    --mdb-gutter-y: 0;
  } // Spacing variable reset
  .g-lg-1,
  .gx-lg-1 {
    --mdb-gutter-x: 0.25rem;
  } // Spacing variable set
  .g-lg-1,
  .gy-lg-1 {
    --mdb-gutter-y: 0.25rem;
  } // Spacing variable set
  .g-lg-2,
  .gx-lg-2 {
    --mdb-gutter-x: 0.5rem;
  } // Spacing variable set
  .g-lg-2,
  .gy-lg-2 {
    --mdb-gutter-y: 0.5rem;
  } // Spacing variable set
  .g-lg-3,
  .gx-lg-3 {
    --mdb-gutter-x: 1rem;
  } // Spacing variable set
  .g-lg-3,
  .gy-lg-3 {
    --mdb-gutter-y: 1rem;
  } // Spacing variable set
  .g-lg-4,
  .gx-lg-4 {
    --mdb-gutter-x: 1.5rem;
  } // Spacing variable set
  .g-lg-4,
  .gy-lg-4 {
    --mdb-gutter-y: 1.5rem;
  } // Spacing variable set
  .g-lg-5,
  .gx-lg-5 {
    --mdb-gutter-x: 3rem;
  } // Spacing variable set
  .g-lg-5,
  .gy-lg-5 {
    --mdb-gutter-y: 3rem;
  } // Spacing variable set
}

@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  } // Flexbox
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  } // Flexbox
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  } // Flexbox
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  } // Flexbox
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  } // Flexbox
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  } // Flexbox
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  } // Flexbox
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  } // Flexbox
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  } // Flexbox
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  } // Flexbox
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  } // Flexbox
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  } // Flexbox
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  } // Flexbox
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .offset-xl-0 {
    margin-left: 0;
  } // Spacing
  .offset-xl-1 {
    margin-left: 8.33333333%;
  } // Spacing
  .offset-xl-2 {
    margin-left: 16.66666667%;
  } // Spacing
  .offset-xl-3 {
    margin-left: 25%;
  } // Spacing
  .offset-xl-4 {
    margin-left: 33.33333333%;
  } // Spacing
  .offset-xl-5 {
    margin-left: 41.66666667%;
  } // Spacing
  .offset-xl-6 {
    margin-left: 50%;
  } // Spacing
  .offset-xl-7 {
    margin-left: 58.33333333%;
  } // Spacing
  .offset-xl-8 {
    margin-left: 66.66666667%;
  } // Spacing
  .offset-xl-9 {
    margin-left: 75%;
  } // Spacing
  .offset-xl-10 {
    margin-left: 83.33333333%;
  } // Spacing
  .offset-xl-11 {
    margin-left: 91.66666667%;
  } // Spacing
  .g-xl-0,
  .gx-xl-0 {
    --mdb-gutter-x: 0;
  } // Spacing variable reset
  .g-xl-0,
  .gy-xl-0 {
    --mdb-gutter-y: 0;
  } // Spacing variable reset
  .g-xl-1,
  .gx-xl-1 {
    --mdb-gutter-x: 0.25rem;
  } // Spacing variable set
  .g-xl-1,
  .gy-xl-1 {
    --mdb-gutter-y: 0.25rem;
  } // Spacing variable set
  .g-xl-2,
  .gx-xl-2 {
    --mdb-gutter-x: 0.5rem;
  } // Spacing variable set
  .g-xl-2,
  .gy-xl-2 {
    --mdb-gutter-y: 0.5rem;
  } // Spacing variable set
  .g-xl-3,
  .gx-xl-3 {
    --mdb-gutter-x: 1rem;
  } // Spacing variable set
  .g-xl-3,
  .gy-xl-3 {
    --mdb-gutter-y: 1rem;
  } // Spacing variable set
  .g-xl-4,
  .gx-xl-4 {
    --mdb-gutter-x: 1.5rem;
  } // Spacing variable set
  .g-xl-4,
  .gy-xl-4 {
    --mdb-gutter-y: 1.5rem;
  } // Spacing variable set
  .g-xl-5,
  .gx-xl-5 {
    --mdb-gutter-x: 3rem;
  } // Spacing variable set
  .g-xl-5,
  .gy-xl-5 {
    --mdb-gutter-y: 3rem;
  } // Spacing variable set
}

@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%;
  } // Flexbox
  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  } // Flexbox
  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  } // Flexbox
  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  } // Flexbox
  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  } // Flexbox
  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  } // Flexbox
  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  } // Flexbox
  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  } // Flexbox
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  } // Flexbox
  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  } // Flexbox
  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  } // Flexbox
  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  } // Flexbox
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  } // Flexbox
  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  } // Flexbox
  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  } // Flexbox
  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  } // Flexbox
  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  } // Flexbox
  .offset-xxl-0 {
    margin-left: 0;
  } // Spacing
  .offset-xxl-1 {
    margin-left: 8.33333333%;
  } // Spacing
  .offset-xxl-2 {
    margin-left: 16.66666667%;
  } // Spacing
  .offset-xxl-3 {
    margin-left: 25%;
  } // Spacing
  .offset-xxl-4 {
    margin-left: 33.33333333%;
  } // Spacing
  .offset-xxl-5 {
    margin-left: 41.66666667%;
  } // Spacing
  .offset-xxl-6 {
    margin-left: 50%;
  } // Spacing
  .offset-xxl-7 {
    margin-left: 58.33333333%;
  } // Spacing
  .offset-xxl-8 {
    margin-left: 66.66666667%;
  } // Spacing
  .offset-xxl-9 {
    margin-left: 75%;
  } // Spacing
  .offset-xxl-10 {
    margin-left: 83.33333333%;
  } // Spacing
  .offset-xxl-11 {
    margin-left: 91.66666667%;
  } // Spacing
  .g-xxl-0,
  .gx-xxl-0 {
    --mdb-gutter-x: 0;
  } // Spacing variable reset
  .g-xxl-0,
  .gy-xxl-0 {
    --mdb-gutter-y: 0;
  } // Spacing variable reset
  .g-xxl-1,
  .gx-xxl-1 {
    --mdb-gutter-x: 0.25rem;
  } // Spacing variable set
  .g-xxl-1,
  .gy-xxl-1 {
    --mdb-gutter-y: 0.25rem;
  } // Spacing variable set
  .g-xxl-2,
  .gx-xxl-2 {
    --mdb-gutter-x: 0.5rem;
  } // Spacing variable set
  .g-xxl-2,
  .gy-xxl-2 {
    --mdb-gutter-y: 0.5rem;
  } // Spacing variable set
  .g-xxl-3,
  .gx-xxl-3 {
    --mdb-gutter-x: 1rem;
  } // Spacing variable set
  .g-xxl-3,
  .gy-xxl-3 {
    --mdb-gutter-y: 1rem;
  } // Spacing variable set
  .g-xxl-4,
  .gx-xxl-4 {
    --mdb-gutter-x: 1.5rem;
  } // Spacing variable set
  .g-xxl-4,
  .gy-xxl-4 {
    --mdb-gutter-y: 1.5rem;
  } // Spacing variable set
  .g-xxl-5,
  .gx-xxl-5 {
    --mdb-gutter-x: 3rem;
  } // Spacing variable set
  .g-xxl-5,
  .gy-xxl-5 {
    --mdb-gutter-y: 3rem;
  } // Spacing variable set
}

/*------------------------------------*\
  #TABLES (Minimal Spacing)
\*------------------------------------*/
.table {
  margin-bottom: 1rem; // Spacing
}

/*------------------------------------*\
  #FORMS (Spacing & Layout)
\*------------------------------------*/
.form-label {
  margin-bottom: 0.5rem; // Spacing
}

.col-form-label {
  padding-top: calc(0.375rem + 1px); // Spacing
  padding-bottom: calc(0.375rem + 1px); // Spacing
  margin-bottom: 0; // Spacing
  line-height: 1.6; // Spacing related
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px); // Spacing
  padding-bottom: calc(0.5rem + 1px); // Spacing
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px); // Spacing
  padding-bottom: calc(0.25rem + 1px); // Spacing
}

.form-text {
  margin-top: 0.25rem; // Spacing
}

.form-control {
  padding: 0.375rem 0.75rem; // Spacing
  line-height: 1.6; // Spacing related
}

.form-control[type="file"] {
  overflow: hidden; // Layout/Spacing related
}

.form-control::file-selector-button {
  padding: 0.375rem 0.75rem; // Spacing
  margin: -0.375rem -0.75rem; // Spacing (negative margin)
  margin-inline-end: 0.75rem; // Spacing
}
.form-control::-webkit-file-upload-button {
  padding: 0.375rem 0.75rem; // Spacing
  margin: -0.375rem -0.75rem; // Spacing (negative margin)
  margin-inline-end: 0.75rem; // Spacing
}

.form-control-plaintext {
  padding: 0.375rem 0; // Spacing
  margin-bottom: 0; // Spacing
  line-height: 1.6; // Spacing related
}
.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
  padding-right: 0; // Spacing
  padding-left: 0; // Spacing
}

.form-control-sm {
  padding: 0.25rem 0.5rem; // Spacing
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem; // Spacing
  margin: -0.25rem -0.5rem; // Spacing (negative margin)
  margin-inline-end: 0.5rem; // Spacing
}
.form-control-sm::-webkit-file-upload-button {
  padding: 0.25rem 0.5rem; // Spacing
  margin: -0.25rem -0.5rem; // Spacing (negative margin)
  margin-inline-end: 0.5rem; // Spacing
}

.form-control-lg {
  padding: 0.5rem 1rem; // Spacing
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem; // Spacing
  margin: -0.5rem -1rem; // Spacing (negative margin)
  margin-inline-end: 1rem; // Spacing
}
.form-control-lg::-webkit-file-upload-button {
  padding: 0.5rem 1rem; // Spacing
  margin: -0.5rem -1rem; // Spacing (negative margin)
  margin-inline-end: 1rem; // Spacing
}

.form-control-color {
  padding: 0.375rem; // Spacing
}

.form-select {
  padding: 0.375rem 2.25rem 0.375rem 0.75rem; // Spacing
  -moz-padding-start: calc(0.75rem - 3px); // Spacing (vendor specific)
  line-height: 1.6; // Spacing related
  background-position: right 0.75rem center; // Spacing related (icon position)
}

.form-select[multiple],
.form-select[size]:not([size="1"]) {
  padding-right: 0.75rem; // Spacing
}

.form-select-sm {
  padding-top: 0.25rem; // Spacing
  padding-bottom: 0.25rem; // Spacing
  padding-left: 0.5rem; // Spacing
}

.form-select-lg {
  padding-top: 0.5rem; // Spacing
  padding-bottom: 0.5rem; // Spacing
  padding-left: 1rem; // Spacing
}

.form-check {
  display: block; // Layout related
  padding-left: 1.5em; // Spacing
  margin-bottom: 0.125rem; // Spacing
}
.form-check .form-check-input {
  float: left; // Layout related
  margin-left: -1.5em; // Spacing (negative margin)
}
.form-check-input {
  margin-top: 0.3em; // Spacing
}

.form-switch {
  padding-left: 2.5em; // Spacing
}
.form-switch .form-check-input {
  margin-left: -2.5em; // Spacing (negative margin)
  background-position: 0; // Layout related
}
.form-switch .form-check-input:checked {
  background-position: 100%; // Layout related
}

.form-check-inline {
  display: inline-block; // Layout related
  margin-right: 1rem; // Spacing
}

.form-range {
  height: 1.5rem; // Sizing/Spacing
  padding: 0; // Spacing reset
}
.form-range::-webkit-slider-thumb {
  margin-top: -0.25rem; // Spacing (negative margin)
}

.form-floating {
  position: relative; // Needed for absolute label positioning
}
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px); // Sizing/Spacing
  line-height: 1.25; // Spacing related
}
.form-floating > label {
  position: absolute; // Positioning
  top: 0; // Positioning
  left: 0; // Positioning
  height: 100%; // Sizing
  padding: 1rem 0.75rem; // Spacing
  transform-origin: 0 0; // Positioning anchor
}
.form-floating > .form-control {
  padding: 1rem 0.75rem; // Spacing
}
.form-floating > .form-control:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem; // Spacing adjustment
  padding-bottom: 0.625rem; // Spacing adjustment
}
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem; // Spacing adjustment
  padding-bottom: 0.625rem; // Spacing adjustment
}
.form-floating > .form-control:-webkit-autofill {
  padding-top: 1.625rem; // Spacing adjustment
  padding-bottom: 0.625rem; // Spacing adjustment
}
.form-floating > .form-select {
  padding-top: 1.625rem; // Spacing adjustment
  padding-bottom: 0.625rem; // Spacing adjustment
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem); // Positioning/Spacing
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem); // Positioning/Spacing
}
.form-floating > .form-control:-webkit-autofill ~ label {
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem); // Positioning/Spacing
}

/*------------------------------------*\
  #INPUT GROUP (Flexbox & Spacing)
\*------------------------------------*/
.input-group {
  position: relative; // Needed for z-index layering
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  align-items: stretch; // Flexbox
}
.input-group > .form-control,
.input-group > .form-select {
  position: relative; // Needed for z-index layering
  flex: 1 1 auto; // Flexbox
}
.input-group .btn {
  position: relative; // Needed for z-index layering
}
.input-group-text {
  display: flex; // Flexbox
  align-items: center; // Flexbox
  padding: 0.375rem 0.75rem; // Spacing
  line-height: 1.6; // Spacing related
}
.input-group-lg > .btn,
.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text {
  padding: 0.5rem 1rem; // Spacing
}
.input-group-sm > .btn,
.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text {
  padding: 0.25rem 0.5rem; // Spacing
}
.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 3rem; // Spacing (adjust for icon)
}
.input-group
  > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
    .valid-feedback
  ):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px; // Spacing (negative margin overlap)
}

/* Validation Spacing */
.valid-feedback {
  margin-top: 0.25rem; // Spacing
}
.form-control.is-valid,
.was-validated .form-control:valid {
  padding-right: calc(1.6em + 0.75rem); // Spacing (make room for icon)
  background-position: right calc(0.4em + 0.1875rem) center; // Layout
}
.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
  padding-right: calc(1.6em + 0.75rem); // Spacing (make room for icon)
  background-position: top calc(0.4em + 0.1875rem) right calc(0.4em + 0.1875rem); // Layout
}
.form-select.is-valid:not([multiple]):not([size]),
.form-select.is-valid:not([multiple])[size="1"],
.was-validated .form-select:valid:not([multiple]):not([size]),
.was-validated .form-select:valid:not([multiple])[size="1"] {
  padding-right: 4.125rem; // Spacing (make room for icons)
  background-position: right 0.75rem center, center right 2.25rem; // Layout
}
.invalid-feedback {
  margin-top: 0.25rem; // Spacing
}
.form-control.is-invalid,
.was-validated .form-control:invalid {
  padding-right: calc(1.6em + 0.75rem); // Spacing (make room for icon)
  background-position: right calc(0.4em + 0.1875rem) center; // Layout
}
.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
  padding-right: calc(1.6em + 0.75rem); // Spacing (make room for icon)
  background-position: top calc(0.4em + 0.1875rem) right calc(0.4em + 0.1875rem); // Layout
}
.form-select.is-invalid:not([multiple]):not([size]),
.form-select.is-invalid:not([multiple])[size="1"],
.was-validated .form-select:invalid:not([multiple]):not([size]),
.was-validated .form-select:invalid:not([multiple])[size="1"] {
  padding-right: 4.125rem; // Spacing (make room for icons)
  background-position: right 0.75rem center, center right 2.25rem; // Layout
}

/*------------------------------------*\
  #BUTTONS (Spacing & Minimal Layout)
\*------------------------------------*/
.btn {
  display: inline-block; // Layout related
  padding: 0.375rem 0.75rem; // Spacing
}

.btn-group-lg > .btn,
.btn-lg {
  padding: 0.5rem 1rem; // Spacing
}

.btn-group-sm > .btn,
.btn-sm {
  padding: 0.25rem 0.5rem; // Spacing
}

/*------------------------------------*\
  #DROPDOWNS (Spacing & Positioning)
\*------------------------------------*/
.dropdown,
.dropend,
.dropstart,
.dropup {
  position: relative; // Positioning context
}

.dropdown-toggle:after {
  margin-left: 0.255em; // Spacing
  vertical-align: 0.255em; // Alignment
}
.dropdown-toggle:empty:after {
  margin-left: 0; // Spacing reset
}

.dropdown-menu {
  position: absolute; // Positioning
  min-width: 10rem; // Sizing
  padding: 0.5rem 0; // Spacing
  margin: 0; // Spacing reset MDB
}

.dropdown-menu[data-mdb-popper] {
  top: 100%; // Positioning
  left: 0; // Positioning
  margin-top: 0.125rem; // Spacing
}

.dropdown-menu-start[data-mdb-popper] {
  right: auto; // Positioning
  left: 0; // Positioning
}

.dropdown-menu-end[data-mdb-popper] {
  right: 0; // Positioning
  left: auto; // Positioning
}

// Responsive Dropdown Positions
@media (min-width: 576px) {
  .dropdown-menu-sm-start[data-mdb-popper] {
    right: auto;
    left: 0;
  } // Positioning
  .dropdown-menu-sm-end[data-mdb-popper] {
    right: 0;
    left: auto;
  } // Positioning
}
@media (min-width: 768px) {
  .dropdown-menu-md-start[data-mdb-popper] {
    right: auto;
    left: 0;
  } // Positioning
  .dropdown-menu-md-end[data-mdb-popper] {
    right: 0;
    left: auto;
  } // Positioning
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start[data-mdb-popper] {
    right: auto;
    left: 0;
  } // Positioning
  .dropdown-menu-lg-end[data-mdb-popper] {
    right: 0;
    left: auto;
  } // Positioning
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start[data-mdb-popper] {
    right: auto;
    left: 0;
  } // Positioning
  .dropdown-menu-xl-end[data-mdb-popper] {
    right: 0;
    left: auto;
  } // Positioning
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start[data-mdb-popper] {
    right: auto;
    left: 0;
  } // Positioning
  .dropdown-menu-xxl-end[data-mdb-popper] {
    right: 0;
    left: auto;
  } // Positioning
}

.dropup .dropdown-menu[data-mdb-popper] {
  top: auto; // Positioning
  bottom: 100%; // Positioning
  margin-top: 0; // Spacing reset
  margin-bottom: 0.125rem; // Spacing
}
.dropup .dropdown-toggle:after {
  margin-left: 0.255em; // Spacing
  vertical-align: 0.255em; // Alignment
}
.dropup .dropdown-toggle:empty:after {
  margin-left: 0; // Spacing reset
}

.dropend .dropdown-menu[data-mdb-popper] {
  top: 0; // Positioning
  right: auto; // Positioning
  left: 100%; // Positioning
  margin-top: 0; // Spacing reset
  margin-left: 0.125rem; // Spacing
}
.dropend .dropdown-toggle:after {
  margin-left: 0.255em; // Spacing
  vertical-align: 0.255em; // Alignment
}
.dropend .dropdown-toggle:empty:after {
  margin-left: 0; // Spacing reset
}
.dropend .dropdown-toggle:after {
  vertical-align: 0; // Alignment reset
}

.dropstart .dropdown-menu[data-mdb-popper] {
  top: 0; // Positioning
  right: 100%; // Positioning
  left: auto; // Positioning
  margin-top: 0; // Spacing reset
  margin-right: 0.125rem; // Spacing
}
.dropstart .dropdown-toggle:before {
  margin-right: 0.255em; // Spacing
  vertical-align: 0.255em; // Alignment
}
.dropstart .dropdown-toggle:empty:after {
  margin-left: 0; // Spacing reset
}
.dropstart .dropdown-toggle:before {
  vertical-align: 0; // Alignment reset
}

.dropdown-divider {
  height: 0; // Sizing/Spacing
  margin: 0.5rem 0; // Spacing
}

.dropdown-item {
  padding: 0.5rem 1rem; // Spacing MDB
}

.dropdown-header {
  padding: 0.5rem 1rem; // Spacing
  margin-bottom: 0; // Spacing reset
}

.dropdown-item-text {
  padding: 0.5rem 1rem; // Spacing MDB
}

/*------------------------------------*\
  #BUTTON GROUP (Flexbox & Spacing)
\*------------------------------------*/
.btn-group,
.btn-group-vertical {
  position: relative; // Positioning context
  display: inline-flex; // Flexbox
  vertical-align: middle; // Alignment
}

.btn-group-vertical > .btn,
.btn-group > .btn {
  position: relative; // Positioning context for z-index
  flex: 1 1 auto; // Flexbox
}

.btn-toolbar {
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  justify-content: flex-start; // Flexbox
}
.btn-toolbar .input-group {
  width: auto; // Layout override for toolbar context
}

.btn-group > .btn-group:not(:first-child),
.btn-group > .btn:not(:first-child) {
  margin-left: -0.125rem; // Spacing (negative margin overlap)
}

.dropdown-toggle-split {
  padding-right: 0.5625rem; // Spacing
  padding-left: 0.5625rem; // Spacing
}
.dropdown-toggle-split:after,
.dropend .dropdown-toggle-split:after,
.dropup .dropdown-toggle-split:after {
  margin-left: 0; // Spacing reset
}
.dropstart .dropdown-toggle-split:before {
  margin-right: 0; // Spacing reset
}
.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
  padding-right: 0.375rem; // Spacing
  padding-left: 0.375rem; // Spacing
}
.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
  padding-right: 0.75rem; // Spacing
  padding-left: 0.75rem; // Spacing
}

.btn-group-vertical {
  flex-direction: column; // Flexbox
  align-items: flex-start; // Flexbox
  justify-content: center; // Flexbox
}
.btn-group-vertical > .btn-group:not(:first-child),
.btn-group-vertical > .btn:not(:first-child) {
  margin-top: -0.125rem; // Spacing (negative margin overlap)
}

/*------------------------------------*\
  #NAV (Flexbox & Spacing)
\*------------------------------------*/
.nav {
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  padding-left: 0; // Spacing reset
  margin-bottom: 0; // Spacing reset
}

.nav-link {
  display: block; // Layout related
  padding: 0.5rem 1rem; // Spacing
}

.nav-tabs .nav-link {
  margin-bottom: -1px; // Spacing (negative margin overlap)
}

.nav-tabs .dropdown-menu {
  margin-top: -1px; // Spacing (negative margin overlap)
}

.nav-fill .nav-item,
.nav-fill > .nav-link {
  flex: 1 1 auto; // Flexbox
}

.nav-justified .nav-item,
.nav-justified > .nav-link {
  flex-basis: 0; // Flexbox
  flex-grow: 1; // Flexbox
}

/*------------------------------------*\
  #NAVBAR (Flexbox & Spacing)
\*------------------------------------*/
.navbar {
  position: relative; // Positioning context
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  align-items: center; // Flexbox
  justify-content: space-between; // Flexbox
  padding-top: 0.5rem; // Spacing MDB
  padding-bottom: 0.5rem; // Spacing
}

.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-lg,
.navbar > .container-md,
.navbar > .container-sm,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: flex; // Flexbox
  flex-wrap: inherit; // Flexbox
  align-items: center; // Flexbox
  justify-content: space-between; // Flexbox
}

.navbar-brand {
  padding-top: 0.3rem; // Spacing MDB
  padding-bottom: 0.3rem; // Spacing MDB
  margin-right: 1rem; // Spacing
  display: flex; // MDB - Align items inside brand
  align-items: center; // MDB - Align items inside brand
}
.navbar-brand img {
  margin-right: 0.25rem; // MDB Spacing
}

.navbar-nav {
  display: flex; // Flexbox
  flex-direction: column; // Flexbox (default mobile)
  padding-left: 0; // Spacing reset
  margin-bottom: 0; // Spacing reset
}
.navbar-nav .nav-link {
  padding-right: 0; // Spacing reset (mobile)
  padding-left: 0; // Spacing reset (mobile)
}
.navbar-nav .dropdown-menu {
  position: static; // Positioning override (mobile)
}

.navbar-text {
  padding-top: 0.5rem; // Spacing
  padding-bottom: 0.5rem; // Spacing
}

.navbar-collapse {
  flex-basis: 100%; // Flexbox (mobile)
  flex-grow: 1; // Flexbox (mobile)
  align-items: center; // Flexbox (mobile)
}

.navbar-toggler {
  padding: 0.25rem 0.75rem; // Spacing
}

// Responsive Navbar Expansions
@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  } // Flexbox
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  } // Flexbox
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  } // Positioning
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  } // Spacing
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  } // Flexbox
  .navbar-expand-sm .offcanvas {
    flex-grow: 1;
  } // Flexbox
  .navbar-expand-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
  } // Flexbox, Spacing
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  } // Flexbox
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  } // Flexbox
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  } // Positioning
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  } // Spacing
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  } // Flexbox
  .navbar-expand-md .offcanvas {
    flex-grow: 1;
  } // Flexbox
  .navbar-expand-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
  } // Flexbox, Spacing
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  } // Flexbox
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  } // Flexbox
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  } // Positioning
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  } // Spacing
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  } // Flexbox
  .navbar-expand-lg .offcanvas {
    flex-grow: 1;
  } // Flexbox
  .navbar-expand-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
  } // Flexbox, Spacing
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  } // Flexbox
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  } // Flexbox
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  } // Positioning
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  } // Spacing
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  } // Flexbox
  .navbar-expand-xl .offcanvas {
    flex-grow: 1;
  } // Flexbox
  .navbar-expand-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
  } // Flexbox, Spacing
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  } // Flexbox
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  } // Flexbox
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  } // Positioning
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  } // Spacing
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  } // Flexbox
  .navbar-expand-xxl .offcanvas {
    flex-grow: 1;
  } // Flexbox
  .navbar-expand-xxl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
  } // Flexbox, Spacing
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
} // Flexbox
.navbar-expand .navbar-nav {
  flex-direction: row;
} // Flexbox
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
} // Positioning
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
} // Spacing
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
} // Flexbox
.navbar-expand .offcanvas {
  flex-grow: 1;
} // Flexbox
.navbar-expand .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
} // Flexbox, Spacing

/*------------------------------------*\
  #BREADCRUMB (Flexbox & Spacing)
\*------------------------------------*/
.breadcrumb {
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  padding: 0; // Spacing reset
  margin-bottom: 1rem; // Spacing
}
.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem; // Spacing
}
.breadcrumb-item + .breadcrumb-item:before {
  float: left; // Layout related
  padding-right: 0.5rem; // Spacing
}

/*------------------------------------*\
  #PAGINATION (Flexbox & Spacing)
\*------------------------------------*/
.pagination {
  display: flex; // Flexbox
  padding-left: 0; // Spacing reset
}
.page-link {
  display: block; // Layout related
}
.page-item:not(:first-child) .page-link {
  margin-left: -1px; // Spacing (negative margin overlap)
}
.page-link {
  padding: 0.375rem 0.75rem; // Spacing
}
.pagination-lg .page-link {
  padding: 0.75rem 1.5rem; // Spacing
}
.pagination-sm .page-link {
  padding: 0.25rem 0.5rem; // Spacing
}

/*------------------------------------*\
  #BADGE (Spacing)
\*------------------------------------*/
.badge {
  display: inline-block; // Layout related
  padding: 0.35em 0.65em; // Spacing
}
.btn .badge {
  position: relative; // Positioning context
  top: -1px; // Positioning/Spacing
}

/*------------------------------------*\
  #ALERT (Spacing)
\*------------------------------------*/
.alert {
  padding: 1.25rem 1.5rem; // Spacing MDB
  margin-bottom: 1rem; // Spacing
}
.alert-dismissible {
  padding-right: 4.5rem; // Spacing (make room for close button) MDB
}
.alert-dismissible .btn-close {
  padding: 1.5625rem 1.5rem; // Spacing MDB
}

/*------------------------------------*\
  #ACCORDION (Flexbox & Spacing)
\*------------------------------------*/
// .accordion-button {
//   display: flex; // Flexbox
//   align-items: center; // Flexbox
//   padding: 1.15rem 1.5rem; // Spacing MDB
// }
// .accordion-button:after {
//   margin-left: auto; // Flexbox Spacing
// }
// .accordion-header {
//   margin-bottom: 0; // Spacing reset
// }
// .accordion-body {
//   padding: 1.15rem 1.5rem; // Spacing MDB
// }

/*------------------------------------*\
  #PROGRESS (Flexbox & Spacing)
\*------------------------------------*/
.progress {
  height: 4px; // Sizing/Spacing MDB
}
.progress,
.progress-bar {
  display: flex; // Flexbox
  overflow: hidden; // Layout related
}
.progress-bar {
  flex-direction: column; // Flexbox
  justify-content: center; // Flexbox
}

/*------------------------------------*\
  #LIST GROUP (Flexbox & Spacing)
\*------------------------------------*/
.list-group {
  display: flex; // Flexbox
  flex-direction: column; // Flexbox
  padding-left: 0; // Spacing reset
  margin-bottom: 0; // Spacing reset
}
.list-group-item {
  padding: 0.5rem 1.5rem; // Spacing MDB
}
.list-group-item + .list-group-item.active {
  margin-top: -1px; // Spacing (negative margin overlap)
}
.list-group-horizontal {
  flex-direction: row; // Flexbox
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0; // Spacing reset for horizontal
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px; // Spacing (negative margin overlap)
}

// Responsive List Group Horizontal
@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  } // Flexbox
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  } // Spacing reset
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  } // Spacing
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  } // Flexbox
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  } // Spacing reset
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  } // Spacing
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  } // Flexbox
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  } // Spacing reset
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  } // Spacing
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  } // Flexbox
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  } // Spacing reset
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  } // Spacing
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    flex-direction: row;
  } // Flexbox
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  } // Spacing reset
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  } // Spacing
}

/*------------------------------------*\
  #CLOSE BUTTON (Spacing)
\*------------------------------------*/
.btn-close {
  padding: 0.25em; // Spacing
}

/*------------------------------------*\
  #TOAST (Flexbox & Spacing)
\*------------------------------------*/
.toast-container > :not(:last-child) {
  margin-bottom: 0.75rem; // Spacing
}
.toast-header {
  display: flex; // Flexbox
  align-items: center; // Flexbox
  padding: 0.5rem 0.75rem; // Spacing
}
.toast-header .btn-close {
  margin-right: -0.375rem; // Spacing (negative margin)
  margin-left: 0.75rem; // Spacing
}
.toast-body {
  padding: 0.75rem; // Spacing
}

/*------------------------------------*\
  #MODAL (Flexbox & Spacing)
\*------------------------------------*/
.modal-dialog {
  margin: 0.5rem; // Spacing
}
.modal-dialog-centered {
  display: flex; // Flexbox
  align-items: center; // Flexbox
}
.modal-content {
  display: flex; // Flexbox
  flex-direction: column; // Flexbox
}
.modal-header {
  display: flex; // Flexbox
  flex-shrink: 0; // Flexbox
  align-items: center; // Flexbox
  justify-content: space-between; // Flexbox
  padding: 1rem; // Spacing
}
.modal-header .btn-close {
  padding: 0.5rem; // Spacing
  margin: -0.5rem -0.5rem -0.5rem auto; // Spacing (negative margins)
}
.modal-title {
  margin-bottom: 0; // Spacing reset
  line-height: 1.6; // Spacing related
}
.modal-body {
  flex: 1 1 auto; // Flexbox
  padding: 1rem; // Spacing
}
.modal-footer {
  display: flex; // Flexbox
  flex-wrap: wrap; // Flexbox
  flex-shrink: 0; // Flexbox
  align-items: center; // Flexbox
  justify-content: flex-end; // Flexbox
  padding: 0.75rem; // Spacing
}
.modal-footer > * {
  margin: 0.25rem; // Spacing
}
@media (min-width: 576px) {
  .modal-dialog {
    margin: 1.75rem auto; // Spacing
  }
}

/*------------------------------------*\
  #TOOLTIP & POPOVER (Spacing)
\*------------------------------------*/
.tooltip {
  margin: 0; // Spacing reset
}
.tooltip .tooltip-arrow {
  width: 0.8rem; // Sizing/Spacing
  height: 0.4rem; // Sizing/Spacing
}
.bs-tooltip-auto[data-popper-placement^="top"],
.bs-tooltip-top {
  padding: 0.4rem 0; // Spacing
}
.bs-tooltip-auto[data-popper-placement^="right"],
.bs-tooltip-end {
  padding: 0 0.4rem; // Spacing
}
.bs-tooltip-auto[data-popper-placement^="bottom"],
.bs-tooltip-bottom {
  padding: 0.4rem 0; // Spacing
}
.bs-tooltip-auto[data-popper-placement^="left"],
.bs-tooltip-start {
  padding: 0 0.4rem; // Spacing
}
.tooltip-inner {
  padding: 0.25rem 0.5rem; // Spacing MDB
}

.popover {
  margin: 0; // Spacing reset MDB
}
.popover .popover-arrow {
  width: 1rem; // Sizing/Spacing
  height: 0.5rem; // Sizing/Spacing
}
.popover-header {
  padding: 0.5rem 1rem; // Spacing
  margin-bottom: 0; // Spacing reset
}
.popover-body {
  padding: 1rem; // Spacing
}

/*------------------------------------*\
  #CAROUSEL (Spacing & Positioning)
\*------------------------------------*/
.carousel-item {
  float: left; // Layout related
  margin-right: -100%; // Layout related (trick for sliding)
}
.carousel-control-next,
.carousel-control-prev {
  display: flex; // Flexbox
  align-items: center; // Flexbox
  justify-content: center; // Flexbox
  padding: 0; // Spacing reset
}
.carousel-indicators {
  display: flex; // Flexbox
  justify-content: center; // Flexbox
  padding: 0; // Spacing reset
  margin-right: 15%; // Spacing
  margin-bottom: 1rem; // Spacing
  margin-left: 15%; // Spacing
}
.carousel-indicators [data-mdb-target] {
  padding: 0; // Spacing reset
  margin-right: 3px; // Spacing
  margin-left: 3px; // Spacing
}
.carousel-caption {
  padding-top: 1.25rem; // Spacing
  padding-bottom: 1.25rem; // Spacing
}

/*------------------------------------*\
  #SPINNER (Minimal Spacing)
\*------------------------------------*/
.spinner-border {
  vertical-align: -0.125em; // Alignment/Spacing
}
.spinner-grow {
  vertical-align: -0.125em; // Alignment/Spacing
}

/*------------------------------------*\
  #OFFCANVAS (Flexbox & Spacing)
\*------------------------------------*/
.offcanvas {
  display: flex; // Flexbox
  flex-direction: column; // Flexbox
}
.offcanvas-header {
  display: flex; // Flexbox
  align-items: center; // Flexbox
  justify-content: space-between; // Flexbox
  padding: 1rem; // Spacing
}
.offcanvas-header .btn-close {
  padding: 0.5rem; // Spacing
  margin-top: -0.5rem; // Spacing (negative margin)
  margin-right: -0.5rem; // Spacing (negative margin)
  margin-bottom: -0.5rem; // Spacing (negative margin)
}
.offcanvas-title {
  margin-bottom: 0; // Spacing reset
  line-height: 1.6; // Spacing related
}
.offcanvas-body {
  flex-grow: 1; // Flexbox
  padding: 1rem; // Spacing
}

/*------------------------------------*\
  #STACKS (Flexbox & Gap)
\*------------------------------------*/
.hstack {
  flex-direction: row; // Flexbox
  align-items: center; // Flexbox
}
.hstack,
.vstack {
  display: flex; // Flexbox
  align-self: stretch; // Flexbox
}
.vstack {
  flex: 1 1 auto; // Flexbox
  flex-direction: column; // Flexbox
}

/*------------------------------------*\
  #FLEX UTILITIES
\*------------------------------------*/
.d-flex {
  display: flex !important;
}
.d-inline-flex {
  display: inline-flex !important;
}
.flex-fill {
  flex: 1 1 auto !important;
}
.flex-row {
  flex-direction: row !important;
}
.flex-column {
  flex-direction: column !important;
}
.flex-row-reverse {
  flex-direction: row-reverse !important;
}
.flex-column-reverse {
  flex-direction: column-reverse !important;
}
.flex-grow-0 {
  flex-grow: 0 !important;
}
.flex-grow-1 {
  flex-grow: 1 !important;
}
.flex-shrink-0 {
  flex-shrink: 0 !important;
}
.flex-shrink-1 {
  flex-shrink: 1 !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.justify-content-start {
  justify-content: flex-start !important;
}
.justify-content-end {
  justify-content: flex-end !important;
}
.justify-content-center {
  justify-content: center !important;
}
.justify-content-between {
  justify-content: space-between !important;
}
.justify-content-around {
  justify-content: space-around !important;
}
.justify-content-evenly {
  justify-content: space-evenly !important;
}
.align-items-start {
  align-items: flex-start !important;
}
.align-items-end {
  align-items: flex-end !important;
}
.align-items-center {
  align-items: center !important;
}
.align-items-baseline {
  align-items: baseline !important;
}
.align-items-stretch {
  align-items: stretch !important;
}
.align-content-start {
  align-content: flex-start !important;
}
.align-content-end {
  align-content: flex-end !important;
}
.align-content-center {
  align-content: center !important;
}
.align-content-between {
  align-content: space-between !important;
}
.align-content-around {
  align-content: space-around !important;
}
.align-content-stretch {
  align-content: stretch !important;
}
.align-self-auto {
  align-self: auto !important;
}
.align-self-start {
  align-self: flex-start !important;
}
.align-self-end {
  align-self: flex-end !important;
}
.align-self-center {
  align-self: center !important;
}
.align-self-baseline {
  align-self: baseline !important;
}
.align-self-stretch {
  align-self: stretch !important;
}
.order-first {
  order: -1 !important;
}
.order-0 {
  order: 0 !important;
}
.order-1 {
  order: 1 !important;
}
.order-2 {
  order: 2 !important;
}
.order-3 {
  order: 3 !important;
}
.order-4 {
  order: 4 !important;
}
.order-5 {
  order: 5 !important;
}
.order-last {
  order: 6 !important;
}

/*------------------------------------*\
  #SPACING UTILITIES (Margin, Padding, Gap)
\*------------------------------------*/
.gap-0 {
  gap: 0 !important;
}
.gap-1 {
  gap: 0.25rem !important;
}
.gap-2 {
  gap: 0.5rem !important;
}
.gap-3 {
  gap: 1rem !important;
}
.gap-4 {
  gap: 1.5rem !important;
}
.gap-5 {
  gap: 3rem !important;
}

// Additional gap utilities for better spacing control
.gap-6 {
  gap: 4rem !important;
}
.gap-7 {
  gap: 5rem !important;
}

// Row and column gap utilities
.row-gap-0 {
  row-gap: 0 !important;
}
.row-gap-1 {
  row-gap: 0.25rem !important;
}
.row-gap-2 {
  row-gap: 0.5rem !important;
}
.row-gap-3 {
  row-gap: 1rem !important;
}
.row-gap-4 {
  row-gap: 1.5rem !important;
}
.row-gap-5 {
  row-gap: 3rem !important;
}

.column-gap-0 {
  column-gap: 0 !important;
}
.column-gap-1 {
  column-gap: 0.25rem !important;
}
.column-gap-2 {
  column-gap: 0.5rem !important;
}
.column-gap-3 {
  column-gap: 1rem !important;
}
.column-gap-4 {
  column-gap: 1.5rem !important;
}
.column-gap-5 {
  column-gap: 3rem !important;
}

.m-0 {
  margin: 0 !important;
}
.m-1 {
  margin: 0.25rem !important;
}
.m-2 {
  margin: 0.5rem !important;
}
.m-3 {
  margin: 1rem !important;
}
.m-4 {
  margin: 1.5rem !important;
}
.m-5 {
  margin: 3rem !important;
}
.m-auto {
  margin: auto !important;
}
.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}
.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}
.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}
.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}
.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}
.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}
.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}
.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}
.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}
.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}
.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}
.mt-0 {
  margin-top: 0 !important;
}
.mt-1 {
  margin-top: 0.25rem !important;
}
.mt-2 {
  margin-top: 0.5rem !important;
}
.mt-3 {
  margin-top: 1rem !important;
}
.mt-4 {
  margin-top: 1.5rem !important;
}
.mt-5 {
  margin-top: 3rem !important;
}
.mt-auto {
  margin-top: auto !important;
}
.me-0 {
  margin-right: 0 !important;
}
.me-1 {
  margin-right: 0.25rem !important;
}
.me-2 {
  margin-right: 0.5rem !important;
}
.me-3 {
  margin-right: 1rem !important;
}
.me-4 {
  margin-right: 1.5rem !important;
}
.me-5 {
  margin-right: 3rem !important;
}
.me-auto {
  margin-right: auto !important;
}
.mb-0 {
  margin-bottom: 0 !important;
}
.mb-1 {
  margin-bottom: 0.25rem !important;
}
.mb-2 {
  margin-bottom: 0.5rem !important;
}
.mb-3 {
  margin-bottom: 1rem !important;
}
.mb-4 {
  margin-bottom: 1.5rem !important;
}
.mb-5 {
  margin-bottom: 3rem !important;
}
.mb-auto {
  margin-bottom: auto !important;
}
// MDB Specific Margin Bottom
.mb-6 {
  margin-bottom: 3.5rem !important;
}
.mb-7 {
  margin-bottom: 4rem !important;
}
.mb-8 {
  margin-bottom: 5rem !important;
}
.mb-9 {
  margin-bottom: 6rem !important;
}
.mb-10 {
  margin-bottom: 8rem !important;
}
.mb-11 {
  margin-bottom: 10rem !important;
}
.mb-12 {
  margin-bottom: 12rem !important;
}
.mb-13 {
  margin-bottom: 14rem !important;
}
.mb-14 {
  margin-bottom: 16rem !important;
}
.ms-0 {
  margin-left: 0 !important;
}
.ms-1 {
  margin-left: 0.25rem !important;
}
.ms-2 {
  margin-left: 0.5rem !important;
}
.ms-3 {
  margin-left: 1rem !important;
}
.ms-4 {
  margin-left: 1.5rem !important;
}
.ms-5 {
  margin-left: 3rem !important;
}
.ms-auto {
  margin-left: auto !important;
}

// Negative Margin Utilities
.m-n1 {
  margin: -0.25rem !important;
}
.m-n2 {
  margin: -0.5rem !important;
}
.m-n3 {
  margin: -1rem !important;
}
.m-n4 {
  margin: -1.5rem !important;
}
.m-n5 {
  margin: -3rem !important;
}
.mx-n1 {
  margin-right: -0.25rem !important;
  margin-left: -0.25rem !important;
}
.mx-n2 {
  margin-right: -0.5rem !important;
  margin-left: -0.5rem !important;
}
.mx-n3 {
  margin-right: -1rem !important;
  margin-left: -1rem !important;
}
.mx-n4 {
  margin-right: -1.5rem !important;
  margin-left: -1.5rem !important;
}
.mx-n5 {
  margin-right: -3rem !important;
  margin-left: -3rem !important;
}
.my-n1 {
  margin-top: -0.25rem !important;
  margin-bottom: -0.25rem !important;
}
.my-n2 {
  margin-top: -0.5rem !important;
  margin-bottom: -0.5rem !important;
}
.my-n3 {
  margin-top: -1rem !important;
  margin-bottom: -1rem !important;
}
.my-n4 {
  margin-top: -1.5rem !important;
  margin-bottom: -1.5rem !important;
}
.my-n5 {
  margin-top: -3rem !important;
  margin-bottom: -3rem !important;
}
.mt-n1 {
  margin-top: -0.25rem !important;
}
.mt-n2 {
  margin-top: -0.5rem !important;
}
.mt-n3 {
  margin-top: -1rem !important;
}
.mt-n4 {
  margin-top: -1.5rem !important;
}
.mt-n5 {
  margin-top: -3rem !important;
}
.me-n1 {
  margin-right: -0.25rem !important;
}
.me-n2 {
  margin-right: -0.5rem !important;
}
.me-n3 {
  margin-right: -1rem !important;
}
.me-n4 {
  margin-right: -1.5rem !important;
}
.me-n5 {
  margin-right: -3rem !important;
}
.mb-n1 {
  margin-bottom: -0.25rem !important;
}
.mb-n2 {
  margin-bottom: -0.5rem !important;
}
.mb-n3 {
  margin-bottom: -1rem !important;
}
.mb-n4 {
  margin-bottom: -1.5rem !important;
}
.mb-n5 {
  margin-bottom: -3rem !important;
}
.ms-n1 {
  margin-left: -0.25rem !important;
}
.ms-n2 {
  margin-left: -0.5rem !important;
}
.ms-n3 {
  margin-left: -1rem !important;
}
.ms-n4 {
  margin-left: -1.5rem !important;
}
.ms-n5 {
  margin-left: -3rem !important;
}

.p-0 {
  padding: 0 !important;
}
.p-1 {
  padding: 0.25rem !important;
}
.p-2 {
  padding: 0.5rem !important;
}
.p-3 {
  padding: 1rem !important;
}
.p-4 {
  padding: 1.5rem !important;
}
.p-5 {
  padding: 3rem !important;
}
.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}
.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}
.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}
.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}
.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}
.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}
.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}
.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}
.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}
.pt-0 {
  padding-top: 0 !important;
}
.pt-1 {
  padding-top: 0.25rem !important;
}
.pt-2 {
  padding-top: 0.5rem !important;
}
.pt-3 {
  padding-top: 1rem !important;
}
.pt-4 {
  padding-top: 1.5rem !important;
}
.pt-5 {
  padding-top: 3rem !important;
}
.pe-0 {
  padding-right: 0 !important;
}
.pe-1 {
  padding-right: 0.25rem !important;
}
.pe-2 {
  padding-right: 0.5rem !important;
}
.pe-3 {
  padding-right: 1rem !important;
}
.pe-4 {
  padding-right: 1.5rem !important;
}
.pe-5 {
  padding-right: 3rem !important;
}
.pb-0 {
  padding-bottom: 0 !important;
}
.pb-1 {
  padding-bottom: 0.25rem !important;
}
.pb-2 {
  padding-bottom: 0.5rem !important;
}
.pb-3 {
  padding-bottom: 1rem !important;
}
.pb-4 {
  padding-bottom: 1.5rem !important;
}
.pb-5 {
  padding-bottom: 3rem !important;
}
.ps-0 {
  padding-left: 0 !important;
}
.ps-1 {
  padding-left: 0.25rem !important;
}
.ps-2 {
  padding-left: 0.5rem !important;
}
.ps-3 {
  padding-left: 1rem !important;
}
.ps-4 {
  padding-left: 1.5rem !important;
}
.ps-5 {
  padding-left: 3rem !important;
}

/*------------------------------------*\
  #RESPONSIVE FLEX & SPACING UTILITIES
\*------------------------------------*/

@media (min-width: 576px) {
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-sm-0 {
    gap: 0 !important;
  }
  .gap-sm-1 {
    gap: 0.25rem !important;
  }
  .gap-sm-2 {
    gap: 0.5rem !important;
  }
  .gap-sm-3 {
    gap: 1rem !important;
  }
  .gap-sm-4 {
    gap: 1.5rem !important;
  }
  .gap-sm-5 {
    gap: 3rem !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .order-sm-first {
    order: -1 !important;
  }
  .order-sm-0 {
    order: 0 !important;
  }
  .order-sm-1 {
    order: 1 !important;
  }
  .order-sm-2 {
    order: 2 !important;
  }
  .order-sm-3 {
    order: 3 !important;
  }
  .order-sm-4 {
    order: 4 !important;
  }
  .order-sm-5 {
    order: 5 !important;
  }
  .order-sm-last {
    order: 6 !important;
  }
  .m-sm-0 {
    margin: 0 !important;
  }
  .m-sm-1 {
    margin: 0.25rem !important;
  }
  .m-sm-2 {
    margin: 0.5rem !important;
  }
  .m-sm-3 {
    margin: 1rem !important;
  }
  .m-sm-4 {
    margin: 1.5rem !important;
  }
  .m-sm-5 {
    margin: 3rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0 !important;
  }
  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .mt-sm-5 {
    margin-top: 3rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-right: 0 !important;
  }
  .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .me-sm-3 {
    margin-right: 1rem !important;
  }
  .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .me-sm-5 {
    margin-right: 3rem !important;
  }
  .me-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .mb-sm-6 {
    margin-bottom: 3.5rem !important;
  } // MDB
  .mb-sm-7 {
    margin-bottom: 4rem !important;
  } // MDB
  .mb-sm-8 {
    margin-bottom: 5rem !important;
  } // MDB
  .mb-sm-9 {
    margin-bottom: 6rem !important;
  } // MDB
  .mb-sm-10 {
    margin-bottom: 8rem !important;
  } // MDB
  .mb-sm-11 {
    margin-bottom: 10rem !important;
  } // MDB
  .mb-sm-12 {
    margin-bottom: 12rem !important;
  } // MDB
  .mb-sm-13 {
    margin-bottom: 14rem !important;
  } // MDB
  .mb-sm-14 {
    margin-bottom: 16rem !important;
  } // MDB
  .ms-sm-0 {
    margin-left: 0 !important;
  }
  .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .ms-sm-5 {
    margin-left: 3rem !important;
  }
  .ms-sm-auto {
    margin-left: auto !important;
  }
  .m-sm-n1 {
    margin: -0.25rem !important;
  }
  .m-sm-n2 {
    margin: -0.5rem !important;
  }
  .m-sm-n3 {
    margin: -1rem !important;
  }
  .m-sm-n4 {
    margin: -1.5rem !important;
  }
  .m-sm-n5 {
    margin: -3rem !important;
  }
  .mx-sm-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }
  .mx-sm-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }
  .mx-sm-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }
  .mx-sm-n4 {
    margin-right: -1.5rem !important;
    margin-left: -1.5rem !important;
  }
  .mx-sm-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }
  .my-sm-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }
  .my-sm-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }
  .my-sm-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
  .my-sm-n4 {
    margin-top: -1.5rem !important;
    margin-bottom: -1.5rem !important;
  }
  .my-sm-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
  .mt-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .mt-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .mt-sm-n3 {
    margin-top: -1rem !important;
  }
  .mt-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .mt-sm-n5 {
    margin-top: -3rem !important;
  }
  .me-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .me-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .me-sm-n3 {
    margin-right: -1rem !important;
  }
  .me-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .me-sm-n5 {
    margin-right: -3rem !important;
  }
  .mb-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .mb-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .mb-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .mb-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .mb-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .ms-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .ms-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .ms-sm-n3 {
    margin-left: -1rem !important;
  }
  .ms-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .ms-sm-n5 {
    margin-left: -3rem !important;
  }
  .p-sm-0 {
    padding: 0 !important;
  }
  .p-sm-1 {
    padding: 0.25rem !important;
  }
  .p-sm-2 {
    padding: 0.5rem !important;
  }
  .p-sm-3 {
    padding: 1rem !important;
  }
  .p-sm-4 {
    padding: 1.5rem !important;
  }
  .p-sm-5 {
    padding: 3rem !important;
  }
  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-sm-0 {
    padding-top: 0 !important;
  }
  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .pt-sm-5 {
    padding-top: 3rem !important;
  }
  .pe-sm-0 {
    padding-right: 0 !important;
  }
  .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .pe-sm-5 {
    padding-right: 3rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }
  .ps-sm-0 {
    padding-left: 0 !important;
  }
  .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .ps-sm-5 {
    padding-left: 3rem !important;
  }
}
@media (min-width: 768px) {
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-md-0 {
    gap: 0 !important;
  }
  .gap-md-1 {
    gap: 0.25rem !important;
  }
  .gap-md-2 {
    gap: 0.5rem !important;
  }
  .gap-md-3 {
    gap: 1rem !important;
  }
  .gap-md-4 {
    gap: 1.5rem !important;
  }
  .gap-md-5 {
    gap: 3rem !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
  .order-md-first {
    order: -1 !important;
  }
  .order-md-0 {
    order: 0 !important;
  }
  .order-md-1 {
    order: 1 !important;
  }
  .order-md-2 {
    order: 2 !important;
  }
  .order-md-3 {
    order: 3 !important;
  }
  .order-md-4 {
    order: 4 !important;
  }
  .order-md-5 {
    order: 5 !important;
  }
  .order-md-last {
    order: 6 !important;
  }
  .m-md-0 {
    margin: 0 !important;
  }
  .m-md-1 {
    margin: 0.25rem !important;
  }
  .m-md-2 {
    margin: 0.5rem !important;
  }
  .m-md-3 {
    margin: 1rem !important;
  }
  .m-md-4 {
    margin: 1.5rem !important;
  }
  .m-md-5 {
    margin: 3rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-md-0 {
    margin-top: 0 !important;
  }
  .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .mt-md-3 {
    margin-top: 1rem !important;
  }
  .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .mt-md-5 {
    margin-top: 3rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-right: 0 !important;
  }
  .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .me-md-3 {
    margin-right: 1rem !important;
  }
  .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .me-md-5 {
    margin-right: 3rem !important;
  }
  .me-md-auto {
    margin-right: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-md-5 {
    margin-bottom: 3rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .mb-md-6 {
    margin-bottom: 3.5rem !important;
  } // MDB
  .mb-md-7 {
    margin-bottom: 4rem !important;
  } // MDB
  .mb-md-8 {
    margin-bottom: 5rem !important;
  } // MDB
  .mb-md-9 {
    margin-bottom: 6rem !important;
  } // MDB
  .mb-md-10 {
    margin-bottom: 8rem !important;
  } // MDB
  .mb-md-11 {
    margin-bottom: 10rem !important;
  } // MDB
  .mb-md-12 {
    margin-bottom: 12rem !important;
  } // MDB
  .mb-md-13 {
    margin-bottom: 14rem !important;
  } // MDB
  .mb-md-14 {
    margin-bottom: 16rem !important;
  } // MDB
  .ms-md-0 {
    margin-left: 0 !important;
  }
  .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .ms-md-3 {
    margin-left: 1rem !important;
  }
  .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .ms-md-5 {
    margin-left: 3rem !important;
  }
  .ms-md-auto {
    margin-left: auto !important;
  }
  .m-md-n1 {
    margin: -0.25rem !important;
  }
  .m-md-n2 {
    margin: -0.5rem !important;
  }
  .m-md-n3 {
    margin: -1rem !important;
  }
  .m-md-n4 {
    margin: -1.5rem !important;
  }
  .m-md-n5 {
    margin: -3rem !important;
  }
  .mx-md-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }
  .mx-md-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }
  .mx-md-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }
  .mx-md-n4 {
    margin-right: -1.5rem !important;
    margin-left: -1.5rem !important;
  }
  .mx-md-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }
  .my-md-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }
  .my-md-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }
  .my-md-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
  .my-md-n4 {
    margin-top: -1.5rem !important;
    margin-bottom: -1.5rem !important;
  }
  .my-md-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
  .mt-md-n1 {
    margin-top: -0.25rem !important;
  }
  .mt-md-n2 {
    margin-top: -0.5rem !important;
  }
  .mt-md-n3 {
    margin-top: -1rem !important;
  }
  .mt-md-n4 {
    margin-top: -1.5rem !important;
  }
  .mt-md-n5 {
    margin-top: -3rem !important;
  }
  .me-md-n1 {
    margin-right: -0.25rem !important;
  }
  .me-md-n2 {
    margin-right: -0.5rem !important;
  }
  .me-md-n3 {
    margin-right: -1rem !important;
  }
  .me-md-n4 {
    margin-right: -1.5rem !important;
  }
  .me-md-n5 {
    margin-right: -3rem !important;
  }
  .mb-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .mb-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .mb-md-n3 {
    margin-bottom: -1rem !important;
  }
  .mb-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .mb-md-n5 {
    margin-bottom: -3rem !important;
  }
  .ms-md-n1 {
    margin-left: -0.25rem !important;
  }
  .ms-md-n2 {
    margin-left: -0.5rem !important;
  }
  .ms-md-n3 {
    margin-left: -1rem !important;
  }
  .ms-md-n4 {
    margin-left: -1.5rem !important;
  }
  .ms-md-n5 {
    margin-left: -3rem !important;
  }
  .p-md-0 {
    padding: 0 !important;
  }
  .p-md-1 {
    padding: 0.25rem !important;
  }
  .p-md-2 {
    padding: 0.5rem !important;
  }
  .p-md-3 {
    padding: 1rem !important;
  }
  .p-md-4 {
    padding: 1.5rem !important;
  }
  .p-md-5 {
    padding: 3rem !important;
  }
  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-md-0 {
    padding-top: 0 !important;
  }
  .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .pt-md-3 {
    padding-top: 1rem !important;
  }
  .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .pt-md-5 {
    padding-top: 3rem !important;
  }
  .pe-md-0 {
    padding-right: 0 !important;
  }
  .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .pe-md-3 {
    padding-right: 1rem !important;
  }
  .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .pe-md-5 {
    padding-right: 3rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-md-5 {
    padding-bottom: 3rem !important;
  }
  .ps-md-0 {
    padding-left: 0 !important;
  }
  .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .ps-md-3 {
    padding-left: 1rem !important;
  }
  .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .ps-md-5 {
    padding-left: 3rem !important;
  }
}
@media (min-width: 992px) {
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-lg-0 {
    gap: 0 !important;
  }
  .gap-lg-1 {
    gap: 0.25rem !important;
  }
  .gap-lg-2 {
    gap: 0.5rem !important;
  }
  .gap-lg-3 {
    gap: 1rem !important;
  }
  .gap-lg-4 {
    gap: 1.5rem !important;
  }
  .gap-lg-5 {
    gap: 3rem !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .order-lg-first {
    order: -1 !important;
  }
  .order-lg-0 {
    order: 0 !important;
  }
  .order-lg-1 {
    order: 1 !important;
  }
  .order-lg-2 {
    order: 2 !important;
  }
  .order-lg-3 {
    order: 3 !important;
  }
  .order-lg-4 {
    order: 4 !important;
  }
  .order-lg-5 {
    order: 5 !important;
  }
  .order-lg-last {
    order: 6 !important;
  }
  .m-lg-0 {
    margin: 0 !important;
  }
  .m-lg-1 {
    margin: 0.25rem !important;
  }
  .m-lg-2 {
    margin: 0.5rem !important;
  }
  .m-lg-3 {
    margin: 1rem !important;
  }
  .m-lg-4 {
    margin: 1.5rem !important;
  }
  .m-lg-5 {
    margin: 3rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0 !important;
  }
  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .mt-lg-5 {
    margin-top: 3rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-right: 0 !important;
  }
  .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .me-lg-3 {
    margin-right: 1rem !important;
  }
  .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .me-lg-5 {
    margin-right: 3rem !important;
  }
  .me-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .mb-lg-6 {
    margin-bottom: 3.5rem !important;
  } // MDB
  .mb-lg-7 {
    margin-bottom: 4rem !important;
  } // MDB
  .mb-lg-8 {
    margin-bottom: 5rem !important;
  } // MDB
  .mb-lg-9 {
    margin-bottom: 6rem !important;
  } // MDB
  .mb-lg-10 {
    margin-bottom: 8rem !important;
  } // MDB
  .mb-lg-11 {
    margin-bottom: 10rem !important;
  } // MDB
  .mb-lg-12 {
    margin-bottom: 12rem !important;
  } // MDB
  .mb-lg-13 {
    margin-bottom: 14rem !important;
  } // MDB
  .mb-lg-14 {
    margin-bottom: 16rem !important;
  } // MDB
  .ms-lg-0 {
    margin-left: 0 !important;
  }
  .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .ms-lg-5 {
    margin-left: 3rem !important;
  }
  .ms-lg-auto {
    margin-left: auto !important;
  }
  .m-lg-n1 {
    margin: -0.25rem !important;
  }
  .m-lg-n2 {
    margin: -0.5rem !important;
  }
  .m-lg-n3 {
    margin: -1rem !important;
  }
  .m-lg-n4 {
    margin: -1.5rem !important;
  }
  .m-lg-n5 {
    margin: -3rem !important;
  }
  .mx-lg-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }
  .mx-lg-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }
  .mx-lg-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }
  .mx-lg-n4 {
    margin-right: -1.5rem !important;
    margin-left: -1.5rem !important;
  }
  .mx-lg-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }
  .my-lg-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }
  .my-lg-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }
  .my-lg-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
  .my-lg-n4 {
    margin-top: -1.5rem !important;
    margin-bottom: -1.5rem !important;
  }
  .my-lg-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
  .mt-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .mt-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .mt-lg-n3 {
    margin-top: -1rem !important;
  }
  .mt-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .mt-lg-n5 {
    margin-top: -3rem !important;
  }
  .me-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .me-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .me-lg-n3 {
    margin-right: -1rem !important;
  }
  .me-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .me-lg-n5 {
    margin-right: -3rem !important;
  }
  .mb-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .mb-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .mb-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .mb-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .mb-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .ms-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .ms-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .ms-lg-n3 {
    margin-left: -1rem !important;
  }
  .ms-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .ms-lg-n5 {
    margin-left: -3rem !important;
  }
  .p-lg-0 {
    padding: 0 !important;
  }
  .p-lg-1 {
    padding: 0.25rem !important;
  }
  .p-lg-2 {
    padding: 0.5rem !important;
  }
  .p-lg-3 {
    padding: 1rem !important;
  }
  .p-lg-4 {
    padding: 1.5rem !important;
  }
  .p-lg-5 {
    padding: 3rem !important;
  }
  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .pt-lg-5 {
    padding-top: 3rem !important;
  }
  .pe-lg-0 {
    padding-right: 0 !important;
  }
  .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .pe-lg-5 {
    padding-right: 3rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }
  .ps-lg-0 {
    padding-left: 0 !important;
  }
  .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .ps-lg-5 {
    padding-left: 3rem !important;
  }
}
@media (min-width: 1200px) {
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-xl-0 {
    gap: 0 !important;
  }
  .gap-xl-1 {
    gap: 0.25rem !important;
  }
  .gap-xl-2 {
    gap: 0.5rem !important;
  }
  .gap-xl-3 {
    gap: 1rem !important;
  }
  .gap-xl-4 {
    gap: 1.5rem !important;
  }
  .gap-xl-5 {
    gap: 3rem !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .order-xl-first {
    order: -1 !important;
  }
  .order-xl-0 {
    order: 0 !important;
  }
  .order-xl-1 {
    order: 1 !important;
  }
  .order-xl-2 {
    order: 2 !important;
  }
  .order-xl-3 {
    order: 3 !important;
  }
  .order-xl-4 {
    order: 4 !important;
  }
  .order-xl-5 {
    order: 5 !important;
  }
  .order-xl-last {
    order: 6 !important;
  }
  .m-xl-0 {
    margin: 0 !important;
  }
  .m-xl-1 {
    margin: 0.25rem !important;
  }
  .m-xl-2 {
    margin: 0.5rem !important;
  }
  .m-xl-3 {
    margin: 1rem !important;
  }
  .m-xl-4 {
    margin: 1.5rem !important;
  }
  .m-xl-5 {
    margin: 3rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0 !important;
  }
  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xl-5 {
    margin-top: 3rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-right: 0 !important;
  }
  .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xl-3 {
    margin-right: 1rem !important;
  }
  .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xl-5 {
    margin-right: 3rem !important;
  }
  .me-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .mb-xl-6 {
    margin-bottom: 3.5rem !important;
  } // MDB
  .mb-xl-7 {
    margin-bottom: 4rem !important;
  } // MDB
  .mb-xl-8 {
    margin-bottom: 5rem !important;
  } // MDB
  .mb-xl-9 {
    margin-bottom: 6rem !important;
  } // MDB
  .mb-xl-10 {
    margin-bottom: 8rem !important;
  } // MDB
  .mb-xl-11 {
    margin-bottom: 10rem !important;
  } // MDB
  .mb-xl-12 {
    margin-bottom: 12rem !important;
  } // MDB
  .mb-xl-13 {
    margin-bottom: 14rem !important;
  } // MDB
  .mb-xl-14 {
    margin-bottom: 16rem !important;
  } // MDB
  .ms-xl-0 {
    margin-left: 0 !important;
  }
  .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xl-5 {
    margin-left: 3rem !important;
  }
  .ms-xl-auto {
    margin-left: auto !important;
  }
  .m-xl-n1 {
    margin: -0.25rem !important;
  }
  .m-xl-n2 {
    margin: -0.5rem !important;
  }
  .m-xl-n3 {
    margin: -1rem !important;
  }
  .m-xl-n4 {
    margin: -1.5rem !important;
  }
  .m-xl-n5 {
    margin: -3rem !important;
  }
  .mx-xl-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }
  .mx-xl-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }
  .mx-xl-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }
  .mx-xl-n4 {
    margin-right: -1.5rem !important;
    margin-left: -1.5rem !important;
  }
  .mx-xl-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }
  .my-xl-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }
  .my-xl-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }
  .my-xl-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
  .my-xl-n4 {
    margin-top: -1.5rem !important;
    margin-bottom: -1.5rem !important;
  }
  .my-xl-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
  .mt-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .mt-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .mt-xl-n3 {
    margin-top: -1rem !important;
  }
  .mt-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .mt-xl-n5 {
    margin-top: -3rem !important;
  }
  .me-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .me-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .me-xl-n3 {
    margin-right: -1rem !important;
  }
  .me-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .me-xl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .mb-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .mb-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .mb-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .mb-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .ms-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .ms-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .ms-xl-n3 {
    margin-left: -1rem !important;
  }
  .ms-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .ms-xl-n5 {
    margin-left: -3rem !important;
  }
  .p-xl-0 {
    padding: 0 !important;
  }
  .p-xl-1 {
    padding: 0.25rem !important;
  }
  .p-xl-2 {
    padding: 0.5rem !important;
  }
  .p-xl-3 {
    padding: 1rem !important;
  }
  .p-xl-4 {
    padding: 1.5rem !important;
  }
  .p-xl-5 {
    padding: 3rem !important;
  }
  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-xl-0 {
    padding-top: 0 !important;
  }
  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xl-5 {
    padding-top: 3rem !important;
  }
  .pe-xl-0 {
    padding-right: 0 !important;
  }
  .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xl-5 {
    padding-right: 3rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xl-0 {
    padding-left: 0 !important;
  }
  .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xl-5 {
    padding-left: 3rem !important;
  }
}
@media (min-width: 1400px) {
  .d-xxl-inline {
    display: inline !important;
  }
  .d-xxl-inline-block {
    display: inline-block !important;
  }
  .d-xxl-block {
    display: block !important;
  }
  .d-xxl-grid {
    display: grid !important;
  }
  .d-xxl-table {
    display: table !important;
  }
  .d-xxl-table-row {
    display: table-row !important;
  }
  .d-xxl-table-cell {
    display: table-cell !important;
  }
  .d-xxl-flex {
    display: flex !important;
  }
  .d-xxl-inline-flex {
    display: inline-flex !important;
  }
  .d-xxl-none {
    display: none !important;
  }
  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xxl-row {
    flex-direction: row !important;
  }
  .flex-xxl-column {
    flex-direction: column !important;
  }
  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .gap-xxl-0 {
    gap: 0 !important;
  }
  .gap-xxl-1 {
    gap: 0.25rem !important;
  }
  .gap-xxl-2 {
    gap: 0.5rem !important;
  }
  .gap-xxl-3 {
    gap: 1rem !important;
  }
  .gap-xxl-4 {
    gap: 1.5rem !important;
  }
  .gap-xxl-5 {
    gap: 3rem !important;
  }
  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xxl-center {
    justify-content: center !important;
  }
  .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .align-items-xxl-center {
    align-items: center !important;
  }
  .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .align-content-xxl-center {
    align-content: center !important;
  }
  .align-content-xxl-between {
    align-content: space-between !important;
  }
  .align-content-xxl-around {
    align-content: space-around !important;
  }
  .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .align-self-xxl-auto {
    align-self: auto !important;
  }
  .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .align-self-xxl-center {
    align-self: center !important;
  }
  .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .align-self-xxl-stretch {
    align-self: stretch !important;
  }
  .order-xxl-first {
    order: -1 !important;
  }
  .order-xxl-0 {
    order: 0 !important;
  }
  .order-xxl-1 {
    order: 1 !important;
  }
  .order-xxl-2 {
    order: 2 !important;
  }
  .order-xxl-3 {
    order: 3 !important;
  }
  .order-xxl-4 {
    order: 4 !important;
  }
  .order-xxl-5 {
    order: 5 !important;
  }
  .order-xxl-last {
    order: 6 !important;
  }
  .m-xxl-0 {
    margin: 0 !important;
  }
  .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .m-xxl-3 {
    margin: 1rem !important;
  }
  .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .m-xxl-5 {
    margin: 3rem !important;
  }
  .m-xxl-auto {
    margin: auto !important;
  }
  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .mt-xxl-5 {
    margin-top: 3rem !important;
  }
  .mt-xxl-auto {
    margin-top: auto !important;
  }
  .me-xxl-0 {
    margin-right: 0 !important;
  }
  .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .me-xxl-5 {
    margin-right: 3rem !important;
  }
  .me-xxl-auto {
    margin-right: auto !important;
  }
  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .mb-xxl-6 {
    margin-bottom: 3.5rem !important;
  } // MDB
  .mb-xxl-7 {
    margin-bottom: 4rem !important;
  } // MDB
  .mb-xxl-8 {
    margin-bottom: 5rem !important;
  } // MDB
  .mb-xxl-9 {
    margin-bottom: 6rem !important;
  } // MDB
  .mb-xxl-10 {
    margin-bottom: 8rem !important;
  } // MDB
  .mb-xxl-11 {
    margin-bottom: 10rem !important;
  } // MDB
  .mb-xxl-12 {
    margin-bottom: 12rem !important;
  } // MDB
  .mb-xxl-13 {
    margin-bottom: 14rem !important;
  } // MDB
  .mb-xxl-14 {
    margin-bottom: 16rem !important;
  } // MDB
  .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  .ms-xxl-auto {
    margin-left: auto !important;
  }
  .m-xxl-n1 {
    margin: -0.25rem !important;
  }
  .m-xxl-n2 {
    margin: -0.5rem !important;
  }
  .m-xxl-n3 {
    margin: -1rem !important;
  }
  .m-xxl-n4 {
    margin: -1.5rem !important;
  }
  .m-xxl-n5 {
    margin: -3rem !important;
  }
  .mx-xxl-n1 {
    margin-right: -0.25rem !important;
    margin-left: -0.25rem !important;
  }
  .mx-xxl-n2 {
    margin-right: -0.5rem !important;
    margin-left: -0.5rem !important;
  }
  .mx-xxl-n3 {
    margin-right: -1rem !important;
    margin-left: -1rem !important;
  }
  .mx-xxl-n4 {
    margin-right: -1.5rem !important;
    margin-left: -1.5rem !important;
  }
  .mx-xxl-n5 {
    margin-right: -3rem !important;
    margin-left: -3rem !important;
  }
  .my-xxl-n1 {
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
  }
  .my-xxl-n2 {
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
  }
  .my-xxl-n3 {
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
  }
  .my-xxl-n4 {
    margin-top: -1.5rem !important;
    margin-bottom: -1.5rem !important;
  }
  .my-xxl-n5 {
    margin-top: -3rem !important;
    margin-bottom: -3rem !important;
  }
  .mt-xxl-n1 {
    margin-top: -0.25rem !important;
  }
  .mt-xxl-n2 {
    margin-top: -0.5rem !important;
  }
  .mt-xxl-n3 {
    margin-top: -1rem !important;
  }
  .mt-xxl-n4 {
    margin-top: -1.5rem !important;
  }
  .mt-xxl-n5 {
    margin-top: -3rem !important;
  }
  .me-xxl-n1 {
    margin-right: -0.25rem !important;
  }
  .me-xxl-n2 {
    margin-right: -0.5rem !important;
  }
  .me-xxl-n3 {
    margin-right: -1rem !important;
  }
  .me-xxl-n4 {
    margin-right: -1.5rem !important;
  }
  .me-xxl-n5 {
    margin-right: -3rem !important;
  }
  .mb-xxl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .mb-xxl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .mb-xxl-n3 {
    margin-bottom: -1rem !important;
  }
  .mb-xxl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .mb-xxl-n5 {
    margin-bottom: -3rem !important;
  }
  .ms-xxl-n1 {
    margin-left: -0.25rem !important;
  }
  .ms-xxl-n2 {
    margin-left: -0.5rem !important;
  }
  .ms-xxl-n3 {
    margin-left: -1rem !important;
  }
  .ms-xxl-n4 {
    margin-left: -1.5rem !important;
  }
  .ms-xxl-n5 {
    margin-left: -3rem !important;
  }
  .p-xxl-0 {
    padding: 0 !important;
  }
  .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .p-xxl-3 {
    padding: 1rem !important;
  }
  .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .p-xxl-5 {
    padding: 3rem !important;
  }
  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .pt-xxl-5 {
    padding-top: 3rem !important;
  }
  .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .ps-xxl-5 {
    padding-left: 3rem !important;
  }
}

/*------------------------------------*\
  # MDB Specific Spacing & Flex
\*------------------------------------*/
.note {
  padding: 10px; // Spacing
  margin-bottom: 1rem; // Implicit spacing for subsequent elements
}

.mdb-flag-selected {
  margin: 10px auto 0; // Spacing
}
.mdb-selected-flag-text {
  margin: 0 auto; // Spacing
}

i.flag:not(.icon) {
  margin: 0 0.5em 0 0; // Spacing
}

.form-outline .form-control {
  padding: 0.33em 0.75em; // MDB Spacing
}
.form-outline .form-control ~ .form-label {
  left: 0.75rem; // Spacing
  padding-top: 0.37rem; // Spacing
}
.form-outline .form-control.form-control-lg {
  padding-left: 0.75em; // MDB Spacing
  padding-right: 0.75em; // MDB Spacing
}
.form-outline .form-control.form-control-lg ~ .form-label {
  padding-top: 0.7rem; // MDB Spacing
}
.form-outline .form-control.form-control-sm {
  padding: 0.43em 0.99em 0.35em; // MDB Spacing
}
.form-outline .form-control.form-control-sm ~ .form-label {
  padding-top: 0.33rem; // MDB Spacing
}

.input-group > .form-control {
  height: calc(2.08rem + 2px); // MDB Sizing/Spacing
  padding-top: 0.27rem; // MDB Spacing
  padding-bottom: 0.27rem; // MDB Spacing
}
.input-group-text {
  padding-top: 0.26rem; // MDB Spacing
  padding-bottom: 0.26rem; // MDB Spacing
}
.input-group-lg > .form-control {
  height: calc(2.645rem + 2px); // MDB Sizing/Spacing
  padding-top: 0.33rem; // MDB Spacing
  padding-bottom: 0.33rem; // MDB Spacing
}
.input-group-sm > .form-control {
  height: calc(1.66rem + 2px); // MDB Sizing/Spacing
  padding-top: 0.33rem; // MDB Spacing
  padding-bottom: 0.33rem; // MDB Spacing
}
.input-group .invalid-feedback,
.input-group .valid-feedback,
.was-validated .input-group .invalid-feedback,
.was-validated .input-group .valid-feedback {
  margin-top: 2.5rem; // MDB Spacing override
}
.valid-feedback {
  margin-top: -0.75rem; // MDB Spacing override
}
.invalid-feedback {
  margin-top: -0.75rem; // MDB Spacing override
}

.btn {
  padding: 0.625rem 1.5rem 0.5rem; // MDB Spacing
}
[class*="btn-outline-"] {
  padding: 0.5rem 1.375rem 0.375rem; // MDB Spacing
}
.btn-group-lg > [class*="btn-outline-"].btn,
[class*="btn-outline-"].btn-lg {
  padding: 0.625rem 1.5625rem 0.5625rem; // MDB Spacing
}
.btn-group-sm > [class*="btn-outline-"].btn,
[class*="btn-outline-"].btn-sm {
  padding: 0.25rem 0.875rem 0.1875rem; // MDB Spacing
}
.btn-floating,
[class*="btn-outline-"].btn-floating {
  padding: 0; // Spacing reset
}
.fixed-action-btn {
  display: flex; // MDB Flexbox
  flex-flow: column-reverse nowrap; // MDB Flexbox
  align-items: center; // MDB Flexbox
  padding: 0.9375rem 20px 20px; // MDB Spacing
  margin-bottom: 0; // MDB Spacing reset
}
.fixed-action-btn ul {
  display: flex; // MDB Flexbox
  flex-direction: column; // MDB Flexbox
  padding: 0; // MDB Spacing reset
  margin: 0; // MDB Spacing reset
}
.fixed-action-btn ul li {
  display: flex; // MDB Flexbox
  margin-right: auto; // MDB Spacing
  margin-bottom: 1.5rem; // MDB Spacing
  margin-left: auto; // MDB Spacing
}
.fixed-action-btn ul li:first-of-type {
  margin-top: 0.75rem; // MDB Spacing
}

.nav-tabs .nav-link {
  padding: 17px 29px 16px; // MDB Spacing
}
.nav-pills {
  margin-left: -0.5rem; // MDB Spacing (negative margin)
}
.nav-pills .nav-link {
  padding: 17px 29px 16px; // MDB Spacing
  margin: 0.5rem; // MDB Spacing
}
.navbar {
  padding-top: 0.5625rem; // MDB Spacing
}
.navbar .breadcrumb {
  margin-bottom: 0; // MDB Spacing reset
}
.badge-dot {
  margin-left: -0.3125rem; // MDB Spacing (negative margin)
}
.badge-notification {
  margin-top: -0.1rem; // MDB Spacing (negative margin)
  margin-left: -0.5rem; // MDB Spacing (negative margin)
  padding: 0.2em 0.45em; // MDB Spacing
}

.list-group-light .list-group-item {
  padding: 1rem 0; // MDB Spacing
}
.list-group-small .list-group-item {
  padding: 0.5rem 0; // MDB Spacing
}

.tooltip-inner {
  padding: 6px 16px; // MDB Spacing
}
.nav-pills.menu-sidebar .nav-link {
  padding: 0 5px; // MDB Spacing
  margin-top: 3px; // MDB Spacing
  margin-bottom: 3px; // MDB Spacing
}

/*------------------------------------*\
  #CARD UTILITIES (Reusable Card Styles)
\*------------------------------------*/
.card-grid-2 {
  display: grid !important;
  grid-template-columns: repeat(2, 1fr) !important;
}

.card-grid-3 {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
}

.card-grid-4 {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
}

.card-grid-responsive {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
}

/*------------------------------------*\
  #BORDER RADIUS UTILITIES
\*------------------------------------*/
.rounded-xs {
  border-radius: 0.125rem !important;
}

.rounded-sm {
  border-radius: 0.25rem !important;
}

.rounded-md {
  border-radius: 0.5rem !important;
}

.rounded-lg {
  border-radius: 0.75rem !important;
}

.rounded-xl {
  border-radius: 1rem !important;
}

.rounded-2xl {
  border-radius: 1.5rem !important;
}

.rounded-3xl {
  border-radius: 2rem !important;
}

/*------------------------------------*\
  #TYPOGRAPHY UTILITIES
\*------------------------------------*/
.font-mulish {
  font-family: Mulish, sans-serif !important;
}

.font-weight-500 {
  font-weight: 500 !important;
}

.font-weight-700 {
  font-weight: 700 !important;
}

.line-height-1-2 {
  line-height: 1.2 !important;
}

.line-height-1-5 {
  line-height: 1.5 !important;
}

.text-28 {
  font-size: 28px !important;
}

.text-24 {
  font-size: 24px !important;
}

.text-22 {
  font-size: 22px !important;
}

.text-20 {
  font-size: 20px !important;
}

.text-16 {
  font-size: 16px !important;
}

.text-14 {
  font-size: 14px !important;
}

.text-12 {
  font-size: 12px !important;
}

/*------------------------------------*\
  #LAYOUT UTILITIES
\*------------------------------------*/
.h-280 {
  height: 280px !important;
}

.h-260 {
  height: 260px !important;
}

.h-240 {
  height: 240px !important;
}

.h-220 {
  height: 220px !important;
}

.max-w-1000 {
  max-width: 1000px !important;
}

.max-w-1200 {
  max-width: 1200px !important;
}

.max-w-800 {
  max-width: 800px !important;
}

.w-80 {
  width: 80% !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.inset-0 {
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}

.z-1 {
  z-index: 1 !important;
}

.pointer-events-none {
  pointer-events: none !important;
}

/*------------------------------------*\
  #TRANSITION UTILITIES
\*------------------------------------*/
.transition-opacity {
  transition: opacity 0.3s ease !important;
}

.transition-all {
  transition: all 0.3s ease !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-1 {
  opacity: 1 !important;
}

/*------------------------------------*\
  #SHADOW UTILITIES
\*------------------------------------*/
.shadow-card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
}

.shadow-card-hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;
}

/*------------------------------------*\
  #OBJECT FIT UTILITIES
\*------------------------------------*/
.object-contain {
  object-fit: contain !important;
}

.object-cover {
  object-fit: cover !important;
}
