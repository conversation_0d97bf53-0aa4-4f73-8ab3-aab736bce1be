/**
 * =========================================================================
 * Play+ Design System: Slider Component Tokens
 *
 * Component-specific semantic tokens for slider elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for slider styling.
 * =========================================================================
 */

:root {
  /* --- Slider Base --- */
  --slider-track-height: 4px;
  --slider-track-border-radius: var(--global-radius-sm);
  --slider-transition: 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --slider-cursor: pointer;

  /* --- Slider Track --- */
  --slider-track-background: var(--color-surface-subtle);
  --slider-track-background-disabled: var(--color-surface-disabled);
  --slider-track-border: none;
  --slider-track-border-radius: var(--slider-track-height);

  /* --- Slider Progress --- */
  --slider-progress-background: var(--color-brand-primary);
  --slider-progress-background-disabled: var(--color-surface-disabled);
  --slider-progress-border-radius: var(--slider-track-height);

  /* --- Slider Thumb --- */
  --slider-thumb-size: 20px;
  --slider-thumb-background: var(--color-brand-primary);
  --slider-thumb-background-hover: var(--color-brand-primary-hover);
  --slider-thumb-background-active: var(--color-brand-primary-active);
  --slider-thumb-background-disabled: var(--color-surface-disabled);
  --slider-thumb-border: 2px solid var(--color-background-primary);
  --slider-thumb-border-hover: 2px solid var(--color-background-primary);
  --slider-thumb-border-active: 2px solid var(--color-background-primary);
  --slider-thumb-border-disabled: 2px solid var(--color-surface-disabled);
  --slider-thumb-border-radius: 50%;
  --slider-thumb-shadow: var(--global-elevation-01);
  --slider-thumb-shadow-hover: var(--global-elevation-02);
  --slider-thumb-shadow-active: var(--global-elevation-03);

  /* --- Slider Thumb Inner Circle (Dragging State) --- */
  --slider-thumb-inner-size: 8px;
  --slider-thumb-inner-background: var(--color-background-primary);
  --slider-thumb-inner-background-hover: var(--color-background-primary);
  --slider-thumb-inner-background-active: var(--color-background-primary);
  --slider-thumb-inner-border-radius: 50%;

  /* --- Slider Focus --- */
  --slider-focus-ring: var(--accessibility-focus-ring-width) var(--accessibility-focus-ring-style) var(--accessibility-focus-ring-color);
  --slider-focus-ring-offset: var(--accessibility-focus-ring-offset);

  /* --- Slider Label --- */
  --slider-label-font: var(--font-label);
  --slider-label-color: var(--color-text-secondary);
  --slider-label-color-disabled: var(--color-text-disabled);
  --slider-label-weight-sm: var(--global-font-weight-bold);
  --slider-label-margin-bottom: var(--global-spacing-2);
  --slider-label-font-size-sm:var(--global-font-size-md);
  --slider-label-font-size-md:var(--global-font-size-xs);
  --slider-label-font-family:var(--global-font-family-body);
  --slider-label-line-height:22px;
  --slider-tooltip-border-radius: 2px;
  --slider-tooltip-margin:var(--global-spacing-1);
  --slider-label-weight-md: var(--global-font-weight-medium);


  /* --- Slider Value --- */
  --slider-value-font: var(--font-body-2);
  --slider-value-color: var(--color-text-primary);
  --slider-value-color-disabled: var(--color-text-disabled);
  --slider-value-weight: var(--global-font-weight-regular);
  --slider-value-margin-top: var(--global-spacing-2);

  /* --- Slider Sizes --- */
  --slider-size-sm-track-height: var(--global-spacing-1);
  --slider-size-sm-thumb-size: 20px;
  --slider-size-sm-thumb-inner-size: 6px;
  --slider-size-sm-label: var(--font-caption);
  --slider-size-sm-value: var(--font-label);

  --slider-size-md-track-height: var(--global-spacing-1);
  --slider-size-md-thumb-size: var(--global-spacing-5);
  --slider-size-md-thumb-inner-size: var(--global-spacing-2);
  --slider-size-md-label: var(--font-label);
  --slider-size-md-value: var(--font-body-2);

  --slider-size-lg-track-height: 6px;
  --slider-size-lg-thumb-size: var(--global-spacing-5);
  --slider-size-lg-thumb-inner-size: 10px;
  --slider-size-lg-label: var(--font-body-2);
  --slider-size-lg-value: var(--font-body-1);

  /* --- Slider Range --- */
  --slider-range-gap: var(--global-spacing-4);
  --slider-range-align-items: center;
  --slider-range-justify-content: space-between;

  /* --- Slider Marks --- */
  --slider-mark-size: 4px;
  --slider-mark-background: var(--color-border-default);
  --slider-mark-background-active: var(--color-brand-primary);
  --slider-mark-border-radius: 50%;
  --slider-mark-margin-top: calc((var(--slider-thumb-size) - var(--slider-mark-size)) / 2);

  /* --- Slider Tooltip --- */
  --slider-tooltip-padding: var(--global-spacing-2);
  --slider-tooltip-arrow-size: var(--global-spacing-1);
  --slider-handle-icon-width:var(--global-spacing-5);
  --slider-handle-icon-height:var(--global-spacing-5);

  /* --- Slider Input (for input type) --- */
  --slider-input-width: 48px;
  --slider-input-height: 28px;
  --slider-input-padding: 8px;
  --slider-input-border-radius: var(--global-radius-sm);
  --slider-input-border: 1px solid var(--global-color-gray-300);
  --slider-input-background: var(--global-color-white);
  --slider-input-font-size: var(--slider-label-font-size-md);
  --slider-input-font-weight: var(--slider-label-weight-md);
  --slider-input-font-family: var(--slider-label-font-family);
  --slider-input-color: var(--slider-value-color);
  --slider-input-gap: var(--global-spacing-3);
  --slider-input-transition: var(--slider-transition);
  --slider-input-color:#3B3F46;
  --slider-container-height:48px;
  --slider-container-padding: 4px var(--global-spacing-3);
  --slider-container-gap:var(--global-spacing-3);

}
