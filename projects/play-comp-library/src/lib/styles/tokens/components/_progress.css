/**
 * =========================================================================
 * Play+ Design System: Progress Component Tokens
 *
 * Component-specific semantic tokens for progress bar elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for progress styling.
 * =========================================================================
 */

:root {
  /* --- Progress Base --- */
  --progress-transition: var(--motion-pattern-fade);
  --progress-animation-duration: var(--global-motion-duration-standard);
  --progress-animation-easing: var(--global-motion-easing-enter);

  /* --- Progress Gradients (Using Progress Colors) --- */
  --progress-primary-color: var(--rgb-brand-primary);
  --progress-gradient-start: rgb(var(--progress-primary-color));
  --progress-gradient-end: rgba(var(--progress-primary-color), 0.5);
  --progress-linear-gradient: linear-gradient(90deg, var(--progress-gradient-start) 0%, var(--progress-gradient-end) 100%);

  /* --- Progress Text --- */
  --progress-text-font: var(--font-body-2);
  --progress-text-color: var(--color-text-primary);
  --progress-text-weight: var(--global-font-weight-bold);
  --progress-text-fill: var(--color-text-primary);

  /* --- Progress Label --- */
  --progress-label-font: var(--font-body-2);
  --progress-label-color: var(--color-text-secondary);
  --progress-label-weight: var(--global-font-weight-regular);
  --progress-label-line-height: var(--global-line-height-default);

  /* --- Progress Percentage --- */
  --progress-percentage-font: var(--font-caption);
  --progress-percentage-color: var(--color-text-secondary);
  --progress-percentage-weight: var(--global-font-weight-medium);
  --progress-percentage-line-height: var(--global-line-height-default);

  /* --- Linear Progress --- */
  --progress-linear-height: 8px;
  --progress-linear-background: var(--color-surface-subtle);
  --progress-linear-background-disabled: var(--color-surface-disabled);
  --progress-linear-border-radius: var(--global-radius-sm);
  --progress-linear-overflow: hidden;

  --progress-linear-fill-background: var(--color-brand-primary);
  --progress-linear-fill-background-disabled: var(--color-surface-disabled);
  --progress-linear-fill-transition: width var(--progress-animation-duration) var(--progress-animation-easing);

  /* --- Circular Progress --- */
  --progress-circular-size: 120px;
  --progress-circular-stroke-width: 8px;
  --progress-circular-background: var(--color-surface-subtle);
  --progress-circular-background-disabled: var(--color-surface-disabled);

  --progress-circular-fill-stroke: var(--color-brand-primary);
  --progress-circular-fill-stroke-disabled: var(--color-surface-disabled);
  --progress-circular-fill-transition: stroke-dashoffset var(--progress-animation-duration) var(--progress-animation-easing);

  /* --- Progress Indeterminate --- */
  --progress-indeterminate-animation: spin 1.5s linear infinite;
  --progress-indeterminate-duration: 1.5s;
  --progress-indeterminate-timing: linear;
  --progress-indeterminate-iteration: infinite;

  /* --- Progress Sizes --- */
  --progress-size-sm-linear-height: 4px;
  --progress-size-sm-circular-size: 80px;
  --progress-size-sm-circular-stroke: 6px;
  --progress-size-sm-text: var(--font-label);
  --progress-size-sm-label: var(--font-caption);
  --progress-size-sm-percentage: var(--font-caption);

  --progress-size-md-linear-height: 8px;
  --progress-size-md-circular-size: 120px;
  --progress-size-md-circular-stroke: 8px;
  --progress-size-md-text: var(--font-body-2);
  --progress-size-md-label: var(--font-body-2);
  --progress-size-md-percentage: var(--font-caption);

  --progress-size-lg-linear-height: 12px;
  --progress-size-lg-circular-size: 160px;
  --progress-size-lg-circular-stroke: 12px;
  --progress-size-lg-text: var(--font-body-1);
  --progress-size-lg-label: var(--font-body-1);
  --progress-size-lg-percentage: var(--font-label);

  /* --- Progress Variants --- */
  --progress-success-fill: var(--global-color-green-500);
  --progress-success-stroke: var(--global-color-green-500);

  --progress-warning-fill: var(--global-color-yellow-500);
  --progress-warning-stroke: var(--global-color-yellow-500);

  --progress-error-fill: var(--global-color-red-500);
  --progress-error-stroke: var(--global-color-red-500);

  --progress-info-fill: var(--global-color-blue-info-500);
  --progress-info-stroke: var(--global-color-blue-info-500);

  /* --- Progress Container --- */
  --progress-container-gap: var(--global-spacing-3);
  --progress-container-align-items: center;
  --progress-container-justify-content: space-between;
}