:root {
  /* Use existing semantic variables from the design system */
  --autocomplete-background: var(--color-background-primary);
  --autocomplete-border-width: 1px;
  --autocomplete-border-color: var(--color-border-default);
  --autocomplete-border-radius: var(--global-radius-md);
  --autocomplete-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);

  --autocomplete-option-color: var(--color-text-primary);
  --autocomplete-option-hover-bg: var(--color-surface-subtle-hover);
  --autocomplete-option-hover-color: var(--color-text-primary);
  --autocomplete-option-icon-color: var(--color-text-secondary);

  --autocomplete-empty-color: var(--color-text-secondary);

  --autocomplete-chip-bg: var(--color-surface-subtle);
  --autocomplete-chip-color: var(--color-text-primary);
  --autocomplete-chip-remove-color: var(--color-border-error);

  /* Additional semantic variables for autocomplete */
  --autocomplete-surface-primary: var(--color-background-primary);
  --autocomplete-surface-hover: var(--color-surface-subtle-hover);
  --autocomplete-surface-active: var(--color-surface-subtle-active);
  --autocomplete-surface-secondary: var(--color-surface-subtle);
  --autocomplete-border-secondary: var(--color-border-default);
  --autocomplete-border-tertiary: var(--color-border-subtle);
  --autocomplete-error-primary: var(--color-border-error);
  --autocomplete-error-surface: rgba(244, 67, 54, 0.1);
}
