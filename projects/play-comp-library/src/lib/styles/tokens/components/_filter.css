/**
 * Component: Filter
 * Purpose: Filter component tokens for page footers and navigation
 */

:root {
    /* Base styles */
    --filter-background-primary: var(--color-background-primary);
    --filter-border: 1px solid var(--color-border-default);
    --filter-border-radius: var(--global-spacing-2);
    
    /* Text colors */
    --filter-text-color: var(--color-text-primary);
    --filter-text-active: var(--color-text-interactive);
    
    /* Interactive states */
    --filter-background-hover: var(--color-surface-subtle-hover);
    --filter-border-hover: var(--color-border-interactive);
    --filter-border-focus: var(--color-border-focus);
    
    /* Badge colors */
    --filter-badge-background: var(--color-background-info);
    --filter-badge-text: var(--color-text-on-primary);
    
    /* Panel styles */
    --filter-panel-background: var(--color-background-primary);
    --filter-panel-border: var(--color-border-default);
    --filter-panel-shadow: var(--global-elevation-02);
    
    /* Header/Footer styles */
    --filter-header-background: var(--color-background-primary);
    --filter-header-border: var(--color-border-default);
    --filter-footer-background: var(--color-background-primary);
    
    /* Button styles */
    --filter-clear-text: var(--color-text-interactive);
    --filter-clear-hover: var(--color-text-interactive-hover);
    --filter-apply-background: var(--color-surface-interactive-primary);
    --filter-apply-hover: var(--color-surface-interactive-primary-hover);
    --filter-apply-text: var(--color-text-on-primary);
    
    /* Disabled state */
    --filter-disabled-opacity: 0.5;
}