/**
 * =========================================================================
 * Play+ Design System: Drawer Component Tokens
 *
 * Component-specific semantic tokens for drawer/slide-out panel elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for drawer styling.
 *
 * STRUCTURE:
 * - Drawer base properties (background, borders, shadows)
 * - Position-specific sizing (left/right vs top/bottom)
 * - Animation and transition properties
 * - Overlay and backdrop styling
 * - Content area styling (header, body, footer)
 * =========================================================================
 */

:root {
  /* =======================
     DRAWER BASE PROPERTIES
     ======================= */

  /* Drawer Surface */
  --drawer-background: var(--color-background-primary);
  --drawer-border: 1px solid var(--color-border-default);
  --drawer-border-radius: 0; /* Drawers typically don't have rounded corners */
  --drawer-shadow: var(--global-elevation-04);
  --drawer-z-index: 1050;

  /* Drawer Overlay */
  --drawer-overlay-background: var(--popup-overlay-background);
  --drawer-overlay-z-index: 1049;

  /* =======================
     DRAWER SIZING SYSTEM
     ======================= */

  /* Small Drawer */
  --drawer-size-sm-width: 320px;
  --drawer-size-sm-height: 200px;
  --drawer-size-sm-max-width: 90vw;
  --drawer-size-sm-max-height: 50vh;

  /* Medium Drawer (Default) */
  --drawer-size-md-width: 480px;
  --drawer-size-md-height: 300px;
  --drawer-size-md-max-width: 90vw;
  --drawer-size-md-max-height: 60vh;

  /* Large Drawer */
  --drawer-size-lg-width: 640px;
  --drawer-size-lg-height: 400px;
  --drawer-size-lg-max-width: 90vw;
  --drawer-size-lg-max-height: 70vh;

  /* Extra Large Drawer */
  --drawer-size-xl-width: 800px;
  --drawer-size-xl-height: 500px;
  --drawer-size-xl-max-width: 95vw;
  --drawer-size-xl-max-height: 80vh;

  /* Full Size Drawer */
  --drawer-size-full-width: 100vw;
  --drawer-size-full-height: 100vh;

  /* =======================
     DRAWER HEADER STYLING
     ======================= */

  /* Header Layout */
  --drawer-header-padding: var(--global-spacing-4);
  --drawer-header-border: 1px solid var(--color-border-default);
  --drawer-header-background: var(--color-background-primary);
  --drawer-header-gap: var(--global-spacing-3);

  /* Header Typography */
  --drawer-title-font: var(--font-heading-3);
  --drawer-title-color: var(--color-text-primary);
  --drawer-title-weight: var(--global-font-weight-semibold);
  --drawer-title-line-height: var(--global-line-height-tight);

  --drawer-subtitle-font: var(--font-body-2);
  --drawer-subtitle-color: var(--color-text-secondary);
  --drawer-subtitle-weight: var(--global-font-weight-regular);
  --drawer-subtitle-line-height: var(--global-line-height-default);
  --drawer-subtitle-margin: var(--global-spacing-1) 0 0 0;

  /* Close Button */
  --drawer-close-button-size: 32px;
  --drawer-close-button-color: var(--color-text-secondary);
  --drawer-close-button-hover-color: var(--color-text-primary);
  --drawer-close-button-background: transparent;
  --drawer-close-button-hover-background: var(--color-surface-hover);

  /* =======================
     DRAWER CONTENT AREAS
     ======================= */

  /* Body Content */
  --drawer-body-padding: var(--global-spacing-4);
  --drawer-body-background: var(--color-background-primary);
  --drawer-body-text-color: var(--color-text-primary);

  /* Footer Content */
  --drawer-footer-padding: var(--global-spacing-4);
  --drawer-footer-border: 1px solid var(--color-border-default);
  --drawer-footer-background: var(--color-background-primary);
  --drawer-footer-gap: var(--global-spacing-3);

  /* =======================
     DRAWER SPRING ANIMATION
     ======================= */

  /* Smooth Animation Parameters */
  --drawer-smooth-duration: 0.25s;
  --drawer-smooth-easing: cubic-bezier(0.16, 1, 0.3, 1);

  /* Smooth Motion Pattern */
  --drawer-smooth-transition: transform var(--drawer-smooth-duration) var(--drawer-smooth-easing),
                             opacity var(--drawer-smooth-duration) var(--drawer-smooth-easing),
                             visibility var(--drawer-smooth-duration) var(--drawer-smooth-easing);
  
  --drawer-transition-fade: opacity var(--drawer-animation-duration) var(--drawer-animation-easing),
                           visibility var(--drawer-animation-duration) var(--drawer-animation-easing);
  
  --drawer-transition-scale: transform var(--drawer-animation-duration) var(--drawer-animation-easing),
                            opacity var(--drawer-animation-duration) var(--drawer-animation-easing),
                            visibility var(--drawer-animation-duration) var(--drawer-animation-easing);

  /* Overlay Animation */
  --drawer-overlay-transition: opacity var(--drawer-animation-duration) var(--drawer-animation-easing),
                              visibility var(--drawer-animation-duration) var(--drawer-animation-easing);

  /* ===================================================================
     DRAWER VARIANTS
     =================================================================== */
  
  /* Glass Variant */
  --drawer-glass-background: var(--glass-background-color, rgba(255, 255, 255, 0.8));
  --drawer-glass-backdrop-filter: var(--glass-backdrop-blur, blur(10px));
  --drawer-glass-border: var(--glass-border-width, 1px) solid var(--glass-border-color, rgba(255, 255, 255, 0.2));
  --drawer-glass-shadow: var(--glass-elevation, var(--global-elevation-04));

  /* Overlay Variant */
  --drawer-overlay-variant-background: var(--color-background-overlay, rgba(0, 0, 0, 0.9));
  --drawer-overlay-variant-text: var(--color-text-on-dark, #ffffff);
  --drawer-overlay-variant-border: none;

  /* ===================================================================
     DRAWER RESIZE HANDLE
     =================================================================== */
  
  --drawer-resize-handle-size: 4px;
  --drawer-resize-handle-color: transparent;
  --drawer-resize-handle-hover-color: var(--color-brand-primary);
  --drawer-resize-handle-hover-opacity: 0.3;
  --drawer-resize-handle-transition: background-color var(--global-motion-duration-swift) var(--global-motion-easing-standard),
                                     opacity var(--global-motion-duration-swift) var(--global-motion-easing-standard);

  /* ===================================================================
     DRAWER RESPONSIVE BREAKPOINTS
     =================================================================== */
  
  /* Mobile Breakpoint */
  --drawer-mobile-breakpoint: 768px;
  --drawer-mobile-padding: var(--global-spacing-3);
  --drawer-mobile-width: 100vw;
  --drawer-mobile-height: 100vh;

  /* ===================================================================
     DRAWER ACCESSIBILITY
     =================================================================== */
  
  /* Focus Management */
  --drawer-focus-outline: 2px solid var(--color-border-focus);
  --drawer-focus-outline-offset: 2px;

  /* High Contrast Support */
  --drawer-high-contrast-border: 2px solid;
  --drawer-high-contrast-header-border: 2px solid;
  --drawer-high-contrast-footer-border: 2px solid;

  /* ===================================================================
     DRAWER STATES
     =================================================================== */
  
  /* Loading State */
  --drawer-loading-background: var(--color-surface-disabled);
  --drawer-loading-opacity: 0.6;

  /* Error State */
  --drawer-error-border: 1px solid var(--color-border-error);
  --drawer-error-background: var(--color-background-error);

  /* Success State */
  --drawer-success-border: 1px solid var(--color-border-success);
  --drawer-success-background: var(--color-background-success);

  /* Warning State */
  --drawer-warning-border: 1px solid var(--color-border-warning);
  --drawer-warning-background: var(--color-background-warning);
}
