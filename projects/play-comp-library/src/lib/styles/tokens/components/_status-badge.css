/**
 * =========================================================================
 * Play+ Design System: Status Badge Component Tokens
 *
 * Component-specific semantic tokens for status badge elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for badge styling.
 * =========================================================================
 */

:root {

  --status-badge-font-family: var(--font-family-body);
  --status-badge-font-style: normal;
  --status-badge-border-radius: 4px;
  --status-badge-pill-border-radius: 99px;
  --status-badge-line-height: var(--global-line-height-none);
  --status-badge-font-weight: var(--global-font-weight-medium);

  --status-badge-error-background: var(--color-background-error);
  --status-badge-error-text: var(--color-text-on-brand);

  --status-badge-success-background: var(--color-background-success);
  --status-badge-success-text: var(--color-text-on-brand);

  --status-badge-warning-background: var(--color-background-warning);
  --status-badge-warning-text: var(--color-text-on-brand);

  --status-badge-info-background: var(--color-background-info);
  --status-badge-info-text: var(--color-text-on-brand);

  /* Size variants -*/
  --status-badge-xs-font-size: 10px;
  --status-badge-xs-height: 16px;
  --status-badge-xs-padding: 2px var(--global-spacing-2);

  --status-badge-sm-font-size: 12px;
  --status-badge-sm-height: 20px;
  --status-badge-sm-padding: 2px var(--global-spacing-2);

  --status-badge-md-font-size: 12px;
  --status-badge-md-height: 24px;
  --status-badge-md-padding: 4px var(--global-spacing-2);

  --status-badge-lg-font-size: 14x;
  --status-badge-lg-height: 28px;
  --status-badge-lg-padding: 4px var(--global-spacing-2);

  --status-badge-xl-font-size: 14x;
  --status-badge-xl-height: 32px;
  --status-badge-xl-padding: 4px var(--global-spacing-2);


}