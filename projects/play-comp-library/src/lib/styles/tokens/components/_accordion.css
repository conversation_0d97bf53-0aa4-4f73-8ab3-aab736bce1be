/**
 * Component: Accordion
 * Purpose: Accordion component tokens for collapsible content sections
 */

:root {
  /* Accordion Base */
  --accordion-background: var(--color-background-primary);
  --accordion-border: 1px solid var(--color-border-default);
  --accordion-border-radius: var(--global-radius-md);
  --accordion-padding: var(--global-spacing-4);

  /* Accordion Header */
  --accordion-header-background: var(--color-background-secondary);
  --accordion-header-text: var(--color-text-primary);
  --accordion-header-font: var(--font-heading-h4);
  --accordion-header-padding: var(--global-spacing-4);
  --accordion-header-hover-background: var(--color-surface-subtle-hover);
  --accordion-header-active-background: var(--color-surface-subtle);

  /* Accordion Icon */
  --accordion-icon-color: var(--color-text-secondary);
  --accordion-icon-size: var(--global-icon-size-md);
  --accordion-icon-transition: transform var(--global-motion-duration-standard) var(--global-motion-easing-standard);

  /* Accordion Content */
  --accordion-content-background: var(--color-background-primary);
  --accordion-content-text: var(--color-text-secondary);
  --accordion-content-font: var(--font-body-1);
  --accordion-content-padding: var(--global-spacing-4);
  --accordion-content-border-top: 1px solid var(--color-border-subtle);

  /* Accordion Variants */
  --accordion-simple-background: var(--color-background-secondary);
  --accordion-simple-border: none;
  --accordion-simple-border-radius: 0;

  --accordion-light-background: var(--color-background-primary);
  --accordion-light-border: 1px solid var(--color-border-subtle);
  --accordion-light-border-radius: var(--global-radius-sm);

  --accordion-dark-background: var(--global-color-gray-900);
  --accordion-dark-text: var(--global-color-white);
  --accordion-dark-header-background: var(--global-color-gray-800);
  --accordion-dark-header-text: var(--global-color-white);
  --accordion-dark-content-background: var(--global-color-gray-900);
  --accordion-dark-content-text: var(--global-color-gray-300);
  --accordion-dark-border: 1px solid var(--global-color-gray-700);

  /* Accordion States */
  --accordion-disabled-background: var(--color-background-disabled);
  --accordion-disabled-text: var(--color-text-disabled);
  --accordion-disabled-border: 1px solid var(--color-border-disabled);

  /* Accordion Animation */
  --accordion-content-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);
  --accordion-icon-rotate: rotate(180deg);

  /*Accordion Fonts */
  --accordion-font-family:var(--global-font-family-body);
  --accordion-font-weight:var(--global-font-weight-regular);

} 