/**
 * Component: List
 * Purpose: List component tokens for displaying selectable lists
 */

:root {
  /* List Container */
  --list-container-background: var(--color-background-primary);
  --list-container-border: var(--global-color-gray-700);
  --list-container-border-radius: var(--global-radius-lg);
  --list-container-padding: var(--global-spacing-2);
  --list-container-gap: var(--global-spacing-5);
  --list-container-margin: var(--global-spacing-4);

  /* List Title */
  --list-title-color: var(--global-color-black);
  --list-title-font: var(--font-heading-h4);
  --list-title-size: var(--global-font-size-lg);
  --list-title-weight: var(--global-font-weight-bold);
  --list-title-line-height: var(--global-line-height-relaxed);
  --list-title-font-family: var(--global-font-family-heading);

  /* List Items */
  --list-items-gap: var(--global-spacing-4);

  /* List Item */
  --list-item-background: var(--color-background-primary);
  --list-item-border: 1px solid var(--color-border-subtle);
  --list-item-border-radius: var(--global-spacing-2);
  --list-item-padding: var(--global-spacing-4);
  --list-item-gap: var(--global-spacing-3);
  --list-item-cursor: pointer;
  --list-item-border-color: var(--global-color-gray-400);
  --list-item-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);
  --list-item-color: var(--color-text-primary);
--list-buttons-gap:var(--global-spacing-2);
  /* List Item Active */
  --list-item-active-border: 1px solid var(--color-border-tertiary);
  --list-item-active-background: var(--color-background-secondary);
  --list-item-active: var(--color-background-secondary);
  --list-active-bg: var(--global-color-blue-100);


  /* List Item Title */
  --list-item-title-color: var(--color-text-primary);
  --list-item-title-font: var(--font-body-2);
  --list-item-title-size: var(--global-font-size-md);
  --list-item-title-weight: var(--global-font-weight-medium);

  /* List Item Subtitle */
  --list-item-subtitle-color: var(--color-text-secondary);
  --list-item-subtitle-font: var(--font-body-2);
  --list-item-subtitle-size: var(--global-font-size-sm);
  --list-item-subtitle-weight: var(--global-font-weight-regular);

  /* List Item Icon */
  --list-item-icon-size: var(--global-icon-size-lg);
  --list-background-color:var(--color-brand-primary);
  --list-disable-color:var(--global-color-gray-600);

  --list-error-text: var(--color-text-error);
  --list-error-font: var(--font-label);
  --list-error-spacing: var(--global-spacing-1);
  --list-error-font-size: var(--global-font-size-xs);
  
}