/**
 * Component: Timepicker
 * Purpose: Timepicker component tokens for time selection
 */

:root {
  /* Timepicker Base */
  --timepicker-background: var(--color-background-primary);
  --timepicker-border: 1px solid #e5e7eb;
  --timepicker-border-radius: var(--global-radius-md);
  --timepicker-shadow: var(--global-elevation-02);

  /* Timepicker Input Container */
  --timepicker-input-height: 48px;
  --timepicker-input-border-radius: 8px;
  --timepicker-input-min-width: 200px;
  --timepicker-input-padding-horizontal: 10px;

  /* Timepicker Display */
  --timepicker-display-text: var(--color-text-primary);
  --timepicker-display-font: var(--font-body-1);

  /* Timepicker Input Fields */
  --timepicker-input-background: var(--color-background-primary);
  --timepicker-input-text: var(--color-text-primary);
  --timepicker-input-border-color: var(--color-brand-primary);
  --timepicker-input-border-width: 1px;
  --timepicker-input-border-radius-small: 4px;
  --timepicker-input-padding-small: 1px;
  --timepicker-input-shadow-overlay: var(--global-elevation-03);

  /* Timepicker Scroll Areas */
  --timepicker-scroll-background: var(--color-background-primary);
  --timepicker-scroll-max-height: 12rem;
  --timepicker-scroll-gap-hours-minutes: 3px;
  --timepicker-scroll-gap-minutes-period: 10px;

  /* Timepicker Time Items */
  --timepicker-time-item-background: transparent;
  --timepicker-time-item-text: var(--color-text-primary);
  --timepicker-time-item-font: var(--font-body-2);
  --timepicker-time-item-padding: var(--global-spacing-2);
  --timepicker-time-item-border-radius: var(--global-radius-sm);
  --timepicker-time-item-transition: all var(--global-motion-duration-standard) var(--global-motion-easing-standard);
  --timepicker-time-item-height: 32px;

  --timepicker-time-item-hover-background: var(--color-surface-subtle-hover);
  --timepicker-time-item-hover-text: var(--color-text-primary);

  --timepicker-time-item-selected-text: var(--color-brand-primary);
  --timepicker-time-item-selected-font-weight: 500;

  /* Timepicker Icon */
  --timepicker-icon-background: var(--color-surface-subtle);
  --timepicker-icon-color: var(--color-text-secondary);
  --timepicker-icon-padding: var(--global-spacing-2);
  --timepicker-icon-size: 32px;
  --timepicker-icon-border-radius: 50%;
  --timepicker-icon-margin-left: var(--global-spacing-3);
}