/**
 * =========================================================================
 * Play+ Design System: Tooltip Component Tokens
 *
 * Component-specific semantic tokens for tooltip elements.
 * These tokens reference base semantic tokens and provide
 * contextual meaning for tooltip styling.
 * =========================================================================
 */

:root {
  /* --- Tooltip Base --- */
  --tooltip-background: var(--color-background-primary);
  --tooltip-text: var(--color-text-primary);
  --tooltip-font: var(--font-body-2);
  --tooltip-opacity: 1;
  --tooltip-border-radius: var(--global-radius-sm);
  --tooltip-shadow: var(--global-elevation-03);
  --tooltip-padding: 8px 12px;
  --tooltip-max-width: 300px;
  --tooltip-z-index: 999999;
  --tooltip-border-color: var(--color-border-default);

  /* --- Tooltip Arrow --- */
  --tooltip-arrow-size: 8px;
  --tooltip-arrow-color: var(--tooltip-background);
  --tooltip-arrow-border: var(--tooltip-arrow-size) solid transparent;

  /* --- Tooltip Positions --- */
  --tooltip-position-top-arrow: var(--tooltip-arrow-border);
  --tooltip-position-top-arrow-border-color: var(--tooltip-arrow-color) transparent transparent transparent;

  --tooltip-position-bottom-arrow: var(--tooltip-arrow-border);
  --tooltip-position-bottom-arrow-border-color: transparent transparent var(--tooltip-arrow-color) transparent;

  --tooltip-position-left-arrow: var(--tooltip-arrow-border);
  --tooltip-position-left-arrow-border-color: transparent transparent transparent var(--tooltip-arrow-color);

  --tooltip-position-right-arrow: var(--tooltip-arrow-border);
  --tooltip-position-right-arrow-border-color: transparent var(--tooltip-arrow-color) transparent transparent;

  /* --- Tooltip Sizes --- */
  --tooltip-size-sm-font: var(--font-label);
  --tooltip-size-sm-padding: 8px 12px;
  --tooltip-size-sm-max-width: 200px;

  --tooltip-size-md-font: var(--font-body-2);
  --tooltip-size-md-padding: 8px 12px;
  --tooltip-size-md-max-width: 300px;

  --tooltip-size-lg-font: var(--font-body-1);
  --tooltip-size-lg-padding: 8px 12px;
  --tooltip-size-lg-max-width: 400px;

  /* --- Tooltip Animation --- */
  --tooltip-transition: var(--motion-pattern-fade);
  --tooltip-animation-duration: var(--global-motion-duration-standard);
  --tooltip-animation-easing: var(--global-motion-easing-enter);

  /* --- Tooltip Delay --- */
  --tooltip-show-delay: 0.5s;
  --tooltip-hide-delay: 0s;



  /* --- Tooltip Title --- */
  --tooltip-title-color: var(--color-text-primary);
  --tooltip-title-font-family: var(--global-font-family-body);
  --tooltip-title-font-size: var(--global-font-size-sm);
  --tooltip-title-font-weight: var(--global-font-weight-semibold);

  /* --- Tooltip Description --- */
  --tooltip-description-color: var(--color-text-primary);
  --tooltip-description-font-family: var(--global-font-family-body);
  --tooltip-description-font-size: 10px;
  --tooltip-description-font-weight: var(--global-font-weight-medium);

  /* --- Tooltip Icon --- */
  --tooltip-icon-size: 12px;
  --tooltip-icon-color: #000;
  --tooltip-icon-gap: 4px;

  /* --- Tooltip Content Max Width --- */
  --tooltip-content-max-width: 150px;
}