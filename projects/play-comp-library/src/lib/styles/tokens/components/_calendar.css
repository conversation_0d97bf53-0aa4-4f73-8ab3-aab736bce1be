/**
 * Component: Calendar
 * Purpose: Calendar component tokens for date selection and display
 */

:root {
  /* Calendar Base */
  --calendar-font-family: var(--font-family-body);

  /* Calendar Input Button */
  --calendar-input-btn-background: var(--color-surface-subtle);
  --calendar-input-btn-hover-background: var(--global-color-pink-500);
  --calendar-input-btn-border-radius: var(--global-radius-sm);
  --calendar-input-btn-margin: 8px;
  --calendar-input-btn-icon-right: 1rem;
  --calendar-input-btn-icon-padding: 0.375rem;

  /* Calendar Navigation */
  --calendar-nav-button-background: var(--global-color-rose-500);
  --calendar-nav-button-hover-background: var(--global-color-pink-500);
  --calendar-nav-button-background-color: white;
  --calendar-nav-button-border-radius: 50%;
  --calendar-nav-button-height-width: 2.25rem;
  --calendar-nav-button-padding: var(--calendar-size-sm-padding);
  --calendar-nav-controls-gap: 0.25rem;
  --calendar-nav-controls-gap-large: var(--calendar-size-sm-padding);
  --calendar-nav-shadow: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1);
  --calendar-nav-shadow-hover: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.15);

  /* Calendar Month/Year Display */
  --calendar-month-text: var(--color-surface-interactive-default);
  --calendar-year-text: var(--color-surface-interactive-default);
  --calendar-month-year-selected-color: var(--global-color-rose-500);
  --calendar-month-year-selector-padding: 0.5rem 0;
  --calendar-month-year-gap: 0.75rem;

  /* Calendar Grid */
  --calendar-cell-border-radius: var(--global-radius-sm);
  --calendar-grid-margin-top: 0.5rem;

  /* Calendar Day Names */
  --calendar-day-name-text: var(--global-color-gray-700);

  /* Calendar Date States */
  --calendar-date-text: var(--color-text-primary);
  --calendar-date-hover-background: var(--global-color-rose-500);
  --calendar-date-selected-background: var(--color-surface-interactive-default);
  --calendar-date-selected-text: var(--color-text-on-brand);
  --calendar-date-today-border: 2px solid var(--color-border-interactive);
  --calendar-date-disabled-text: var(--color-text-disabled);

  /* Calendar Range Selection */
  --calendar-range-start-background: var(--color-surface-interactive-default);
  --calendar-range-start-text: var(--color-text-on-brand);

  /* Calendar Sizes */
  --calendar-size-sm-font: var(--global-font-size-xs);
  --calendar-size-sm-padding: var(--global-spacing-2);
  --calendar-size-md-font: var(--global-font-size-sm);

  /* Calendar Input */
  --calendar-input-background: var(--color-background-primary);
  --calendar-input-border: 1px solid var(--color-border-default);
  --calendar-input-border-radius: var(--global-radius-md);
  --calendar-input-text: var(--color-text-primary);
  --calendar-input-padding: var(--global-spacing-3);
  --calendar-input-padding-right: 0.5rem;
  --calendar-input-width-single: 16.5rem;
  --calendar-input-width-range: 21.75rem;
  --calendar-input-height: 3rem;

  /* Calendar Input Segments */
  --calendar-segment-day-month-width: 1.5rem;
  --calendar-segment-year-width: 2.5rem;

  /* Calendar Popup */
  --calendar-popup-background: var(--color-background-primary);
  --calendar-popup-border: 1px solid var(--color-border-default);
  --calendar-popup-border-radius: var(--global-radius-lg);
  --calendar-popup-shadow: var(--global-elevation-03);
  --calendar-popup-z-index: 1000;
  --calendar-popup-padding: 1rem;
  --calendar-popup-margin-top: 5px;

  /* Calendar Glass Surface Effect */
  --calendar-glass-background-fallback: rgba(255, 255, 255, 0.4);
  --calendar-glass-background-medium: rgba(255, 255, 255, 0.5);
  --calendar-glass-background-strong: rgba(255, 255, 255, 0.75);
  --calendar-glass-background-max: rgba(255, 255, 255, 1.0);
  --calendar-glass-backdrop-filter: blur(15px) saturate(180%);
  --calendar-glass-border: 2px solid rgba(255, 255, 255, 0.3);
  --calendar-glass-box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  --calendar-glass-text-color: #222;

  /* Calendar Selector Shape Constants */
  --calendar-circle-selector-radius: 50%;
  --calendar-square-selector-radius: 0.5rem;
}