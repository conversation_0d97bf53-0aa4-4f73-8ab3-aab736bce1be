/* Design Tokens */

:root {
    /*fileupload*/
    --fileupload-font-family-body: var(--font-family-body);
    --fileupload-font-family-heading: var(--global-font-family-heading);
    --fileupload-font-size-md: var(--global-font-size-md);
    --fileupload-font-size-xs: var(--global-font-size-xs);
    --fileupload-font-size-sm: var(--global-font-size-sm);
    --fileupload-font-size-lg: var(--global-font-size-lg);

    --fileupload-font-weight-regular: var(--global-font-weight-regular);
    --fileupload-font-weight-semibold: var(--global-font-weight-semibold);

    --fileupload-primary-text-disabled: var(--color-text-placeholder);
    --fileupload-background-default: var(--color-background-primary);

    --fileupload-highlighted-text-pink: var(--global-color-pink-500);
    --fileupload-text-color-primary: var(--color-text-primary);
    --fileupload-active-button-bg: var(--global-color-pink-500);
    --fileupload-disabled-button-bg: var(--color-background-disabled);
    --fileupload-heading-color: var(--color-text-primary);

    --fileupload-selected-filename-color: var(--color-text-primary);
    --fileupload-border-color: var(--color-text-disabled);
    --fileupload-danger-background: var(--global-color-red-500);
    --fileupload-text-color-link: var(--color-text-success);


}