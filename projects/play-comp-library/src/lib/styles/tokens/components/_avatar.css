/**
 * Component: Avatar
 * Purpose: Avatar component tokens for user profile images and status indicators
 */

:root {
  /* Avatar Base */
  --avatar-size-us: var(--global-spacing-3);
  --avatar-size-xs: var(--global-spacing-5);
  --avatar-size-sm: var(--global-spacing-10);
  --avatar-size-md: 2.75rem;
  --avatar-size-lg: var(--global-spacing-12);
  --avatar-size-xl: 3.25rem;
  --avatar-size-ul:5rem;

  /* Avatar Border */
  --avatar-border-radius: var(--global-radius-circle);
  --avatar-border-width: 0.125rem;
  --avatar-border-color: var(--color-border-subtle);

  /* Avatar Background */
  --avatar-background: var(--color-surface-subtle);
  --avatar-fallback-background: var(--color-surface-subtle);

  /* Avatar Text */
  --avatar-text-color: var(--global-color-black);
  --avatar-text-font: var(--font-label);
  --avatar-text-size-sm: var(--global-font-size-xs);
  --avatar-text-size-md: var(--global-font-size-sm);
  --avatar-text-size-lg: var(--global-font-size-md);
  --avatar-text-size-xl: var(--global-font-size-lg);

  /* Avatar Status Badge */
  --avatar-status-size: 0.75rem;
  --avatar-status-border-width: 0.125rem;
  --avatar-status-border-color: var(--color-background-primary);
  --avatar-status-online: var(--global-color-green-500);
  --avatar-status-offline: var(--global-color-gray-400);
  --avatar-status-away: var(--global-color-yellow-500);
  --avatar-status-busy: var(--global-color-red-500);
  --avatar-status-font-size:10px;
  --avatar-status-color:var(--global-color-black);
  --avatar-status-font-weight:var(--global-font-weight-semibold);
  --avatar-status-font-family:var(--global-font-family-heading);
  --avatar-status-font-size-ul:var(--global-font-size-lg);
   --avatar-status-font-size-lg:var(--global-font-size-md);
  --avatar-status-line-height:var(--global-spacing-5);
   --avatar-status-font-size-md:var(--global-font-size-sm);
   --avatar-status-font-size-sm:var(--global-font-size-xs);

  /* Avatar Profile Text */
  --avatar-profile-color: var(--color-text-secondary);
  --avatar-profile-font-family: var(--global-font-family-body);
  --avatar-profile-font-weight: var(--global-font-weight-regular);
  --avatar-profile-font-size-xl: var(--global-font-size-sm); 
  --avatar-profile-font-size-lg: var(--global-font-size-xs); 
  --avatar-profile-font-size-md: 10px; 
  --avatar-profile-font-size-sm: var(--global-spacing-2); 
  --avatar-profile-line-height: 22px;

  /* Avatar Additional Text */
  --avatar-additional-color: var(--color-text-secondary);
  --avatar-additional-font-family: var(--global-font-family-body);
  --avatar-additional-font-weight: var(--global-font-weight-regular);
  --avatar-additional-font-size-xl: var(--global-font-size-xs); 
  --avatar-additional-font-size-lg: 10px; 
  --avatar-additional-font-size-md: var(--global-spacing-2); 
  --avatar-additional-font-size-sm: 6px;  
  --avatar-additional-line-height:  14.4px;

  /* Avatar Notification Badge */
  --avatar-notification-size: 1.25rem;
  --avatar-notification-background: var(--color-surface-error);
  --avatar-notification-text: var(--color-text-on-brand);
  --avatar-notification-font: var(--font-label);
  --avatar-notification-border-radius: var(--global-radius-pill);
  --avatar-notification-border: 0.125rem solid var(--color-background-primary);

  /* Avatar Group */
  --avatar-group-spacing: -0.5rem;
  --avatar-group-border-width: 0.125rem;
  --avatar-group-border-color: var(--color-background-primary);

  --avatar-padding:var(--global-spacing-2);
  --avatar-container-gap:var(--global-spacing-2);

  /* Avatar Wrapper Gaps by Size */
  --avatar-wrapper-gap-ul: 10px; /* Ultra large */
  --avatar-wrapper-gap-xl: 8px; /* Extra large */
  --avatar-wrapper-gap-lg: 6px; /* Large */
  --avatar-wrapper-gap-md: 4px; /* Medium */
  --avatar-wrapper-gap-sm: 2px; /* Small */
  --avatar-wrapper-gap-xs: 2px; /* Extra small */
  --avatar-wrapper-gap-us: 2px; /* Ultra small */
  /* --avatar-gap:10px; */
  --avatar-border-radius-sm:var(--global-spacing-2);
  --avatar-border-radius-md:var(--global-spacing-2);
  --avatar-border-radius-lg:var(--global-spacing-3);
  --avatar-border-radius-xl:var(--global-spacing-3);
  --avatar-border-radius-ul:var(--global-spacing-3);

  --avatar-border-radius-xs:6px;
  --avatar-border-radius-us:2px;

  --avatar-line-height:var(--global-spacing-4);

  --avatar-badge-poisition:-8px;
  --avatar-webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  --avatar-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  --avatar-border-thickness:3px;
  --avatar-proccessdone-position:-3px;
  --avatar-processdone-border-thickness:3px;
  --avatar-pill-border-radius:var(--global-radius-circle);
  --avatar-aspect-ratio:1/1;
  --avatar-flex-shrink:0;
  --avatar-animation-speed:2s;
}
