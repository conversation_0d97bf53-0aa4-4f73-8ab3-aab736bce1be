/**
 * Component: Popup
 * Purpose: Popup component tokens for modal dialogs and overlays
 */

:root {
  /* Popup Base */
  --popup-background: var(--color-background-primary);
  --popup-border: 1px solid var(--color-border-default);
  --popup-border-radius: var(--global-radius-lg);
  --popup-shadow: var(--global-elevation-03);
  --popup-padding: var(--global-spacing-4);
  --popup-max-width: 32rem;
  --popup-max-height: 80vh;
  --popup-z-index: 1000;

  /* Popup Black color */
  --popup-black-color: var(--color-text-primary);

  /* Popup Overlay */
  --popup-overlay-background: rgba(0, 0, 0, 0.5);
  --popup-overlay-z-index: 999;

  /* Popup Content Padding */
  --popup-content-padding-xl: var(--global-spacing-5);
  --popup-content-gap: var(--global-spacing-3);

  /* Popup Header */
  --popup-header-background: var(--color-background-primary);
  --popup-header-padding: var(--global-spacing-4);
  --popup-header-border-bottom: 1px solid var(--color-border-subtle);
  --popup-header-border-radius: var(--global-radius-lg) var(--global-radius-lg) 0 0;

  --popup-heading-text: var(--typography-heading-h4-text);
  --popup-heading-font: var(--typography-heading-h4-font);
  --popup-heading-size: var(--global-font-size-lg);
  --popup-heading-weight: var(--global-font-weight-semibold);
  --popup-heading-line-height: var(--global-line-height-tight);
  --popup-heading-margin: 0;

  /* Popup Content */
  --popup-content-background: var(--color-background-primary);
  --popup-content-padding: var(--global-spacing-4);
  --popup-content-border-radius: 0 0 var(--global-radius-lg) var(--global-radius-lg);

  --popup-description-text: var(--typography-body-1-text);
  --popup-description-font: var(--typography-body-1-font);
  --popup-description-size: var(--global-font-size-md);
  --popup-description-weight: var(--global-font-weight-regular);
  --popup-description-line-height: var(--global-line-height-normal);
  --popup-description-margin: var(--global-spacing-2) 0 0 0;



  /* =========================================================================
   * SHARED DIALOG TOKENS (All States: Success, Error, Warning, Info)
   * ========================================================================= */

  /* --- Shared Dialog Base --- */
  --dialog-background: var(--global-color-white);
  --dialog-border-radius: var(--global-radius-md);
  --dialog-shadow: var(--global-elevation-03);
  --dialog-border-bottom-width: var(--global-spacing-1);
  --dialog-border-bottom-height: var(--global-spacing-1);

  /* --- Shared Dialog Content --- */
  --dialog-content-gap: var(--global-spacing-3);
  --dialog-content-padding: var(--global-spacing-4);

  /* --- Shared Dialog Icon --- */
  --dialog-icon-color: var(--global-color-white);
  --dialog-icon-border-width: var(--global-spacing-1);
  --dialog-close-icon-size: var(--global-icon-size-sm);

  /* --- Shared Dialog State Colors --- */
  --dialog-success-color: var(--color-text-success);
  --dialog-error-color: var(--color-text-error);
  --dialog-warning-color: var(--color-text-warning);
  --dialog-info-color: var(--color-text-info);
  --dialog-primary-color: var(--color-brand-primary);
  --dialog-icon-text-color: var(--global-color-white);

  /* --- Shared Dialog Typography --- */
  --dialog-title-font-family: var(--typography-dialog-title-font-family);
  --dialog-title-font-weight: var(--typography-dialog-title-font-weight);
  --dialog-title-line-height-tight: var(--typography-dialog-title-lg-line-height);
  --dialog-text-color: var(--typography-dialog-description-color);
  --dialog-text-font-family: var(--typography-dialog-description-font-family);
  --dialog-text-font-weight: var(--typography-dialog-description-font-weight);
  --dialog-text-line-height: var(--typography-dialog-description-lg-line-height);
  --dialog-text-margin-top: var(--global-spacing-1);

  /* --- Shared Dialog Actions --- */
  --dialog-actions-gap: var(--global-spacing-3);
  --dialog-actions-margin-top: var(--global-spacing-5);

  /* --- Shared Dialog Content Spacing --- */
  --dialog-content-gap: var(--global-spacing-3);
  --dialog-icon-margin-bottom: var(--global-spacing-2);
  --dialog-max-width: 400px;

  /* --- Shared Dialog Loading/Spinner --- */
  --dialog-spinner-size: 50px;
  --dialog-spinner-border-width: var(--global-spacing-1);
  --dialog-spinner-border-color: var(--color-background-subtle);
  --dialog-spinner-border-top-color: var(--dialog-primary-color);

  /* --- Shared Dialog Progress Bar --- */
  --dialog-progress-max-width: 300px;
  --dialog-progress-bar-height: var(--global-spacing-2);
  --dialog-progress-bar-border-radius: var(--global-spacing-1);
  --dialog-progress-text-font-size: var(--typography-dialog-description-lg-size);

  /* --- Shared Dialog WITH ACTIONS --- */
  --dialog-with-actions-width: 400px;
  --dialog-with-actions-height: 166px;
  --dialog-with-actions-content-padding-top: var(--global-spacing-5);
  --dialog-with-actions-content-padding-horizontal: var(--global-spacing-4);
  --dialog-with-actions-content-padding-bottom: var(--global-spacing-3);
  --dialog-with-actions-content-gap: var(--global-spacing-4);
  --dialog-with-actions-icon-size: 24px;
  --dialog-with-actions-title-font-size: var(--typography-dialog-title-lg-size);
  --dialog-with-actions-title-line-height: var(--typography-dialog-title-lg-line-height);
  --dialog-with-actions-text-font-size: var(--typography-dialog-description-lg-size);
  --dialog-with-actions-text-line-height: var(--typography-dialog-description-lg-line-height);
  --dialog-with-actions-actions-padding: var(--global-spacing-4);

  /* --- Shared Dialog Size: Large (400px × 64px) --- */
  --dialog-lg-width: 400px;
  --dialog-lg-min-height: 64px;
  --dialog-lg-content-padding-top: var(--global-spacing-5);
  --dialog-lg-content-padding-horizontal: var(--global-spacing-5);
  --dialog-lg-content-padding-bottom: var(--global-spacing-2);
  --dialog-lg-content-gap: var(--global-spacing-3);
  --dialog-lg-icon-size: var(--global-icon-size-xl);
  --dialog-lg-title-font-size: var(--typography-dialog-title-lg-size);
  --dialog-lg-title-line-height: var(--typography-dialog-title-lg-line-height);
  --dialog-lg-text-font-size: var(--typography-dialog-description-lg-size);
  --dialog-lg-text-line-height: var(--typography-dialog-description-lg-line-height);

  /* --- Shared Dialog Size: Medium (327px × 64px) --- */
  --dialog-md-width: 327px;
  --dialog-md-min-height: 64px;
  --dialog-md-content-padding: var(--global-spacing-4);
  --dialog-md-content-gap: var(--global-spacing-2);
  --dialog-md-icon-size: var(--global-icon-size-lg);
  --dialog-md-title-font-size: var(--typography-dialog-title-md-size);
  --dialog-md-title-line-height: var(--typography-dialog-title-md-line-height);
  --dialog-md-text-font-size: var(--typography-dialog-description-md-size);
  --dialog-md-text-line-height: var(--typography-dialog-description-md-line-height);

  /* --- Shared Dialog Size: Small (270px × 64px) --- */
  --dialog-sm-width: 270px;
  --dialog-sm-min-height: 64px;
  --dialog-sm-content-padding-vertical: var(--global-spacing-3);
  --dialog-sm-content-padding-horizontal: var(--global-spacing-4);
  --dialog-sm-content-gap: var(--global-spacing-2);
  --dialog-sm-icon-size: var(--global-icon-size-md);
  --dialog-sm-title-font-size: var(--typography-dialog-title-sm-size);
  --dialog-sm-title-line-height: var(--typography-dialog-title-sm-line-height);
  --dialog-sm-text-font-size: var(--typography-dialog-description-sm-size);
  --dialog-sm-text-line-height: var(--typography-dialog-description-sm-line-height);

  /* =========================================================================
   * STATE-SPECIFIC DIALOG TOKENS (Colors Only)
   * ========================================================================= */

  /* --- Success Dialog Colors --- */
  --success-dialog-border-bottom-color: var(--global-color-green-500);
  --success-dialog-icon-background: var(--global-color-green-500);
  --success-dialog-icon-border-color: var(--global-color-green-600);
  --success-dialog-title-color: var(--global-color-green-500);

  /* --- Error Dialog Colors --- */
  --error-dialog-border-bottom-color: var(--global-color-red-500);
  --error-dialog-icon-background: var(--global-color-red-500);
  --error-dialog-icon-border-color: var(--global-color-red-600);
  --error-dialog-title-color: var(--global-color-red-500);

  /* --- Warning Dialog Colors --- */
  --warning-dialog-border-bottom-color: var(--global-color-yellow-500);
  --warning-dialog-icon-background: var(--global-color-yellow-500);
  --warning-dialog-icon-border-color: var(--global-color-yellow-600);
  --warning-dialog-title-color: var(--global-color-yellow-500);

  /* --- Info Dialog Colors --- */
  --info-dialog-border-bottom-color: var(--global-color-blue-500);
  --info-dialog-icon-background: var(--global-color-blue-500);
  --info-dialog-icon-border-color: var(--global-color-blue-600);
  --info-dialog-title-color: var(--global-color-blue-500);

  /* Success Dialog Aliases */
  --success-dialog-background: var(--dialog-background);
  --success-dialog-border-radius: var(--dialog-border-radius);
  --success-dialog-shadow: var(--dialog-shadow);
  --success-dialog-border-bottom-width: var(--dialog-border-bottom-width);
  --success-dialog-content-gap: var(--dialog-content-gap);
  --success-dialog-content-padding: var(--dialog-content-padding);
  --success-dialog-icon-color: var(--dialog-icon-color);
  --success-dialog-icon-border-width: var(--dialog-icon-border-width);
  --success-dialog-title-font-weight: var(--dialog-title-font-weight);
  --success-dialog-text-color: var(--dialog-text-color);
  --success-dialog-text-line-height: var(--dialog-text-line-height);
  --success-dialog-actions-gap: var(--dialog-actions-gap);
  --success-dialog-with-actions-width: var(--dialog-with-actions-width);
  --success-dialog-with-actions-height: var(--dialog-with-actions-height);
  --success-dialog-with-actions-content-padding-top: var(--dialog-with-actions-content-padding-top);
  --success-dialog-with-actions-content-padding-horizontal: var(--dialog-with-actions-content-padding-horizontal);
  --success-dialog-with-actions-content-padding-bottom: var(--dialog-with-actions-content-padding-bottom);
  --success-dialog-with-actions-content-gap: var(--dialog-with-actions-content-gap);
  --success-dialog-with-actions-icon-size: var(--dialog-with-actions-icon-size);
  --success-dialog-with-actions-title-font-size: var(--dialog-with-actions-title-font-size);
  --success-dialog-with-actions-title-line-height: var(--dialog-with-actions-title-line-height);
  --success-dialog-with-actions-text-font-size: var(--dialog-with-actions-text-font-size);
  --success-dialog-with-actions-text-line-height: var(--dialog-with-actions-text-line-height);
  --success-dialog-with-actions-actions-padding: var(--dialog-with-actions-actions-padding);
  --success-dialog-lg-width: var(--dialog-lg-width);
  --success-dialog-lg-min-height: var(--dialog-lg-min-height);
  --success-dialog-lg-content-padding-top: var(--dialog-lg-content-padding-top);
  --success-dialog-lg-content-padding-horizontal: var(--dialog-lg-content-padding-horizontal);
  --success-dialog-lg-content-padding-bottom: var(--dialog-lg-content-padding-bottom);
  --success-dialog-lg-content-gap: var(--dialog-lg-content-gap);
  --success-dialog-lg-icon-size: var(--dialog-lg-icon-size);
  --success-dialog-lg-title-font-size: var(--dialog-lg-title-font-size);
  --success-dialog-lg-title-line-height: var(--dialog-lg-title-line-height);
  --success-dialog-lg-text-font-size: var(--dialog-lg-text-font-size);
  --success-dialog-lg-text-line-height: var(--dialog-lg-text-line-height);
  --success-dialog-md-width: var(--dialog-md-width);
  --success-dialog-md-min-height: var(--dialog-md-min-height);
  --success-dialog-md-content-padding: var(--dialog-md-content-padding);
  --success-dialog-md-content-gap: var(--dialog-md-content-gap);
  --success-dialog-md-icon-size: var(--dialog-md-icon-size);
  --success-dialog-md-title-font-size: var(--dialog-md-title-font-size);
  --success-dialog-md-title-line-height: var(--dialog-md-title-line-height);
  --success-dialog-md-text-font-size: var(--dialog-md-text-font-size);
  --success-dialog-md-text-line-height: var(--dialog-md-text-line-height);
  --success-dialog-sm-width: var(--dialog-sm-width);
  --success-dialog-sm-min-height: var(--dialog-sm-min-height);
  --success-dialog-sm-content-padding-vertical: var(--dialog-sm-content-padding-vertical);
  --success-dialog-sm-content-padding-horizontal: var(--dialog-sm-content-padding-horizontal);
  --success-dialog-sm-content-gap: var(--dialog-sm-content-gap);
  --success-dialog-sm-icon-size: var(--dialog-sm-icon-size);
  --success-dialog-sm-title-font-size: var(--dialog-sm-title-font-size);
  --success-dialog-sm-title-line-height: var(--dialog-sm-title-line-height);
  --success-dialog-sm-text-font-size: var(--dialog-sm-text-font-size);
  --success-dialog-sm-text-line-height: var(--dialog-sm-text-line-height);

  /* Error Dialog Aliases */
  --error-dialog-background: var(--dialog-background);
  --error-dialog-border-radius: var(--dialog-border-radius);
  --error-dialog-shadow: var(--dialog-shadow);
  --error-dialog-border-bottom-width: var(--dialog-border-bottom-width);
  --error-dialog-content-gap: var(--dialog-content-gap);
  --error-dialog-content-padding: var(--dialog-content-padding);
  --error-dialog-icon-color: var(--dialog-icon-color);
  --error-dialog-icon-border-width: var(--dialog-icon-border-width);
  --error-dialog-title-font-weight: var(--dialog-title-font-weight);
  --error-dialog-text-color: var(--dialog-text-color);
  --error-dialog-text-line-height: var(--dialog-text-line-height);
  --error-dialog-actions-gap: var(--dialog-actions-gap);
  --error-dialog-with-actions-width: var(--dialog-with-actions-width);
  --error-dialog-with-actions-height: var(--dialog-with-actions-height);
  --error-dialog-with-actions-content-padding-top: var(--dialog-with-actions-content-padding-top);
  --error-dialog-with-actions-content-padding-horizontal: var(--dialog-with-actions-content-padding-horizontal);
  --error-dialog-with-actions-content-padding-bottom: var(--dialog-with-actions-content-padding-bottom);
  --error-dialog-with-actions-content-gap: var(--dialog-with-actions-content-gap);
  --error-dialog-with-actions-icon-size: var(--dialog-with-actions-icon-size);
  --error-dialog-with-actions-title-font-size: var(--dialog-with-actions-title-font-size);
  --error-dialog-with-actions-title-line-height: var(--dialog-with-actions-title-line-height);
  --error-dialog-with-actions-text-font-size: var(--dialog-with-actions-text-font-size);
  --error-dialog-with-actions-text-line-height: var(--dialog-with-actions-text-line-height);
  --error-dialog-with-actions-actions-padding: var(--dialog-with-actions-actions-padding);
  --error-dialog-lg-width: var(--dialog-lg-width);
  --error-dialog-lg-min-height: var(--dialog-lg-min-height);
  --error-dialog-md-width: var(--dialog-md-width);
  --error-dialog-md-min-height: var(--dialog-md-min-height);
  --error-dialog-sm-width: var(--dialog-sm-width);
  --error-dialog-sm-min-height: var(--dialog-sm-min-height);

  /* Info Dialog Aliases */
  --info-dialog-background: var(--dialog-background);
  --info-dialog-border-radius: var(--dialog-border-radius);
  --info-dialog-shadow: var(--dialog-shadow);
  --info-dialog-border-bottom-width: var(--dialog-border-bottom-width);
  --info-dialog-content-gap: var(--dialog-content-gap);
  --info-dialog-content-padding: var(--dialog-content-padding);
  --info-dialog-icon-color: var(--dialog-icon-color);
  --info-dialog-icon-border-width: var(--dialog-icon-border-width);
  --info-dialog-title-font-weight: var(--dialog-title-font-weight);
  --info-dialog-text-color: var(--dialog-text-color);
  --info-dialog-text-line-height: var(--dialog-text-line-height);
  --info-dialog-actions-gap: var(--dialog-actions-gap);
  --info-dialog-with-actions-width: var(--dialog-with-actions-width);
  --info-dialog-with-actions-height: var(--dialog-with-actions-height);
  --info-dialog-with-actions-content-padding-top: var(--dialog-with-actions-content-padding-top);
  --info-dialog-with-actions-content-padding-horizontal: var(--dialog-with-actions-content-padding-horizontal);
  --info-dialog-with-actions-content-padding-bottom: var(--dialog-with-actions-content-padding-bottom);
  --info-dialog-with-actions-content-gap: var(--dialog-with-actions-content-gap);
  --info-dialog-with-actions-icon-size: var(--dialog-with-actions-icon-size);
  --info-dialog-with-actions-title-font-size: var(--dialog-with-actions-title-font-size);
  --info-dialog-with-actions-title-line-height: var(--dialog-with-actions-title-line-height);
  --info-dialog-with-actions-text-font-size: var(--dialog-with-actions-text-font-size);
  --info-dialog-with-actions-text-line-height: var(--dialog-with-actions-text-line-height);
  --info-dialog-with-actions-actions-padding: var(--dialog-with-actions-actions-padding);
  --info-dialog-lg-width: var(--dialog-lg-width);
  --info-dialog-lg-min-height: var(--dialog-lg-min-height);
  --info-dialog-md-width: var(--dialog-md-width);
  --info-dialog-md-min-height: var(--dialog-md-min-height);
  --info-dialog-sm-width: var(--dialog-sm-width);
  --info-dialog-sm-min-height: var(--dialog-sm-min-height);

  /* Warning Dialog Aliases */
  --warning-dialog-background: var(--dialog-background);
  --warning-dialog-border-radius: var(--dialog-border-radius);
  --warning-dialog-shadow: var(--dialog-shadow);
  --warning-dialog-border-bottom-width: var(--dialog-border-bottom-width);
  --warning-dialog-content-gap: var(--dialog-content-gap);
  --warning-dialog-content-padding: var(--dialog-content-padding);
  --warning-dialog-icon-color: var(--dialog-icon-color);
  --warning-dialog-icon-border-width: var(--dialog-icon-border-width);
  --warning-dialog-title-font-weight: var(--dialog-title-font-weight);
  --warning-dialog-text-color: var(--dialog-text-color);
  --warning-dialog-text-line-height: var(--dialog-text-line-height);
  --warning-dialog-actions-gap: var(--dialog-actions-gap);
  --warning-dialog-with-actions-width: var(--dialog-with-actions-width);
  --warning-dialog-with-actions-height: var(--dialog-with-actions-height);
  --warning-dialog-with-actions-content-padding-top: var(--dialog-with-actions-content-padding-top);
  --warning-dialog-with-actions-content-padding-horizontal: var(--dialog-with-actions-content-padding-horizontal);
  --warning-dialog-with-actions-content-padding-bottom: var(--dialog-with-actions-content-padding-bottom);
  --warning-dialog-with-actions-content-gap: var(--dialog-with-actions-content-gap);
  --warning-dialog-with-actions-icon-size: var(--dialog-with-actions-icon-size);
  --warning-dialog-with-actions-title-font-size: var(--dialog-with-actions-title-font-size);
  --warning-dialog-with-actions-title-line-height: var(--dialog-with-actions-title-line-height);
  --warning-dialog-with-actions-text-font-size: var(--dialog-with-actions-text-font-size);
  --warning-dialog-with-actions-text-line-height: var(--dialog-with-actions-text-line-height);
  --warning-dialog-with-actions-actions-padding: var(--dialog-with-actions-actions-padding);
  --warning-dialog-lg-width: var(--dialog-lg-width);
  --warning-dialog-lg-min-height: var(--dialog-lg-min-height);
  --warning-dialog-md-width: var(--dialog-md-width);
  --warning-dialog-md-min-height: var(--dialog-md-min-height);
  --warning-dialog-sm-width: var(--dialog-sm-width);
  --warning-dialog-sm-min-height: var(--dialog-sm-min-height);
    /* --- Dialog Typography --- */
  /* Dialog Title Typography */
  --typography-dialog-title-font-family: var(--global-font-family-body);
  --typography-dialog-title-font-weight: var(--global-font-weight-semibold);
  --typography-dialog-title-color: var(--color-text-primary);
  --typography-dialog-title-lg-size: var(--global-font-size-md);
  --typography-dialog-title-lg-line-height: var(--global-line-height-tight);
  --typography-dialog-title-md-size: var(--global-font-size-md);
  --typography-dialog-title-md-line-height: var(--global-line-height-tight);
  --typography-dialog-title-sm-size: var(--global-font-size-xs);
  --typography-dialog-title-sm-line-height: var(--global-line-height-tight);

  /* Dialog Description Typography */
  --typography-dialog-description-font-family: var(--global-font-family-body);
  --typography-dialog-description-font-weight: var(--global-font-weight-regular);
  --typography-dialog-description-color: var(--color-text-secondary);
  --typography-dialog-description-lg-size: var(--global-font-size-sm);
  --typography-dialog-description-lg-line-height: var(--global-line-height-normal);
  --typography-dialog-description-md-size: var(--global-font-size-xs);
  --typography-dialog-description-md-line-height: var(--global-line-height-normal);
  --typography-dialog-description-sm-size: 0.625rem;
  /* 10px */
  --typography-dialog-description-sm-line-height: 0.75rem;
  /* 12px */
}