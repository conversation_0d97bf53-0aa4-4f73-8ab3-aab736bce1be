/**
 * =========================================================================
 * Play+ Design System: Enterprise Theme
 *
 * Enterprise theme with sophisticated corporate colors and professional styling.
 * Perfect for enterprise applications and business platforms.
 * =========================================================================
 */

[data-theme="enterprise"] {
  /* --- Enterprise Theme Global Color Overrides --- */

  /* Primary (Navy Blue) */
  --global-color-slate-800: #1e293b;
  --global-color-slate-900: #0f172a;

  /* Secondary (Teal) */
  --global-color-teal-600: #0d9488;
  --global-color-teal-700: #0f766e;
  --global-color-teal-100: #ccfbf1;

  /* Gray Scale */
  --global-color-gray-800: #1f2937;
  --global-color-gray-500: #6b7280;
  --global-color-gray-400: #9ca3af;
  --global-color-gray-300: #d1d5db;
  --global-color-gray-200: #e5e7eb;
  --global-color-gray-100: #f3f4f6;
  --global-color-white: #ffffff;

  /* Semantic Colors */
  --global-color-red-600: #dc2626;
  --global-color-red-700: #b91c1c;
  --global-color-green-600: #16a34a;
  --global-color-green-700: #15803d;
  --global-color-yellow-600: #ca8a04;
  --global-color-yellow-700: #a16207;

  /* --- Enterprise Theme Color Overrides --- */
  /* PRIMARY (Navy Blue) */
  --color-brand-primary: var(--global-color-slate-800);
  --color-brand-primary-hover: var(--global-color-slate-900);
  --color-brand-primary-active: var(--global-color-slate-900);
  --color-surface-interactive-primary: var(--global-color-slate-800);
  --color-surface-interactive-primary-hover: var(--global-color-slate-900);
  --color-surface-interactive-primary-active: var(--global-color-slate-900);
  --color-border-primary: var(--global-color-slate-800);
  --color-border-primary-hover: var(--global-color-slate-900);
  --color-border-primary-active: var(--global-color-slate-900);
  --color-text-primary: var(--global-color-gray-800);
  --color-text-on-primary: var(--global-color-white);

  /* SECONDARY (Teal) */
  --color-brand-secondary: var(--global-color-teal-600);
  --color-brand-secondary-hover: var(--global-color-teal-700);
  --color-brand-secondary-active: var(--global-color-teal-700);
  --color-surface-interactive-secondary: var(--global-color-teal-100);
  --color-surface-interactive-secondary-hover: var(--global-color-teal-600);
  --color-surface-interactive-secondary-active: var(--global-color-teal-700);
  --color-border-secondary: var(--global-color-teal-600);
  --color-border-secondary-hover: var(--global-color-teal-700);
  --color-border-secondary-active: var(--global-color-teal-700);
  --color-text-secondary: var(--global-color-teal-600);
  --color-text-on-secondary: var(--global-color-white);
  --color-background-secondary: var(--global-color-teal-100);

  /* INTERACTIVE ELEMENTS */
  --color-text-placeholder: var(--global-color-gray-500);
  --color-text-disabled: var(--global-color-gray-400);
  --color-text-on-brand: var(--global-color-white);
  --color-text-interactive: var(--global-color-slate-800);
  --color-text-interactive-hover: var(--global-color-slate-900);
  --color-text-success: var(--global-color-green-600);
  --color-text-error: var(--global-color-red-600);
  --color-background-primary: var(--global-color-white);
  --color-background-disabled: var(--global-color-gray-100);
  --color-surface-interactive-default: var(--global-color-slate-800);
  --color-surface-interactive-hover: var(--global-color-slate-900);
  --color-surface-interactive-active: var(--global-color-slate-900);
  --color-surface-disabled: var(--global-color-gray-200);
  --color-surface-subtle-hover: var(--global-color-gray-100);
  --color-border-default: var(--global-color-gray-400);
  --color-border-subtle: var(--global-color-gray-300);
  --color-border-interactive: var(--global-color-slate-800);
  --color-border-focus: var(--global-color-slate-800);
  --color-border-error: var(--global-color-red-600);
  --color-background-error: var(--global-color-red-600);

  /* SEMANTIC COLORS */
  --color-border-warning: var(--global-color-yellow-600);
  --color-border-success: var(--global-color-green-600);
  --color-border-info: var(--global-color-slate-800);
  --color-text-warning: var(--global-color-yellow-700);
  --color-text-success: var(--global-color-green-700);
  --color-text-info: var(--global-color-slate-800);
  --color-background-warning: var(--global-color-yellow-600);
  --color-background-success: var(--global-color-green-600);
  --color-background-info: var(--global-color-slate-800);

  /* GLASSMORPHISM */
  --glass-backdrop-blur: 10px;
  --glass-background-color: rgba(255, 255, 255, 0.8);
  --glass-border-color: rgba(255, 255, 255, 0.4);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /* =======================
     ENTERPRISE THEME: RGB OVERRIDES
     Extract RGB values from enterprise theme semantic colors
     ======================= */
  --rgb-brand-primary: 30, 41, 59;
  /* From #1e293b */
  --rgb-brand-secondary: 13, 148, 136;
  /* From #0d9488 */
  --rgb-brand-tertiary: 3, 189, 212;
  /* From #03bdd4 */
  --rgb-brand-quaternary: 250, 112, 154;
  /* From #fa709a */
  --rgb-brand-quinary: 254, 225, 64;
  /* From #fee140 */
  --rgb-brand-senary: 156, 39, 176;
  /* From #9c27b0 */
  --rgb-violet: 124, 58, 237;
  /* From #7c3aed */
  --rgb-royal-blue: 37, 99, 235;
  /* From #2563eb */
  --rgb-cyan: 3, 189, 212;
  /* From #03bdd4 */
  --rgb-spearmint: 67, 189, 144;
  /* From #43bd90 */
  --rgb-rose: 250, 112, 154;
  /* From #fa709a */
  --rgb-white: 255, 255, 255;
  /* From #ffffff */
  --rgb-black: 0, 0, 0;
  /* From #000000 */
  --rgb-neutral-100: 243, 244, 246;
  /* From #f3f4f6 */

  /* =======================
     ENTERPRISE THEME: EFFECT COLOR OVERRIDES
     Override all effect colors for proper enterprise theme adaptation
     ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  /* Navy Blue */
  --effect-color-secondary: var(--rgb-brand-secondary);
  /* Teal */
  --effect-color-accent: var(--rgb-cyan);
  /* Cyan accent for variety */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work well on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White glass/shimmer on light bg */

  /* =======================
     ENTERPRISE THEME: SEMANTIC COMPONENT TOKENS
     Theme-aware component-specific tokens using the metaphor system
     ======================= */

  /* Glass Metaphor (Theme-Aware) */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor (Theme-Aware) */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor (Theme-Aware) */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* =======================
     ENTERPRISE THEME: SOPHISTICATED GLASS CHAINING
     Override glass surface colors for enterprise theme variants
     ======================= */

  /* Enterprise theme glass surface - keep default white */
  --glass-surface-color: var(--rgb-white);

  /* Enterprise theme variant glass colors */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: 22, 163, 74;
  --glass-variant-warning: 245, 158, 11;
  --glass-variant-danger: 220, 38, 38;
  --glass-variant-info: 30, 41, 59;
  --glass-variant-purple: 156, 39, 176;
  --glass-variant-emerald: 16, 185, 129;
  --glass-variant-blue: 37, 99, 235;
  --glass-variant-aqua: 3, 189, 212;

  /* Enterprise theme effect color adjustments */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White highlights on light bg */
}
