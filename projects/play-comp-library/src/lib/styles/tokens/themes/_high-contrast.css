/**
 * =========================================================================
 * Play+ Design System: High Contrast Theme
 *
 * High contrast theme for maximum accessibility and visual clarity.
 * This theme is automatically applied when users enable high contrast mode
 * in their operating system settings.
 * =========================================================================
 */

/* High Contrast Mode Detection */
@media (prefers-contrast: more) {
  :root {
    /* =======================
       HIGH CONTRAST: CORE OVERRIDES
       Simplified colors for maximum legibility
       ======================= */

    /* --- Simplified Color Palette --- */
    /* Remove decorative colors, use only essential contrast colors */
    --color-brand-primary: #000000;
    --color-brand-primary-hover: #000000;
    --color-brand-primary-active: #000000;
    --color-brand-secondary: #000000;
    --color-brand-secondary-hover: #000000;
    --color-brand-secondary-active: #000000;

    /* --- Background Colors --- */
    /* Solid backgrounds for maximum contrast */
    --color-background-primary: #ffffff;
    --color-background-secondary: #ffffff;
    --color-background-disabled: #f0f0f0;
    --color-background-error: #ffffff;
    --color-background-warning: #ffffff;
    --color-background-success: #ffffff;
    --color-background-info: #ffffff;

    /* --- Text Colors --- */
    /* High contrast text colors */
    --color-text-primary: #000000;
    --color-text-secondary: #000000;
    --color-text-placeholder: #000000;
    --color-text-disabled: #000000;
    --color-text-on-brand: #ffffff;
    --color-text-interactive: #000000;
    --color-text-interactive-hover: #000000;
    --color-text-success: #000000;
    --color-text-error: #000000;
    --color-text-warning: #000000;
    --color-text-info: #000000;

    /* --- Surface Colors --- */
    /* Simplified surface colors */
    --color-surface-interactive-default: #000000;
    --color-surface-interactive-hover: #000000;
    --color-surface-interactive-active: #000000;
    --color-surface-interactive-primary: #000000;
    --color-surface-interactive-primary-hover: #000000;
    --color-surface-interactive-primary-active: #000000;
    --color-surface-interactive-secondary: #000000;
    --color-surface-interactive-secondary-hover: #000000;
    --color-surface-interactive-secondary-active: #000000;
    --color-surface-disabled: #f0f0f0;
    --color-surface-subtle: #ffffff;
    --color-surface-subtle-hover: #ffffff;
    --color-surface-subtle-active: #ffffff;

    /* --- Border Colors --- */
    /* Strong borders for structural separation */
    --color-border-default: #000000;
    --color-border-subtle: #000000;
    --color-border-interactive: #000000;
    --color-border-focus: #000000;
    --color-border-primary: #000000;
    --color-border-primary-hover: #000000;
    --color-border-primary-active: #000000;
    --color-border-secondary: #000000;
    --color-border-secondary-hover: #000000;
    --color-border-secondary-active: #000000;
    --color-border-error: #000000;
    --color-border-warning: #000000;
    --color-border-success: #000000;
    --color-border-info: #000000;
    --color-border-disabled: #000000;

    /* --- Focus Indicators --- */
    /* Prominent focus indicators for keyboard navigation */
    --accessibility-focus-ring-color: #000000;
    --accessibility-focus-ring-style: solid;
    --accessibility-focus-ring-width: 3px;
    --accessibility-focus-ring-offset: 2px;

    /* --- Glassmorphism Removal --- */
    /* Remove decorative effects for clarity */
    --glass-backdrop-blur: 0px;
    --glass-background-color: #ffffff;
    --glass-border-color: #000000;
    --glass-border-width: 1px;
    --glass-elevation: none;

    /* --- RGB Values --- */
    /* Simplified RGB values for effects */
    --rgb-brand-primary: 0, 0, 0;
    --rgb-brand-secondary: 0, 0, 0;
    --rgb-brand-tertiary: 0, 0, 0;
    --rgb-brand-quaternary: 0, 0, 0;
    --rgb-brand-quinary: 0, 0, 0;
    --rgb-brand-senary: 0, 0, 0;
    --rgb-violet: 0, 0, 0;
    --rgb-royal-blue: 0, 0, 0;
    --rgb-cyan: 0, 0, 0;
    --rgb-spearmint: 0, 0, 0;
    --rgb-rose: 0, 0, 0;
    --rgb-white: 255, 255, 255;
    --rgb-black: 0, 0, 0;
    --rgb-neutral-100: 255, 255, 255;

    /* --- Effect Colors --- */
    /* Simplified effect colors */
    --effect-color-primary: var(--rgb-black);
    --effect-color-secondary: var(--rgb-black);
    --effect-color-accent: var(--rgb-black);
    --effect-color-neutral: var(--rgb-black);
    --effect-color-surface: var(--rgb-white);

    /* --- Glass Surface Colors --- */
    /* Simplified glass surface colors */
    --glass-surface-color: var(--rgb-white);
    --glass-variant-primary: var(--rgb-black);
    --glass-variant-success: var(--rgb-black);
    --glass-variant-warning: var(--rgb-black);
    --glass-variant-danger: var(--rgb-black);
    --glass-variant-info: var(--rgb-black);
    --glass-variant-purple: var(--rgb-black);
    --glass-variant-emerald: var(--rgb-black);
    --glass-variant-blue: var(--rgb-black);
    --glass-variant-aqua: var(--rgb-black);

    /* --- Component-Specific High Contrast Overrides --- */
    /* Button focus states */
    --button-focus-border-width: 3px;
    --button-focus-border-opacity: 1;

    /* Input focus states */
    --input-focus-border-width: 3px;
    --input-focus-border-color: #000000;

    /* Link focus states */
    --link-focus-outline: 3px solid #000000;
    --link-focus-outline-offset: 2px;

    /* Card borders */
    --card-border-width: 2px;
    --card-border-color: #000000;

    /* Modal borders */
    --modal-border-width: 3px;
    --modal-border-color: #000000;

    /* Tooltip borders */
    --tooltip-border-width: 2px;
    --tooltip-border-color: #000000;

    /* Dropdown borders */
    --dropdown-border-width: 2px;
    --dropdown-border-color: #000000;

    /* Tab borders */
    --tab-border-width: 2px;
    --tab-border-color: #000000;

    /* Progress bar borders */
    --progress-border-width: 2px;
    --progress-border-color: #000000;

    /* Slider borders */
    --slider-border-width: 2px;
    --slider-border-color: #000000;

    /* Checkbox borders */
    --checkbox-border-width: 2px;
    --checkbox-border-color: #000000;

    /* Radio button borders */
    --radio-border-width: 2px;
    --radio-border-color: #000000;

    /* Toggle borders */
    --toggle-border-width: 2px;
    --toggle-border-color: #000000;
  }

  /* =======================
     HIGH CONTRAST: COMPONENT OVERRIDES
     Specific component adjustments for high contrast
     ======================= */

  /* Button Component */
  .button,
  .btn,
  [class*="button"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
    box-shadow: none !important;
    text-shadow: none !important;

    &:hover,
    &:focus {
      border-color: #000000 !important;
      background-color: #ffffff !important;
      color: #000000 !important;
      outline: 3px solid #000000 !important;
      outline-offset: 2px !important;
    }

    &:active {
      border-color: #000000 !important;
      background-color: #f0f0f0 !important;
      color: #000000 !important;
    }

    &.disabled,
    &:disabled {
      border-color: #000000 !important;
      background-color: #f0f0f0 !important;
      color: #000000 !important;
      opacity: 0.5 !important;
    }
  }

  /* Input Components */
  input,
  textarea,
  select,
  [class*="input"],
  [class*="textbox"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
    box-shadow: none !important;

    &:focus {
      border-color: #000000 !important;
      outline: 3px solid #000000 !important;
      outline-offset: 2px !important;
    }

    &:disabled {
      border-color: #000000 !important;
      background-color: #f0f0f0 !important;
      color: #000000 !important;
    }
  }

  /* Link Components */
  a,
  [class*="link"] {
    color: #000000 !important;
    text-decoration: underline !important;

    &:hover,
    &:focus {
      color: #000000 !important;
      outline: 3px solid #000000 !important;
      outline-offset: 2px !important;
    }
  }

  /* Card Components */
  [class*="card"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
    box-shadow: none !important;
  }

  /* Modal Components */
  [class*="modal"],
  [class*="dialog"] {
    border: 3px solid #000000 !important;
    background-color: #ffffff !important;
    box-shadow: none !important;
  }

  /* Tooltip Components */
  [class*="tooltip"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
    box-shadow: none !important;
  }

  /* Dropdown Components */
  [class*="dropdown"],
  [class*="select"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;
    box-shadow: none !important;
  }

  /* Tab Components */
  [class*="tab"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;
    color: #000000 !important;

    &.active {
      background-color: #000000 !important;
      color: #ffffff !important;
    }
  }

  /* Progress Components */
  [class*="progress"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;

    .progress-bar {
      background-color: #000000 !important;
      border: 1px solid #000000 !important;
    }
  }

  /* Slider Components */
  [class*="slider"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;

    .slider-thumb {
      border: 2px solid #000000 !important;
      background-color: #ffffff !important;
    }
  }

  /* Checkbox Components */
  [class*="checkbox"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;

    &:checked {
      background-color: #000000 !important;
      border-color: #000000 !important;
    }
  }

  /* Radio Components */
  [class*="radio"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;

    &:checked {
      background-color: #000000 !important;
      border-color: #000000 !important;
    }
  }

  /* Toggle Components */
  [class*="toggle"] {
    border: 2px solid #000000 !important;
    background-color: #ffffff !important;

    &.checked {
      background-color: #000000 !important;
      border-color: #000000 !important;
    }
  }

  /* =======================
     HIGH CONTRAST: UTILITY OVERRIDES
     Remove decorative effects and enhance structural elements
     ======================= */

  /* Remove all shadows and gradients */
  * {
    box-shadow: none !important;
    text-shadow: none !important;
    background-image: none !important;
    filter: none !important;
  }

  /* Remove glassmorphism effects */
  .glass,
  [class*="glass"] {
    backdrop-filter: none !important;
    background-color: #ffffff !important;
    border: 2px solid #000000 !important;
    box-shadow: none !important;
  }

  /* Remove animations and transitions */
  * {
    animation: none !important;
    transition: none !important;
  }

  /* Enhance focus indicators */
  *:focus-visible {
    outline: 3px solid #000000 !important;
    outline-offset: 2px !important;
  }

  /* Ensure sufficient contrast for all text */
  * {
    color: #000000 !important;
  }

  /* Ensure background contrast */
  body,
  html {
    background-color: #ffffff !important;
    color: #000000 !important;
  }
}

/* =======================
   LEGACY HIGH CONTRAST SUPPORT
   Support for older browsers and systems
   ======================= */

/* Internet Explorer/Edge Legacy Support */
@media (-ms-high-contrast: active) {
  :root {
    /* Apply the same high contrast overrides */
    --color-brand-primary: #000000;
    --color-background-primary: #ffffff;
    --color-text-primary: #000000;
    --color-border-default: #000000;
    --accessibility-focus-ring-color: #000000;
    --accessibility-focus-ring-width: 3px;
  }
}

/* Windows High Contrast Mode Support */
@media (-ms-high-contrast: black-on-white) {
  :root {
    /* Black on white high contrast */
    --color-brand-primary: #000000;
    --color-background-primary: #ffffff;
    --color-text-primary: #000000;
    --color-border-default: #000000;
  }
}

@media (-ms-high-contrast: white-on-black) {
  :root {
    /* White on black high contrast */
    --color-brand-primary: #ffffff;
    --color-background-primary: #000000;
    --color-text-primary: #ffffff;
    --color-border-default: #ffffff;
  }
}
