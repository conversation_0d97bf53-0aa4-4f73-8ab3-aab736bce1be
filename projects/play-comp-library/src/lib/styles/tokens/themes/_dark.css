/**
 * =========================================================================
 * Play+ Design System: Dark Theme
 *
 * Dark theme overrides for semantic tokens.
 * This theme uses lighter shades of the same colors from the light theme
 * to ensure better contrast and readability on dark backgrounds.
 * =========================================================================
 */

[data-theme="dark"] {
  /* --- Dark Theme Color Overrides --- */
  /* PRIMARY (Lighter Pink for dark backgrounds) */
  --color-brand-primary: var(--global-color-pink-300);
  --color-brand-primary-hover: var(--global-color-pink-200);
  --color-brand-primary-active: var(--global-color-deep-purple-300);
  --color-surface-interactive-primary: var(--global-color-pink-300);
  --color-surface-interactive-primary-hover: var(--global-color-pink-200);
  --color-surface-interactive-primary-active: var(
    --global-color-deep-purple-300
  );
  --color-border-primary: var(--global-color-pink-300);
  --color-border-primary-hover: var(--global-color-pink-200);
  --color-border-primary-active: var(--global-color-deep-purple-300);
  --color-text-primary: var(--global-color-gray-100);
  --color-text-on-primary: var(--global-color-white);
  --color-text-inactive-tab-button: var(--global-color-gray-300);
  --color-text-active-stepper-circle: var(--global-color-gray-100);
  --color-textbox-input: var(--global-color-gray-700);

  /* SECONDARY (Lighter Blue for dark backgrounds) */
  --color-brand-secondary: var(--global-color-blue-300);
  --color-brand-secondary-hover: var(--global-color-royal-blue-300);
  --color-brand-secondary-active: var(--global-color-royal-blue-400);
  --color-surface-interactive-secondary: var(--global-color-blue-800);
  --color-surface-interactive-secondary-hover: var(--global-color-blue-300);
  --color-surface-interactive-secondary-active: var(
    --global-color-royal-blue-300
  );
  --color-border-secondary: var(--global-color-blue-300);
  --color-border-secondary-hover: var(--global-color-royal-blue-300);
  --color-border-secondary-active: var(--global-color-royal-blue-400);
  --color-text-secondary: var(--global-color-royal-blue-300);
  --color-text-on-secondary: var(--global-color-gray-900);
  --color-background-secondary: var(--global-color-blue-800);

  /* BUTTONS, TABS, TAGS: Use these tokens for all secondary/primary states */
  --color-text-placeholder: var(--global-color-gray-500);
  --color-text-disabled: var(--global-color-gray-600);
  --color-text-on-brand: var(--global-color-gray-900);
  --color-text-interactive: var(--global-color-pink-300);
  --color-text-interactive-hover: var(--global-color-pink-200);
  --color-text-success: var(--global-color-spearmint-300);
  --color-text-error: var(--global-color-rose-300);
  --color-background-primary: var(--global-color-gray-900);
  --color-background-disabled: var(--global-color-gray-800);
  --color-surface-interactive-default: var(--global-color-pink-300);
  --color-surface-interactive-hover: var(--global-color-pink-200);
  --color-surface-interactive-active: var(--global-color-pink-200);
  --color-surface-disabled: var(--global-color-gray-700);
  --color-surface-subtle-hover: var(--global-color-gray-800);
  --color-border-default: var(--global-color-gray-600);
  --color-border-subtle: var(--global-color-gray-700);
  --color-border-interactive: var(--global-color-pink-300);
  --color-border-focus: var(--global-color-pink-300);
  --color-border-error: var(--global-color-rose-300);
  --color-background-error: var(--global-color-rose-300);

  /* Semantic Border Colors */
  --color-border-warning: var(--global-color-amber-300);
  --color-border-success: var(--global-color-spearmint-300);
  --color-border-info: var(--global-color-blue-300);

  /* Semantic Text Colors */
  --color-text-warning: var(--global-color-amber-300);
  --color-text-success: var(--global-color-spearmint-300);
  --color-text-info: var(--global-color-blue-300);

  /* Semantic Background Colors */
  --color-background-warning: var(--global-color-amber-300);
  --color-background-success: var(--global-color-spearmint-300);
  --color-background-info: var(--global-color-blue-300);

  /* --- Dark Theme Glassmorphism --- */
  --glass-backdrop-blur: 16px;
  --glass-background-color: rgba(0, 0, 0, 0.4);
  --glass-border-color: rgba(255, 255, 255, 0.1);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-03);

  /* =======================
      DARK THEME: RGB OVERRIDES
      Extract RGB values from dark theme semantic colors (lighter shades)
      ======================= */
  --rgb-brand-primary: 240, 98, 146;
  /* From pink-300 #f06292 */
  --rgb-brand-secondary: 149, 117, 205;
  /* From deep-purple-300 #9575cd */
  --rgb-brand-tertiary: 100, 181, 246;
  /* From blue-300 #64b5f6 */
  --rgb-brand-quaternary: 103, 232, 249;
  /* From cyan-300 #67e8f9 */
  --rgb-brand-quinary: 94, 234, 212;
  /* From spearmint-300 #5eead4 */
  --rgb-brand-senary: 253, 164, 175;
  /* From rose-300 #fda4af */
  --rgb-violet: 169, 149, 255;
  /* From violet-300 #a995ff */
  --rgb-royal-blue: 147, 197, 253;
  /* From royal-blue-300 #93c5fd */
  --rgb-cyan: 103, 232, 249;
  /* From cyan-300 #67e8f9 */
  --rgb-spearmint: 94, 234, 212;
  /* From spearmint-300 #5eead4 */
  --rgb-rose: 253, 164, 175;
  /* From rose-300 #fda4af */
  --rgb-white: 255, 255, 255;
  /* From #ffffff */
  --rgb-black: 0, 0, 0;
  /* From #000000 */
  --rgb-neutral-100: 17, 24, 39;
  /* Dark background equivalent */

  /* =======================
      DARK THEME: EFFECT COLOR OVERRIDES
      Override all effect colors for proper dark theme adaptation
      ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  /* Pink 300 - lighter for dark bg */
  --effect-color-secondary: var(--rgb-brand-secondary);
  /* Deep purple 300 - lighter for dark bg */
  --effect-color-accent: var(--rgb-violet);
  /* Violet 300 - lighter accent */
  --effect-color-neutral: var(--rgb-white);
  /* White shadows/effects on dark bg */
  --effect-color-surface: var(--rgb-black);
  /* Black glass/shimmer on dark bg */

  /* =======================
      DARK THEME: SEMANTIC COMPONENT TOKENS
      Theme-aware component-specific tokens using the metaphor system
      ======================= */

  /* Glass Metaphor (Dark Theme-Aware) */
  --surface-glass-bg: rgba(var(--effect-color-neutral), 0.08);
  --surface-glass-border: rgba(var(--effect-color-neutral), 0.12);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.25);

  /* Light Metaphor (Dark Theme-Aware) */
  --color-light-glow: rgba(var(--effect-color-primary), 0.35);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.55);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.15);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor (Dark Theme-Aware) */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* =======================
      DARK THEME: SOPHISTICATED GLASS CHAINING
      Override glass surface colors for dark theme variants
      ======================= */

  /* Dark theme glass surface - use dark background */
  --glass-surface-color: var(--rgb-black);

  /* Dark theme variant glass colors (lighter shades) */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: 94, 234, 212;
  /* spearmint-300 */
  --glass-variant-warning: 252, 211, 77;
  /* amber-300 */
  --glass-variant-danger: 253, 164, 175;
  /* rose-300 */
  --glass-variant-info: 100, 181, 246;
  /* blue-300 */

  /* Custom variant example - Lighter versions for dark theme */
  --glass-variant-purple: 149, 117, 205;
  /* deep-purple-300 */
  --glass-variant-emerald: 94, 234, 212;
  /* spearmint-300 */

  /* Dark theme effect color adjustments */
  --effect-color-neutral: var(--rgb-white);
  /* White highlights work on dark bg */
  --effect-color-surface: var(--rgb-black);
  /* Black depths on dark bg */

  /* =======================
      DARK THEME: ADDITIONAL OVERRIDES
      Additional tokens that need adjustment for dark theme
      ======================= */

  /* Text contrast adjustments */
  --color-text-muted: var(--global-color-gray-400);
  --color-text-subtle: var(--global-color-gray-500);
  --color-text-inverse: var(--global-color-gray-900);

  /* Surface variations for dark theme */
  --color-surface-elevated: var(--global-color-gray-800);
  --color-surface-sunken: var(--global-color-gray-900);
  --color-surface-overlay: rgba(0, 0, 0, 0.8);

  /* Input and form controls */
  --color-input-background: var(--global-color-gray-800);
  --color-input-border: var(--global-color-gray-600);
  --color-input-border-focus: var(--global-color-pink-300);
  --color-input-text: var(--global-color-gray-100);

  /* Navigation and menu items */
  --color-nav-item-hover: rgba(var(--rgb-brand-primary), 0.1);
  --color-nav-item-active: rgba(var(--rgb-brand-primary), 0.2);

  /* Dividers and separators */
  --color-divider: var(--global-color-gray-700);
  --color-divider-subtle: var(--global-color-gray-800);
}
