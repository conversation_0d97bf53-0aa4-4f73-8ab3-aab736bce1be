export const APP_CONSTANTS = {
  UPLOAD_TITLE: 'Upload File Here',
  CLOSE_UPLOAD_DIALOG: 'Close upload dialog',
  SUPPORTED_FORMATS_LABEL: 'Supported file formats',
  SUPPORTED_FORMATS_LIST: 'JPEG, PNG, SVG, DOC, DOCX, XLSX, TXT, PDF',
  CLICK_TO_UPLOAD: 'Click to upload a file',
  FILE_UPLOADED_SUCCESS: 'File successfully uploaded',
  CLICK_TO_REUPLOAD: 'Click here to re-upload',
  INVALID_FILE_TYPE: 'Invalid file type. Allowed formats: JPEG, PNG, SVG, DOC, DOCX, XLSX, TXT, PDF.',
  FILE_SIZE_ERROR: 'File is too large. Maximum size allowed is 3MB.',
  UPLOAD_BUTTON: 'Upload',
  FILE_UPLOAD_PROMPT: 'Click here to upload a file',
  MAX_FILE_SIZE : 3 * 1024 * 1024,
  ALLOWED_EXTENSIONS: ['jpeg', 'jpg', 'png', 'svg', 'doc', 'docx', 'xlsx', 'txt', 'pdf'], 
  ACCEPTED_FILE_TYPES: '.jpeg,.jpg,.png,.svg,.doc,.docx,.xlsx,.txt,.pdf', 
  MAX_FILES_ERROR: 'Maximum of 5 files allowed',


  //authentication flow components constants


  PHONE_REQUIRED_ERROR: 'Phone number is required.',
  PHONE_FORMAT_ERROR: 'Phone number must be exactly 13 digits starting with +91.',
  AUTHENTICATION_TITLE: 'Authentication',
  SENT_TITLE: 'Sent!',
  VERIFICATION_SUCCESS_TITLE: 'Verification Successful',
  NEED_TO_VERIFY: 'Need to verify user to authenticate',
  CANCEL_BUTTON: 'Cancel',
  PROCEED_TO_VERIFY_BUTTON: 'Proceed to Verify',
  USERNAME_PLACEHOLDER: 'Enter username',
  USERNAME_REQUIRED_ERROR: 'Username is required and must be at least 3 characters.',
  ENTER_EMAIL: 'Enter email',
  SEND_CODE_EMAIL: 'Send code via email',
  EMAIL_REQUIRED_ERROR: 'Please enter a valid email address.',
  ENTER_PHONE: 'Enter phone number',
  SEND_CODE_SMS: 'Send code via SMS',
  VERIFICATION_SENT_EMAIL: 'A verification mail has been successfully sent to the contact email.',
  VERIFICATION_SENT_SMS: 'A verification code has been successfully sent to the contact phone number.',
  CLOSE_BUTTON: 'Close',
  VERIFICATION_CODE_PLACEHOLDER: 'Enter verification code',
  VERIFICATION_CODE_ERROR: 'Verification code must be 6 digits.',
  PASSWORD_PLACEHOLDER: 'Enter the password',
  PASSWORD_ERROR: 'Password must be at least 6 characters long.',
  VERIFICATION_SUCCESS: 'Verification done Successfully',


  
};