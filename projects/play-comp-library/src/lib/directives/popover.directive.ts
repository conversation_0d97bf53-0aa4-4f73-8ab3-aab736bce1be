import {
  Directive,
  Input,
  HostListener,
  ViewContainerRef,
  ComponentRef,
  ElementRef,
  OnDestroy
} from '@angular/core';
import { PopOverComponent, PopOverData, PopOverConfig } from '../components/pop-over/pop-over.component';

@Directive({
  selector: '[avaPopover]',
  standalone: true
})
export class PopoverDirective implements OnDestroy {

  @Input() avaPopoverData: PopOverData[] = [];
  @Input() avaPopoverArrow: 'start' | 'center' | 'end' | null = null;
  @Input() avaPopoverPosition: 'top' | 'bottom' | 'left' | 'right' = 'top';
  @Input() avaPopoverTrigger: 'click' = 'click';
  @Input() avaPopoverShowButtons: boolean = false;
  @Input() avaPopoverShowPagination: boolean = false;
  @Input() avaPopoverShowIcon: boolean = false;
  @Input() avaPopoverShowSkip: boolean = false;
  @Input() avaPopoverShowLearnMore: boolean = false;

  private popoverRef: ComponentRef<PopOverComponent> | null = null;

  constructor(
    private vcRef: ViewContainerRef,
    private el: ElementRef
  ) { }

  @HostListener('click') onClick() {
    this.popoverRef ? this.hide() : this.show();
  }

  @HostListener('document:click', ['$event']) onDocumentClick(event: Event) {
    if (this.popoverRef && this.avaPopoverTrigger === 'click') {
      const target = event.target as HTMLElement;
      const popoverElement = this.popoverRef.location.nativeElement;
      const triggerElement = this.el.nativeElement;
      
      // Close if clicked outside both trigger and popover
      if (!triggerElement.contains(target) && !popoverElement.contains(target)) {
        this.hide();
      }
    }
  }

  show() {
    if (this.popoverRef || !this.avaPopoverData || this.avaPopoverData.length === 0) return;

    const rect = this.el.nativeElement.getBoundingClientRect();
    const scrollY = window.scrollY;
    const scrollX = window.scrollX;
    this.popoverRef = this.vcRef.createComponent(PopOverComponent);

    // Calculate absolute positions (viewport position + scroll offset)
    const absoluteLeft = rect.left + scrollX;
    const absoluteTop = rect.top + scrollY;
    const absoluteBottom = rect.bottom + scrollY;

    // Create popover configuration
    this.popoverRef.instance.config = {
      arrow: this.avaPopoverArrow,
      position: this.avaPopoverPosition,
      left: absoluteLeft,
      top: absoluteTop,
      bottom: absoluteBottom,
      width: rect.width,
      height: rect.height,
      showButtons: this.avaPopoverShowButtons,
      showPagination: this.avaPopoverShowPagination,
      showIcon: this.avaPopoverShowIcon,
      showSkip: this.avaPopoverShowSkip,
      showLearnMore: this.avaPopoverShowLearnMore,
    };

    // Set the data
    this.popoverRef.instance.data = this.avaPopoverData;

    // Append to body for proper positioning
    document.body.appendChild(this.popoverRef.location.nativeElement);

    // Trigger change detection
    this.popoverRef.changeDetectorRef.detectChanges();
  }

  hide() {
    if (this.popoverRef) {
      this.popoverRef.destroy();
      this.popoverRef = null;
    }
  }

  ngOnDestroy() {
    this.hide();
  }
}
