import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  ComponentRef,
  ViewContainerRef,
  ComponentFactoryResolver,
  Injector,
  OnDestroy,
  OnInit
} from '@angular/core';
import { TooltipComponent } from '../components/tooltip/tooltip.component';

@Directive({
  selector: '[avaTooltipTitle], [avaTooltipDescription]'
})
export class TooltipDirective implements OnInit {

  @Input() avaTooltipTitle: string = '';
  @Input() avaTooltipDescription: string = '';
  @Input() avaTooltipType: 'simple' | 'card' | 'guided' = 'simple';
  @Input() avaTooltipArrow: 'start' | 'center' | 'end' | null = null;
  @Input() avaTooltipTrigger: 'hover' | 'click' | 'focus' = 'hover';
  @Input() avaTooltipPosition: 'top' | 'bottom' | 'left' | 'right' = 'top';
  @Input() avaTooltipSize: 'small' | 'medium' | 'large' = 'medium';
  @Input() avaTooltipVariant: 'default' = 'default';
  @Input() avaTooltipIcon: string = '';
  @Input() avaTooltipIconColor: string = '';

  private tooltipRef: ComponentRef<TooltipComponent> | null = null;

  tooltipElement!: HTMLElement;

  constructor(
    private vcRef: ViewContainerRef,
    private resolver: ComponentFactoryResolver,
    private injector: Injector,
    private el: ElementRef
  ) { }

  ngOnInit() {
    // Directive initialized
  }

  @HostListener('mouseenter') onMouseEnter() {
    if (this.avaTooltipTrigger === 'hover') this.show();
  }

  @HostListener('mouseleave') onMouseLeave() {
    if (this.avaTooltipTrigger === 'hover') this.hide();
  }

  @HostListener('focus') onFocus() {
    if (this.avaTooltipTrigger === 'focus') this.show();
  }

  @HostListener('blur') onBlur() {
    if (this.avaTooltipTrigger === 'focus') this.hide();
  }

  @HostListener('click') onClick() {
    if (this.avaTooltipTrigger === 'click') {
      this.tooltipRef ? this.hide() : this.show();
    }
  }

  show() {
    if (this.tooltipRef) return;
    const rect = this.el.nativeElement.getBoundingClientRect();
    const scrollY = window.scrollY;
    const scrollX = window.scrollX;
    const factory = this.resolver.resolveComponentFactory(TooltipComponent);
    this.tooltipRef = this.vcRef.createComponent(factory);

    // Calculate absolute positions (viewport position + scroll offset)
    const absoluteLeft = rect.left + scrollX;
    const absoluteTop = rect.top + scrollY;
    const absoluteBottom = rect.bottom + scrollY;

    // Create tooltip configuration
    this.tooltipRef.instance.config = {
      title: this.avaTooltipTitle,
      description: this.avaTooltipDescription,
      type: this.avaTooltipType,
      arrow: this.avaTooltipArrow,
      size: this.avaTooltipSize,
      position: this.avaTooltipPosition,
      variant: this.avaTooltipVariant,
      icon: this.avaTooltipIcon,
      iconColor: this.avaTooltipIconColor,
      left: absoluteLeft,
      top: absoluteTop,
      bottom: absoluteBottom,
      width: rect.width,
      height: rect.height,
    };
  }

  hide() {
    if (this.tooltipRef) {
      this.tooltipRef.destroy();
      this.tooltipRef = null;
    }
  }

  ngOnDestroy() {
    this.hide();
  }

}

 