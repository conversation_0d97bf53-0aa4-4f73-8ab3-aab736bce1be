<div class="slider-container" [class.dragging]="isDragging" [class]="'slider-' + size" [class.slider-input-type]="type === 'input'">
  <div class="slider-wrapper">
    <div class="slider" #sliderTrack (mousedown)="onTrackClick($event)">
    <!-- Icon at start -->
    <ava-icon
      *ngIf="iconStart"
      [iconName]="iconStart"
      class="slider-icon slider-icon--start"
    ></ava-icon>

    <!-- Fill bar -->
    <div
      class="slider-fill"
      [style.left.%]="multiRange ? minPercentage : 0"
      [style.width.%]="multiRange ? maxPercentage - minPercentage : percentage"
    ></div>

    <!-- Single or Multi Handles -->
    <ng-container *ngIf="!multiRange">
      <div
        class="slider-handle"
        [style.left.%]="percentage"
        [class.hover]="isHovered"
        [class.dragging]="isDragging"
        tabindex="0"
        role="slider"
        [attr.aria-valuemin]="min"
        [attr.aria-valuemax]="max"
        [attr.aria-valuenow]="value"
        (mousedown)="startDrag($event)"
        (mouseenter)="isHovered = true"
        (mouseleave)="isHovered = false"
        (keydown)="onKeyDown($event)"
      >
        <div class="handle-ring"></div>
        <ng-container *ngIf="!handleIcon">
          <div class="handle-core"></div>
        </ng-container>
        <ava-icon
          *ngIf="handleIcon"
          [iconName]="handleIcon"
          class="slider-handle-icon"
        ></ava-icon>
        <div class="slider-tooltip" *ngIf="showTooltip && type === 'default'">{{ value }}%</div>
      </div>
    </ng-container>
    <ng-container *ngIf="multiRange">
      <!-- Min handle -->
      <div
        class="slider-handle"
        [style.left.%]="minPercentage"
        [class.hover]="isHovered && draggingHandle === 'min'"
        [class.dragging]="isDragging && draggingHandle === 'min'"
        tabindex="0"
        role="slider"
        [attr.aria-valuemin]="min"
        [attr.aria-valuemax]="maxValue - step"
        [attr.aria-valuenow]="minValue"
        (mousedown)="startDrag($event, 'min')"
        (mouseenter)="isHovered = true"
        (mouseleave)="isHovered = false"
      >
        <div class="handle-ring"></div>
        <ng-container *ngIf="!handleIconStart">
          <div class="handle-core"></div>
        </ng-container>
        <ava-icon
          *ngIf="handleIconStart"
          [iconName]="handleIconStart"
          class="slider-handle-icon"
        ></ava-icon>
        <div class="slider-tooltip" *ngIf="showTooltip && type === 'default'">{{ minValue }}%</div>
      </div>
      <!-- Max handle -->
      <div
        class="slider-handle"
        [style.left.%]="maxPercentage"
        [class.hover]="isHovered && draggingHandle === 'max'"
        [class.dragging]="isDragging && draggingHandle === 'max'"
        tabindex="0"
        role="slider"
        [attr.aria-valuemin]="minValue + step"
        [attr.aria-valuemax]="max"
        [attr.aria-valuenow]="maxValue"
        (mousedown)="startDrag($event, 'max')"
        (mouseenter)="isHovered = true"
        (mouseleave)="isHovered = false"
      >
        <div class="handle-ring"></div>
        <ng-container *ngIf="!handleIconEnd">
          <div class="handle-core"></div>
        </ng-container>
        <ava-icon
          *ngIf="handleIconEnd"
          [iconName]="handleIconEnd"
          class="slider-handle-icon"
        ></ava-icon>
        <div class="slider-tooltip" *ngIf="showTooltip && type === 'default'">{{ maxValue }}%</div>
      </div>
    </ng-container>

      <!-- Icon at end -->
      <ava-icon
        *ngIf="iconEnd"
        [iconName]="iconEnd"
        class="slider-icon slider-icon--end"
      ></ava-icon>
    </div>
  </div>

  <!-- Input box for input type -->
  <div class="slider-input-container" *ngIf="type === 'input'">
    <input
      type="number"
      class="slider-input"
      [value]="value"
      (input)="onInputChange($event)"
      (blur)="onTouched()"
      [min]="min"
      [max]="max"
      [step]="step"
    />
  </div>
</div>
