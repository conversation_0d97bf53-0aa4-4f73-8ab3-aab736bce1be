import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
  ViewChild,
  ViewEncapsulation,
  WritableSignal,
  forwardRef,
  signal,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';

export type SliderSize = 'small' | 'medium';
export type SliderType = 'default' | 'input';

@Component({
  selector: 'ava-slider',
  imports: [CommonModule, IconComponent],
  templateUrl: './slider.component.html',
  styleUrl: './slider.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SliderComponent),
      multi: true,
    },
  ],
})
export class SliderComponent implements ControlValueAccessor {
  @Input() min = 0;
  @Input() max = 100;
  @Input() value = 0;
  @Input() step = 1;
  @Input() showTooltip = true;
  @Input() size: SliderSize = 'medium';
  @Input() type: SliderType = 'default';

  // Multi-range support
  @Input() multiRange = false;
  @Input() minValue: number = 20;
  @Input() maxValue: number = 80;
  @Output() minValueChange = new EventEmitter<number>();
  @Output() maxValueChange = new EventEmitter<number>();

  // Icon support
  @Input() iconStart: string = '';
  @Input() iconEnd: string = '';
  @Input() handleIcon: string = '';
  @Input() handleIconStart: string = '';
  @Input() handleIconEnd: string = '';

  @Output() valueChange = new EventEmitter<number>();
  @ViewChild('sliderTrack') sliderTrack!: ElementRef;

  isHovered = false;
  isDragging = false;
  draggingHandle: 'min' | 'max' | null = null;

  private onChange: (value: number) => void = () => {};
  public onTouched: () => void = () => {};

  decimalStepValue: WritableSignal<number> = signal(1);

  constructor(private elementRef: ElementRef, private cdr: ChangeDetectorRef) {}

  /**
   * Rounds a value to the nearest step with proper decimal precision
   * Fixes floating-point arithmetic issues
   */
  private roundToStep(value: number): number {
    // Calculate the number of decimal places in the step
    const stepStr = this.step.toString();
    const decimalPlaces = stepStr.includes('.') ? stepStr.split('.')[1].length : 0;

    // Use precise rounding to avoid floating-point errors
    const factor = Math.pow(10, decimalPlaces);
    const roundedValue = Math.round((value / this.step) * factor) / factor * this.step;

    // Round to the correct number of decimal places
    return parseFloat(roundedValue.toFixed(decimalPlaces));
  }

  get percentage(): number {
    return ((this.value - this.min) / (this.max - this.min)) * 100;
  }

  get minPercentage(): number {
    return ((this.minValue - this.min) / (this.max - this.min)) * 100;
  }
  get maxPercentage(): number {
    return ((this.maxValue - this.min) / (this.max - this.min)) * 100;
  }

  // ControlValueAccessor methods
  writeValue(value: number): void {
    this.value = value || 0;
    // Trigger change detection to update the UI
    this.cdr.markForCheck();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onTrackClick(event: MouseEvent): void {
    if (this.multiRange) {
      this.updateMultiValueFromEvent(event);
    } else {
      this.updateValueFromEvent(event);
    }
  }

  startDrag(event: MouseEvent, handle: 'min' | 'max' | null = null): void {
    event.preventDefault();
    this.isDragging = true;
    this.isHovered = true;
    this.draggingHandle = handle;
  }

  onKeyDown(event: KeyboardEvent): void {
    if (this.multiRange) return; // Not implemented for multi-range yet
    let newValue = this.value;
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowUp':
        newValue = this.value + this.step;
        break;
      case 'ArrowLeft':
      case 'ArrowDown':
        newValue = this.value - this.step;
        break;
      case 'Home':
        newValue = this.min;
        break;
      case 'End':
        newValue = this.max;
        break;
      default:
        return;
    }
    event.preventDefault();
    this.updateValue(newValue);
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (this.isDragging) {
      if (this.multiRange) {
        this.updateMultiValueFromEvent(event, this.draggingHandle);
      } else {
        this.updateValueFromEvent(event);
      }
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    if (this.isDragging) {
      this.isDragging = false;
      this.isHovered = false;
      this.draggingHandle = null;
      this.onTouched();
    }
  }

  onDecimalStepChange(value: number): void {
    const roundedValue = this.roundToStep(value);
    this.decimalStepValue.set(roundedValue);
  }

  onInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const inputValue = parseFloat(target.value);

    if (!isNaN(inputValue)) {
      // Clamp the value to min/max bounds
      const clampedValue = Math.max(this.min, Math.min(this.max, inputValue));
      this.updateValue(clampedValue);
    }
  }

  private updateValueFromEvent(event: MouseEvent): void {
    const rect = this.sliderTrack.nativeElement.getBoundingClientRect();
    const percentage = (event.clientX - rect.left) / rect.width;
    const newValue = this.min + percentage * (this.max - this.min);
    this.updateValue(newValue);
  }

  private updateMultiValueFromEvent(
    event: MouseEvent,
    handle: 'min' | 'max' | null = null
  ): void {
    const rect = this.sliderTrack.nativeElement.getBoundingClientRect();
    const percentage = (event.clientX - rect.left) / rect.width;
    const newValue = this.min + percentage * (this.max - this.min);
    if (
      handle === 'min' ||
      (!handle &&
        Math.abs(newValue - this.minValue) < Math.abs(newValue - this.maxValue))
    ) {
      // Move min handle
      const roundedValue = this.roundToStep(newValue);
      const clamped = Math.min(
        Math.max(this.min, roundedValue),
        this.maxValue - this.step
      );
      if (clamped !== this.minValue) {
        this.minValue = clamped;
        this.minValueChange.emit(this.minValue);
        // Use detectChanges during dragging for immediate UI updates
        if (this.isDragging) {
          this.cdr.detectChanges();
        } else {
          this.cdr.markForCheck();
        }
      }
    } else {
      // Move max handle
      const roundedValue = this.roundToStep(newValue);
      const clamped = Math.max(
        Math.min(this.max, roundedValue),
        this.minValue + this.step
      );
      if (clamped !== this.maxValue) {
        this.maxValue = clamped;
        this.maxValueChange.emit(this.maxValue);
        // Use detectChanges during dragging for immediate UI updates
        if (this.isDragging) {
          this.cdr.detectChanges();
        } else {
          this.cdr.markForCheck();
        }
      }
    }
  }

  private updateValue(value: number): void {
    const roundedValue = this.roundToStep(value);
    const clampedValue = Math.max(this.min, Math.min(this.max, roundedValue));
    if (clampedValue !== this.value) {
      this.value = clampedValue;
      this.onChange(this.value);
      this.valueChange.emit(this.value);
      // Use detectChanges during dragging for immediate UI updates, markForCheck otherwise
      if (this.isDragging) {
        this.cdr.detectChanges();
      } else {
        this.cdr.markForCheck();
      }
    }
  }
}
