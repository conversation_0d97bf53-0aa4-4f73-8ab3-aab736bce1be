import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

@Component({
  selector: 'ava-toast-success',
  standalone: true,
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './success.component.html',
  styleUrls: ['./success.component.scss'],
})
export class SuccessToastComponent implements OnInit, OnDestroy {
  @Input() title?: string;
  @Input() message?: string;
  @Input() duration = 4000;
  @Input() showCloseButton = true;
  @Input() showProgress = true;
  @Input() icon?: string;
  @Input() iconColor?: string;
  @Input() customWidth?: string;
  @Input() customHeight?: string;
  @Input() design?: 'classic' | 'modern' = 'classic';
  @Output() closeToast = new EventEmitter<void>();
  @Input() size: 'large' | 'medium' | 'small' = 'large';
  private timeoutId?: number;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    // Add show animation
    setTimeout(() => {
      this.elementRef.nativeElement
        .querySelector('.ava-toast')
        ?.classList.add('ava-toast-show');
    }, 10);
  }

  ngOnDestroy() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  onClose() {
    this.closeToast.emit();
  }

  getIconSize(): number {
    switch (this.size) {
      case 'large':
        return 24;
      case 'medium':
        return 20;
      case 'small':
        return 16;
      default:
        return 18;
    }
  }
}
