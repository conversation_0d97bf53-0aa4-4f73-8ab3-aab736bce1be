import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ButtonComponent } from './button.component';
import { TestUtils, TestExpectations } from '../../testing/test-utils';

describe('ButtonComponent', () => {
  let component: ButtonComponent;
  let fixture: ComponentFixture<ButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ButtonComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ===== BASIC FUNCTIONALITY TESTS =====
  describe('Basic Functionality', () => {
    it('should render button element', () => {
      TestExpectations.elementToBePresent(fixture, '.ava-button');
    });

    it('should have default label content', () => {
      component.label = 'Click me';
      fixture.detectChanges();
      TestExpectations.elementToHaveText(fixture, '.ava-button', 'Click me');
    });

    it('should be enabled by default', () => {
      TestExpectations.elementToBeEnabled(fixture, '.ava-button');
    });
  });

  // ===== VARIANT TESTS =====
  describe('Button Variants', () => {
    it('should apply primary variant class', () => {
      component.variant = 'primary';
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(fixture, '.ava-button', 'primary');
    });

    it('should apply secondary variant class', () => {
      component.variant = 'secondary';
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(fixture, '.ava-button', 'secondary');
    });

    it('should apply success variant class', () => {
      component.variant = 'success';
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(fixture, '.ava-button', 'success');
    });
  });

  // ===== SIZE TESTS =====
  describe('Button Sizes', () => {
    it('should apply small size class', () => {
      component.size = 'small';
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(fixture, '.ava-button', 'small');
    });

    it('should apply medium size class', () => {
      component.size = 'medium';
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(fixture, '.ava-button', 'medium');
    });

    it('should apply large size class', () => {
      component.size = 'large';
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(fixture, '.ava-button', 'large');
    });
  });

  // ===== STATE TESTS =====
  describe('Button States', () => {
    it('should apply disabled state', () => {
      component.disabled = true;
      fixture.detectChanges();
      TestExpectations.elementToBeDisabled(fixture, '.ava-button');
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-button',
        'ava-button--disabled'
      );
    });

    it('should apply processing state', () => {
      component.processing = true;
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-button',
        'ava-button--processing'
      );
    });
  });

  // ===== INTERACTION TESTS =====
  describe('Button Interactions', () => {
    it('should emit click event when clicked', () => {
      spyOn(component.userClick, 'emit');
      TestUtils.clickElement(fixture, '.ava-button');
      expect(component.userClick.emit).toHaveBeenCalled();
    });

    it('should not emit click event when disabled', () => {
      component.disabled = true;
      fixture.detectChanges();
      spyOn(component.userClick, 'emit');
      TestUtils.clickElement(fixture, '.ava-button');
      expect(component.userClick.emit).not.toHaveBeenCalled();
    });

    it('should handle Enter key press', () => {
      spyOn(component.userClick, 'emit');
      TestUtils.triggerKeyEvent(fixture, '.ava-button', 'keydown', 'Enter');
      expect(component.userClick.emit).toHaveBeenCalled();
    });

    it('should handle Space key press', () => {
      spyOn(component.userClick, 'emit');
      TestUtils.triggerKeyEvent(fixture, '.ava-button', 'keydown', ' ');
      expect(component.userClick.emit).toHaveBeenCalled();
    });
  });

  // ===== ACCESSIBILITY TESTS =====
  describe('Accessibility', () => {
    it('should have proper ARIA attributes when disabled', () => {
      component.disabled = true;
      fixture.detectChanges();
      const button = TestUtils.getElement(fixture, '.ava-button').nativeElement;
      expect(button.getAttribute('aria-disabled')).toBe('true');
    });

    it('should have proper role attribute', () => {
      const button = TestUtils.getElement(fixture, '.ava-button').nativeElement;
      expect(button.getAttribute('role')).toBe('button');
    });

    it('should be focusable when enabled', () => {
      const button = TestUtils.getElement(fixture, '.ava-button').nativeElement;
      expect(button.tabIndex).toBe(0);
    });

    it('should not be focusable when disabled', () => {
      component.disabled = true;
      fixture.detectChanges();
      const button = TestUtils.getElement(fixture, '.ava-button').nativeElement;
      expect(button.tabIndex).toBe(-1);
    });
  });

  // ===== EDGE CASES =====
  describe('Edge Cases', () => {
    it('should handle empty label', () => {
      component.label = '';
      fixture.detectChanges();
      const button = TestUtils.getElement(fixture, '.ava-button').nativeElement;
      expect(button.textContent.trim()).toBe('');
    });

    it('should handle very long label', () => {
      component.label =
        'This is a very long button label that should be handled properly';
      fixture.detectChanges();
      TestExpectations.elementToHaveText(
        fixture,
        '.ava-button',
        'This is a very long button label that should be handled properly'
      );
    });

    it('should handle special characters in label', () => {
      component.label = 'Button & Text <script>alert("test")</script>';
      fixture.detectChanges();
      TestExpectations.elementToHaveText(
        fixture,
        '.ava-button',
        'Button & Text <script>alert("test")</script>'
      );
    });
  });
});
