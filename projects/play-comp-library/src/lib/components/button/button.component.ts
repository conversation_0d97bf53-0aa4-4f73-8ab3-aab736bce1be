import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  Output,
  EventEmitter,
  ElementRef,
} from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';
import { IconComponent } from '../icon/icon.component';

// CLEAN TYPE SEPARATION: Interaction States vs Visual Variants vs Glass Intensity

// Visual/Semantic Variants (what the button represents)
export type ButtonVariant =
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'info'
  | 'purple'
  | 'emerald';

// Interaction States (how user is interacting with button)
export type ButtonState =
  | 'default'
  | 'hover'
  | 'active'
  | 'disabled'
  | 'processing'
  | 'focus';

// Button Sizes
export type ButtonSize = 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge';

// Glass Intensity (surface opacity level)
export type ButtonGlassVariant =
  | 'glass-10'
  | 'glass-50'
  | 'glass-75'
  | 'glass-100';

// Effects System Types
export type ButtonHoverEffect = 'torch' | 'glow' | 'tint' | 'scale' | 'none';
export type ButtonPressedEffect = 'ripple' | 'inset' | 'solid' | 'none';
export type ButtonProcessingEffect = 'pulse' | 'none';
export type ButtonFocusEffect = 'border' | 'none';
export type ButtonDisabledEffect = 'dim' | 'none';

@Component({
  selector: 'ava-button',
  standalone: true,
  imports: [CommonModule, LucideAngularModule, IconComponent],
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ButtonComponent {
  // === CORE BUTTON PROPS ===
  @Input() label = '';
  @Input() variant: ButtonVariant = 'default'; // Default fallback variant
  @Input() size: ButtonSize = 'medium';
  @Input() state: ButtonState = 'default';
  // === GLASS SYSTEM PROPS ===
  @Input() glassVariant: ButtonGlassVariant = 'glass-10'; // Default recommended glass intensity

  // === EFFECTS SYSTEM PROPS ===
  @Input() hoverEffect: ButtonHoverEffect = 'torch'; // Default recommended hover effect
  @Input() pressedEffect: ButtonPressedEffect = 'ripple'; // Default recommended pressed effect
  @Input() processingEffect: ButtonProcessingEffect = 'pulse'; // Default processing effect
  @Input() focusEffect: ButtonFocusEffect = 'border'; // Default focus effect
  @Input() disabledEffect: ButtonDisabledEffect = 'dim'; // Default disabled effect

  // === STATE PROPS ===
  @Input() disabled = false;
  @Input() processing = false; // Processing state for loading/async operations

  // === STYLE OVERRIDE PROPS ===
  @Input() customStyles: Record<string, string> = {}; // CSS custom properties override
  @Input() pill = false;
  @Input() width?: string;
  @Input() height?: string;
  @Input() outlined = false; // New outlined prop
  @Input() clear = false; // New clear prop - transparent background, no border, uses variant text colors
  @Input() rounded = false;

  // === LEGACY PROPS (DEPRECATED) ===
  @Input() gradient?: string; // Legacy - use customStyles instead
  @Input() background?: string; // Legacy - use customStyles instead
  @Input() color?: string; // Legacy - use customStyles instead
  @Input() dropdown = false; // Legacy - use separate dropdown component

  @Input() iconName = '';
  @Input() iconColor = ''; // Manual override - leave empty for automatic color matching
  @Input() iconSize = 20;
  @Input() iconPosition: 'left' | 'right' | 'only' = 'left';

  @Output() userClick = new EventEmitter<Event>();

  isActive = false;
  timeoutRef: ReturnType<typeof setTimeout> | null = null;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    // Glass effect is now default for all buttons
    // Active state now managed through interaction, not props
    this.isActive = false;
  }
  handleClick(event: Event): void {
    if (this.disabled) {
      event.preventDefault();
      return;
    }

    // Create ripple effect if pressedEffect is 'ripple'
    if (this.pressedEffect === 'ripple' && event instanceof MouseEvent) {
      this.createRipple(event);
    }

    this.setActiveState();
    this.userClick.emit(event);
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (!this.disabled) {
        // Create ripple at center for keyboard activation
        if (this.pressedEffect === 'ripple') {
          this.createKeyboardRipple();
        }
        this.setActiveState();
        this.userClick.emit(event);
      }
    }
  }

  onFocus(): void {
    // Focus event - let CSS handle visual focus states
    // This ensures programmatic focus management if needed
  }

  onBlur(): void {
    // Blur event - ensure any focus-related state is cleared
    // This helps ensure focus styles are properly removed
  }
  setActiveState(): void {
    this.isActive = true;
    this.timeoutRef = setTimeout(() => {
      this.isActive = false;
    }, 200);
  }

  createRipple(event: MouseEvent): void {
    const button = event.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();

    // Get the button's current color for natural ripple effect
    const buttonStyle = window.getComputedStyle(button);
    const buttonColor = buttonStyle.color;

    // Calculate base size and position
    const baseSize = Math.max(rect.width, rect.height);
    const centerX = event.clientX - rect.left;
    const centerY = event.clientY - rect.top;

    // Create multiple concentric ripples with different sizes and delays
    const rippleCount = 2;
    const sizeMultipliers = [1.2, 0.9, 0.6];
    const delays = [0, 150, 300];
    const animationClasses = [
      'ava-button-ripple-1',
      'ava-button-ripple-2',
      'ava-button-ripple-3',
    ];

    for (let i = 0; i < rippleCount; i++) {
      setTimeout(() => {
        const size = baseSize * sizeMultipliers[i];
        const x = centerX - size / 2;
        const y = centerY - size / 2;

        // Create ripple element
        const ripple = document.createElement('span');

        // Set position and size
        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;

        // Set the button's color as a CSS custom property for the ripple
        ripple.style.setProperty('--ripple-color', buttonColor);

        // Add CSS class for specific animation timing
        ripple.classList.add(animationClasses[i]);

        // Add ripple to button
        button.appendChild(ripple);

        // Remove ripple after animation completes
        ripple.addEventListener('animationend', () => {
          ripple.remove();
        });
      }, delays[i]);
    }
  }

  createKeyboardRipple(): void {
    const button = this.elementRef.nativeElement;
    const rect = button.getBoundingClientRect();

    // Get the button's current color for natural ripple effect
    const buttonStyle = window.getComputedStyle(button);
    const buttonColor = buttonStyle.color;

    // Calculate base size and center position for keyboard activation
    const baseSize = Math.max(rect.width, rect.height);
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    // Create multiple concentric ripples with same config as mouse ripples
    const rippleCount = 3;
    const sizeMultipliers = [1.2, 0.9, 0.6];
    const delays = [0, 150, 300];
    const animationClasses = [
      'ava-button-ripple-1',
      'ava-button-ripple-2',
      'ava-button-ripple-3',
    ];

    for (let i = 0; i < rippleCount; i++) {
      setTimeout(() => {
        const size = baseSize * sizeMultipliers[i];
        const x = centerX - size / 2;
        const y = centerY - size / 2;

        // Create ripple element
        const ripple = document.createElement('span');

        // Set position and size
        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;

        // Set the button's color as a CSS custom property for the ripple
        ripple.style.setProperty('--ripple-color', buttonColor);

        // Add CSS class for specific animation timing
        ripple.classList.add(animationClasses[i]);

        // Add ripple to button
        button.appendChild(ripple);

        // Remove ripple after animation completes
        ripple.addEventListener('animationend', () => {
          ripple.remove();
        });
      }, delays[i]);
    }
  }

  // === COMPUTED PROPERTIES FOR TEMPLATE ===

  get hasIcon(): boolean {
    return !!this.iconName;
  }

  /**
   * Computes the icon color automatically based on button state and variant.
   *
   * Color Logic:
   * - Disabled: Uses disabled color token
   * - Manual override: Uses iconColor prop if provided
   * - Outlined & Clear buttons: Icon color matches variant color (primary = pink, success = green, etc.)
   * - Filled buttons: Icon color is white (matches text on colored backgrounds)
   *
   * This ensures icons always have proper contrast and semantic meaning.
   */
  get computedIconColor(): string {
    // Disabled state
    if (this.disabled) {
      return 'var(--button-icon-color-disabled)';
    } else if (this.variant === 'secondary') {
      return 'rgb(var(--rgb-brand-primary))';
    }

    // Manual color override
    if (this.iconColor && this.isValidColor(this.iconColor)) {
      return this.iconColor;
    }

    // Automatic color based on button state and variant
    if (this.outlined || this.clear) {
      // Outlined and clear buttons: icon color matches variant color (same as text)
      switch (this.variant) {
        case 'primary':
        case 'default':
          return 'rgb(var(--rgb-brand-primary))';
        case 'success':
          return 'rgb(var(--rgb-brand-success))';
        case 'warning':
          return 'rgb(var(--rgb-brand-warning))';
        case 'danger':
          return 'rgb(var(--rgb-brand-danger))';
        case 'info':
          return 'rgb(var(--rgb-brand-info))';
        // case 'secondary':
        //   return 'rgb(var(--rgb-brand-primary))'; // Secondary uses primary color
        default:
          return 'rgb(var(--rgb-brand-primary))';
      }
    } else {
      // Filled buttons: icon color should match text color for consistency
      return 'var(--button-text-on-color-primary)'; // Instead of 'var(--color-text-on-brand)'
    }
  }

  get buttonClasses(): string {
    const classes = [
      'ava-button',
      // Only add variant class if it's not 'default'
      ...(this.variant !== 'default' ? [this.variant] : []), // primary, secondary, success, etc. (not default)
      this.size, // small, medium, large
      `ava-button--${this.glassVariant}`, // ava-button--glass-25, etc.
      `ava-button--hover-${this.hoverEffect}`, // ava-button--hover-torch, etc.
      `ava-button--pressed-${this.pressedEffect}`, // ava-button--pressed-ripple, etc.
      `ava-button--focus-${this.focusEffect}`, // ava-button--focus-border, etc.
      `ava-button--disabled-${this.disabledEffect}`, // ava-button--disabled-dim, etc.
    ];

    // Add conditional classes
    if (this.processing) {
      classes.push(`ava-button--processing-${this.processingEffect}`); // ava-button--processing-pulse
    }

    if (this.disabled) {
      classes.push('ava-button--disabled');
    }

    if (this.pill) {
      classes.push('ava-button--pill');
    }

    if (this.outlined) {
      classes.push('ava-button--outlined');
    }

    if (this.clear) {
      classes.push('ava-button--clear');
    }

    if (this.isActive) {
      classes.push('ava-button--active');
    }

    if (this.iconPosition === 'only') {
      classes.push('ava-button--icon-only');
    }

    if(this.rounded){
       classes.push('ava-button--icon-only');
    }

    return classes.filter((cls) => cls).join(' ');
  }

  get computedStyles(): Record<string, string> {
    const styles: Record<string, string> = {
      // Set button effect color for this variant (use correct semantic token name)
      '--button-effect-color': `var(--button-variant-${this.variant}-effect-color)`,

      // Apply custom width/height if provided
      ...(this.width && { width: this.width }),
      ...(this.height && { height: this.height }),

      // Legacy gradient support
      ...(this.gradient && { background: this.gradient }),
      ...(this.background && { background: this.background }),
      ...(this.color && { color: this.color }),

      // Apply custom styles (this allows CSS custom property overrides)
      ...this.customStyles,
    };

    return styles;
  }

  isValidColor(value: string): boolean {
    const s = new Option().style;
    s.color = value;
    return s.color !== '';
  }

  getIconSize() {
    if (this.size === 'xlarge') {
      return 32;
    } else if (this.size === 'large') {
      return 19;
    } else if (this.size === 'medium') {
      return 15;
    } else if (this.size === 'small') {
      return 13;
    } else if (this.size === 'xsmall') {
      return 12;
    }
    return this.iconSize;
  }

  ngOnDestroy(): void {
    if (this.timeoutRef) {
      clearTimeout(this.timeoutRef);
    }
  }
}
