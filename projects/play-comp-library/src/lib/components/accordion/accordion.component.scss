// Size-specific variables
$accordion-small-height: 2.25rem; // 36px
$accordion-small-padding: 0.5rem; // 8px
$accordion-small-font-size: 0.875rem; // 14px
$accordion-small-line-height: 1.125rem; // 18px
$accordion-small-icon-size: 1rem; // 16px
$accordion-small-gap: 0.25rem; // 4px

$accordion-medium-height: 2.75rem; // 44px
$accordion-medium-padding: 0.75rem; // 12px
$accordion-medium-font-size: 1rem; // 16px
$accordion-medium-line-height: 1.25rem; // 20px
$accordion-medium-icon-size: 1.25rem; // 20px
$accordion-medium-gap: 0.5rem; // 8px

$accordion-large-height: 3rem; // 48px
$accordion-large-padding: 0.875rem; // 14px
$accordion-large-font-size: 1.25rem; // 20px
$accordion-large-line-height: 1.5rem; // 24px
$accordion-large-icon-size: 1.25rem; // 20px
$accordion-large-gap: 0.5rem; // 8px

// General variables
$accordion-width: 40rem; // 640px
$accordion-margin-bottom: 1rem; // 20px
$accordion-border-radius: 0.5rem; //8px
$accordion-background: var(--color-background-primary);
$accordion-font-family: var(--accordion-font-family); //inter
$accordion-color-gray: var(--accordion-dark-header-background);
$accordion-dark-bg-color: var(--accordion-dark-content-text);

.accordion-container {
  width: 100%;
  max-width: $accordion-width;
  border-radius: $accordion-border-radius;
  overflow: hidden;
  margin-bottom: $accordion-margin-bottom;
  position: relative;
  background: $accordion-background;
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 0.5px solid var(--color-border-subtle);

  .accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    cursor: pointer;
    box-sizing: border-box;
    color: $accordion-color-gray;
    font-family: $accordion-font-family;
    font-style: normal;
    position: relative;
    transition:
      background-color 0.2s ease,
      transform 0.2s ease;
  }

  // Size-specific styles for small
  &.size-small {
    .accordion-header {
      min-height: $accordion-small-height;
      padding: 0 $accordion-small-padding;
      line-height: $accordion-small-line-height;
    }

    .header-row {
      gap: $accordion-small-gap;
    }

    .accordion-icon {
      width: $accordion-small-icon-size;
      height: $accordion-small-icon-size;
    }
    
    .accordion-content {
      padding: 0rem $accordion-small-padding 1rem $accordion-small-padding;
      font-size: $accordion-small-font-size;
      line-height: $accordion-small-line-height;
    }
  }

  // Size-specific styles for medium (default)
  &.size-medium {
    .accordion-header {
      min-height: $accordion-medium-height;
      padding: 0 $accordion-medium-padding;
      line-height: $accordion-medium-line-height;
    }

    .header-row {
      gap: $accordion-medium-gap;
    }

    .accordion-icon {
      width: $accordion-medium-icon-size;
      height: $accordion-medium-icon-size;
    }

    .accordion-content {
      padding: 0rem $accordion-medium-padding 1.5rem $accordion-medium-padding;
      font-size: $accordion-medium-font-size;
      line-height: $accordion-medium-line-height;
    }
  }

  // Size-specific styles for large
  &.size-large {
    .accordion-header {
      min-height: $accordion-large-height;
      padding: 0 $accordion-large-padding;
      font-size: $accordion-large-font-size;
      line-height: $accordion-large-line-height;
      font-weight: 500; // Medium
    }

    .header-row {
      gap: $accordion-large-gap;
    }

    .accordion-icon {
      width: $accordion-large-icon-size;
      height: $accordion-large-icon-size;
    }

    .accordion-content {
      padding: 0rem $accordion-large-padding 1.5rem $accordion-large-padding;
      font-size: $accordion-large-font-size;
      line-height: $accordion-large-line-height;
    }
  }

  // Show gradient border only when accordion is expanded
  &.expanded .accordion-header {
    // Hover effect
    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    // Active/pressed effect
    &:active {
      transform: translateY(0);
      background-color: rgba(0, 0, 0, 0.04);
    }

    // Focus effect for accessibility
    &:focus-visible {
      outline: 2px solid var(--color-border-focus);
      outline-offset: 2px;
    }
  }

  .accordion-body {
    overflow: hidden;
    height: 0;
    transition: height 0.4s ease-in-out;
    background: none;
  }

  .accordion-content {
    color: var(--accordion-dark-content-background);
    font-family: $accordion-font-family;
    font-style: normal;
    font-weight: var(--accordion-font-weight);
  }

  .accordion-title-highlight {
    font-weight: 500;
  }

  .header-row {
    display: flex;
    align-items: center;
    width: 100%;

    .accordion-title {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .right-aligned-icon {
      margin-left: auto;
    }
  }
}

// When withoutBox is active, suppress the header pseudo-divider
.accordion-container.without-box {
  .accordion-header::after {
    display: none;
  }
}

// Divider element used when withoutBox is true
.accordion-divider {
  height: 1px;
  margin-bottom: 2%;
  margin-left: 2%;
  margin-right: 2%;
  background: var(--color-border-default);
}

.accordion-icon {
  transition: transform 0.4s ease-in-out;
  color: $accordion-color-gray !important;
}

.accordion-title-icon {
  color: $accordion-color-gray !important;
}

// Rotate icon when accordion is open
.accordion-container:has(.accordion-body.open) .accordion-icon {
  transform: rotate(360deg);
}

.accordion-container.accordion-dark {
  background: $accordion-dark-bg-color;
  
  .accordion-title {
    background: $accordion-dark-bg-color;
  }
  
  .accordion-title-highlight {
    background: $accordion-dark-bg-color;
    color: var(--accordion-background);
  }

  .accordion-body {
    background: $accordion-dark-bg-color;
  }
  
  .accordion-content {
    color: var(--accordion-background);
  }
}

// Media query for reduced motion accessibility
@media (prefers-reduced-motion: reduce) {
  .accordion-container {
    .accordion-body {
      transition: none;
    }
    
    .accordion-icon {
      transition: none;
    }
  }
}