<div class="accordion-container" [ngClass]="accordionClasses">
  <div class="accordion-header" (click)="toggleExpand()" tabindex="0" role="button" [attr.aria-expanded]="expanded"
    (keydown.enter)="toggleExpand()" (keydown.space)="toggleExpand()">
    <div class="header-row">
      <!-- TYPE: titleIcon -->
      <ng-container *ngIf="type === 'titleIcon'">
        <!-- LEFT: Static Title Icon -->
        <ava-icon [iconName]="titleIcon" [iconSize]="currentIconSize" [attr.aria-label]="true" class="accordion-title-icon" [iconColor]="iconColor">
        </ava-icon>
      </ng-container>
  
      <!-- DEFAULT ICON LEFT -->
      <ng-container *ngIf="type !== 'titleIcon' && iconPosition === 'left'">
        <ava-icon [iconName]="expanded ? iconOpen : iconClosed" [iconSize]="currentIconSize" [attr.aria-label]="true"
          class="accordion-icon" [iconColor]="iconColor">
        </ava-icon>
      </ng-container>
  
      <!-- TITLE: Always Rendered -->
      <span class="accordion-title" [ngClass]="['body-medium-default-' + currentFontSize + '-inter']">
          <ng-content select="[header]"></ng-content>
      </span>
  
      <!-- ICON RIGHT -->
      <ng-container *ngIf="
            (type !== 'titleIcon' && iconPosition === 'right') ||
            type === 'titleIcon'
          ">
        <ava-icon [iconName]="expanded ? iconOpen : iconClosed" [iconSize]="currentIconSize" [attr.aria-label]="true"
          class="accordion-icon" [iconColor]="iconColor">
        </ava-icon>
      </ng-container>
    </div>
  </div>

  <div class="accordion-divider" *ngIf="withoutBox && showHeaderDivider"></div>

  <div class="accordion-body" [ngClass]="{ 'animated-content': animation, open: expanded }"
    [style.height]="expanded ? contentHeight + 'px' : '0'" #bodyRef (transitionend)="onBodyTransitionEnd($event)">
    <div class="accordion-content">
      <ng-content select="[content]"></ng-content>
    </div>
  </div>

  <div class="accordion-divider" *ngIf="withoutBox && showContentDivider"></div>
</div>