<ul class="tree" [ngClass]="[size, 'icon-' + iconPosition]" *ngIf="nodes?.length">
    <ng-container *ngFor="let node of nodes">
        <li [style.padding-left.px]="calculateIndent(node.level)">
            <div class="tree-node" [class.selected]="node.selected" (click)="selectNode(node)" [ngClass]="iconPosition">

                <!-- LEFT SIDE ICONS -->
                <ng-container *ngIf="iconPosition === 'left'">
                    <!-- Chevron -->
                    <span class="toggle" *ngIf="node.children?.length"
                        (click)="toggleExpand(node); $event.stopPropagation()">
                        <lucide-angular [name]="node.expanded ? 'chevron-down' : 'chevron-right'">
                        </lucide-angular>
                    </span>

                    <!-- Folder/File Icon -->
                    <lucide-angular *ngIf="node.icon"
                        [name]="node.expanded && node.icon === 'folder' ? 'folder-open' : node.icon">
                    </lucide-angular>

                    <!-- Label -->
                    <span class="label">{{ node.name }}</span>
                </ng-container>

                <!-- RIGHT SIDE ICONS -->
                <ng-container *ngIf="iconPosition === 'right'">
                    <!-- Folder/File Icon -->
                    <lucide-angular *ngIf="node.icon"
                        [name]="node.expanded && node.icon === 'folder' ? 'folder-open' : node.icon">
                    </lucide-angular>

                    <!-- Label -->
                    <span class="label">{{ node.name }}</span>

                    <!-- Chevron -->
                    <span class="toggle" *ngIf="node.children?.length"
                        (click)="toggleExpand(node); $event.stopPropagation()">
                        <lucide-angular [name]="node.expanded ? 'chevron-down' : 'chevron-right'">
                        </lucide-angular>
                    </span>
                </ng-container>
            </div>

            <!-- Recursive children -->
            <ava-treeview *ngIf="node.expanded && node.children?.length" [nodes]="node.children || []" [size]="size"
                [iconPosition]="iconPosition" [level]="(node.level ?? 0) + 1" (nodeSelect)="nodeSelect.emit($event)">
            </ava-treeview>
        </li>
    </ng-container>
</ul>