// treeview.component.ts
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';

export interface TreeNode {
  id?: string | number;
  name: string;
  icon?: string;
  expanded?: boolean;
  selected?: boolean;
  level?: number;
  children?: TreeNode[];
}

@Component({
  selector: 'ava-treeview',
  standalone: true,
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './treeview.component.html',
  styleUrls: ['./treeview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TreeviewComponent {
  @Input() nodes: TreeNode[] = [];
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() level = 0;

  @Output() nodeSelect = new EventEmitter<TreeNode>();

  toggleExpand(node: TreeNode) {
    if (node.children?.length) {
      node.expanded = !node.expanded;
      // Trigger a view update by creating a new array reference
      this.nodes = [...this.nodes];
    }
  }

  selectNode(node: TreeNode) {
    this.nodeSelect.emit(node);
  }

  calculateIndent(level?: number): number {
    const nodeLevel = level ?? this.level;
    const sizeIndentMap: Record<typeof this.size, number> = {
      xs: 24,
      sm: 25,
      md: 25,
      lg: 20,
      xl: 24,
    };
    return nodeLevel * (sizeIndentMap[this.size] || 16);
  }
}