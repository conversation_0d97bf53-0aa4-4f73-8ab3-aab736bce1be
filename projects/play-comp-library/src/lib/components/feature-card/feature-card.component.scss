.ava-featured-card-container {
    width: 100%;
    padding: 32px 28px 40px;
    border-radius: 28px;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1.8px solid rgba(255 255 255 / 0.13);
    box-shadow: inset 0 0 40px rgba(255 255 255 / 0.06);
    display: flex;
    flex-direction: column;
    gap: 20px;
    user-select: none;
    position: relative;
    text-align: center;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow:
            0 0 48px 12px var(--glow-color),
            inset 0 0 48px 14px var(--glow-color-inset);
    }

    .card-header {
        color: #e5e7f0;
        letter-spacing: 2.1px;
    }

    .card-content {
        line-height: 1rem;
        user-select: none;
    }

    .card-footer {
        margin-top: 3rem;
        margin-bottom: 1rem;
    }
}

/* --- Variants --- */
/* Variant 1: Neon Blue */
.blue {
    --glow-color: rgba(50, 100, 255, 0.7);
    --glow-color-inset: rgba(70, 110, 255, 0.5);
    background: rgba(10, 16, 38, 0.4);
    border-color: rgba(50, 110, 255, 0.5);
    color: #c2cdfc;
    box-shadow: inset 0 0 26px rgba(50, 110, 255, 0.3);

    .card-content {
        color: #9bb2ff;
        text-shadow: 0 0 22px rgba(50, 90, 255, 0.5);
    }
}

/* Variant 2: Purple Glow */
.purple {
    --glow-color: rgba(190, 120, 255, 0.7);
    --glow-color-inset: rgba(200, 130, 255, 0.5);
    background: rgba(45, 20, 65, 0.42);
    border-color: rgba(180, 110, 255, 0.5);
    color: #d8ccff;
    box-shadow: inset 0 0 28px rgba(190, 110, 255, 0.3);

    .card-content {
        color: #dabaff;
        text-shadow: 0 0 22px rgba(190, 90, 255, 0.5);
    }
}

/* Variant 3: Green Neon */
.green {
    --glow-color: rgba(60, 220, 140, 0.7);
    --glow-color-inset: rgba(90, 240, 170, 0.45);
    background: rgba(10, 37, 27, 0.42);
    border-color: rgba(40, 230, 140, 0.55);
    color: #addeb8;
    box-shadow: inset 0 0 30px rgba(50, 230, 130, 0.25);

    .card-content {
        color: #b4f1c2;
        text-shadow: 0 0 20px rgba(50, 240, 140, 0.55);
    }
}

/* Variant 4: Warm Orange */
.orange {
    --glow-color: rgba(255, 155, 100, 0.7);
    --glow-color-inset: rgba(255, 170, 110, 0.45);
    background: rgba(40, 20, 10, 0.42);
    border-color: rgba(255, 150, 70, 0.52);
    color: #ffd8ae;
    box-shadow: inset 0 0 30px rgba(255, 170, 110, 0.3);

    .card-content {
        color: #ffc092;
        text-shadow: 0 0 24px rgba(255, 155, 70, 0.55);
    }
}

/* Variant 5: Red */
.red {
    --glow-color: rgba(255, 60, 80, 0.7);
    --glow-color-inset: rgba(255, 80, 90, 0.45);
    background: rgba(50, 10, 20, 0.42);
    border-color: rgba(255, 80, 90, 0.5);
    color: #ffcdd2;
    box-shadow: inset 0 0 30px rgba(255, 70, 80, 0.3);

    .card-content {
        color: #ffb3b3;
        text-shadow: 0 0 20px rgba(255, 80, 80, 0.5);
    }
}

/* Variant 6: Teal */

.teal {
    --glow-color: rgba(0, 210, 200, 0.7);
    --glow-color-inset: rgba(0, 230, 210, 0.45);
    background: rgba(10, 38, 37, 0.42);
    border-color: rgba(0, 230, 220, 0.5);
    color: #c5f9f7;
    box-shadow: inset 0 0 28px rgba(0, 230, 220, 0.3);

    .card-content {
        color: #a8f3f1;
        text-shadow: 0 0 20px rgba(0, 210, 200, 0.5);
    }
}