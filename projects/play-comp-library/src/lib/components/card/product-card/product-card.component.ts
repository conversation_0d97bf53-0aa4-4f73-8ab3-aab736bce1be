import { Component, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ava-card',
  standalone: true,
  imports: [CommonModule], // Add CommonModule here for *ngIf, etc.
  templateUrl: './product-card.component.html',
  styleUrls: ['./product-card.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class CardComponent {
  // REMOVED: title, icon, iconBg as header content is now fully projected.

  @Input() showHeader: boolean = true;
  @Input() showBody: boolean = true;
  @Input() showFooter: boolean = false;
  @Input() cardClass: string = '';
  @Input() applyHeaderPadding: boolean = true; // Still useful for the header container
  @Input() applyBodyPadding: boolean = true;
  @Input() applyFooterPadding: boolean = true;

  constructor() {}
}