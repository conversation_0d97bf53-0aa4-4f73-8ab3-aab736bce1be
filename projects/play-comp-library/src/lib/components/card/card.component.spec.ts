import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CardComponent } from './card.component';
import {
  TestUtils,
  TestData,
  TestExpectations,
} from '../../testing/test-utils';

describe('CardComponent', () => {
  let component: CardComponent;
  let fixture: ComponentFixture<CardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CardComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ===== BASIC FUNCTIONALITY TESTS =====
  describe('Basic Functionality', () => {
    it('should render card element', () => {
      TestExpectations.elementToBePresent(fixture, '.ava-card');
    });

    it('should render card header when provided', () => {
      component.header = 'Card Header';
      fixture.detectChanges();
      TestExpectations.elementToBePresent(fixture, '.ava-card-header');
      TestExpectations.elementToHaveText(
        fixture,
        '.ava-card-header',
        'Card Header'
      );
    });

    it('should render card content when provided', () => {
      component.content = 'Card Content';
      fixture.detectChanges();
      TestExpectations.elementToBePresent(fixture, '.ava-card-content');
      TestExpectations.elementToHaveText(
        fixture,
        '.ava-card-content',
        'Card Content'
      );
    });

    it('should render card footer when provided', () => {
      component.footer = 'Card Footer';
      fixture.detectChanges();
      TestExpectations.elementToBePresent(fixture, '.ava-card-footer');
      TestExpectations.elementToHaveText(
        fixture,
        '.ava-card-footer',
        'Card Footer'
      );
    });
  });

  // ===== VARIANT TESTS =====
  describe('Card Variants', () => {
    it.each(TestData.colors)('should apply %s variant class', (variant) => {
      component.variant = variant;
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-card',
        `ava-card--${variant}`
      );
    });
  });

  // ===== SIZE TESTS =====
  describe('Card Sizes', () => {
    it.each(TestData.sizes)('should apply %s size class', (size) => {
      component.size = size;
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-card',
        `ava-card--${size}`
      );
    });
  });

  // ===== STYLE TESTS =====
  describe('Card Styles', () => {
    it('should apply elevated style', () => {
      component.elevated = true;
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-card',
        'ava-card--elevated'
      );
    });

    it('should apply outlined style', () => {
      component.outlined = true;
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-card',
        'ava-card--outlined'
      );
    });

    it('should apply rounded style', () => {
      component.rounded = true;
      fixture.detectChanges();
      TestExpectations.elementToHaveClass(
        fixture,
        '.ava-card',
        'ava-card--rounded'
      );
    });

    it('should apply custom width and height', () => {
      component.width = '300px';
      component.height = '200px';
      fixture.detectChanges();
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.style.width).toBe('300px');
      expect(card.style.height).toBe('200px');
    });

    it('should apply custom styles', () => {
      component.customStyles = {
        backgroundColor: 'lightblue',
        border: '2px solid blue',
      };
      fixture.detectChanges();
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.style.backgroundColor).toBe('lightblue');
      expect(card.style.border).toBe('2px solid blue');
    });
  });

  // ===== INTERACTION TESTS =====
  describe('Card Interactions', () => {
    it('should emit click event when clicked', () => {
      spyOn(component.cardClick, 'emit');
      TestUtils.clickElement(fixture, '.ava-card');
      expect(component.cardClick.emit).toHaveBeenCalled();
    });

    it('should be clickable by default', () => {
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.style.cursor).toBe('pointer');
    });

    it('should not be clickable when disabled', () => {
      component.disabled = true;
      fixture.detectChanges();
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.style.cursor).toBe('not-allowed');
    });
  });

  // ===== ACCESSIBILITY TESTS =====
  describe('Accessibility', () => {
    it('should have proper role attribute', () => {
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.getAttribute('role')).toBe('article');
    });

    it('should have proper tabindex when clickable', () => {
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.getAttribute('tabindex')).toBe('0');
    });

    it('should not be focusable when disabled', () => {
      component.disabled = true;
      fixture.detectChanges();
      const card = TestUtils.getElement(fixture, '.ava-card').nativeElement;
      expect(card.getAttribute('tabindex')).toBe('-1');
    });

    it('should handle keyboard events when enabled', () => {
      spyOn(component.cardClick, 'emit');
      TestUtils.triggerKeyEvent(fixture, '.ava-card', 'keydown', 'Enter');
      expect(component.cardClick.emit).toHaveBeenCalled();
    });

    it('should not handle keyboard events when disabled', () => {
      component.disabled = true;
      fixture.detectChanges();
      spyOn(component.cardClick, 'emit');
      TestUtils.triggerKeyEvent(fixture, '.ava-card', 'keydown', 'Enter');
      expect(component.cardClick.emit).not.toHaveBeenCalled();
    });
  });

  // ===== EDGE CASES =====
  describe('Edge Cases', () => {
    it('should handle empty content gracefully', () => {
      component.header = '';
      component.content = '';
      component.footer = '';
      fixture.detectChanges();
      expect(component).toBeTruthy();
    });

    it('should handle very long content', () => {
      component.content = TestData.longText;
      fixture.detectChanges();
      TestExpectations.elementToHaveText(
        fixture,
        '.ava-card-content',
        TestData.longText
      );
    });

    it('should handle HTML content safely', () => {
      component.content = '<strong>Bold text</strong> and <em>italic text</em>';
      fixture.detectChanges();
      const content = TestUtils.getElement(
        fixture,
        '.ava-card-content'
      ).nativeElement;
      expect(content.innerHTML).toContain('<strong>Bold text</strong>');
      expect(content.innerHTML).toContain('<em>italic text</em>');
    });

    it('should handle null/undefined inputs gracefully', () => {
      component.variant = null as any;
      component.size = undefined as any;
      fixture.detectChanges();
      expect(component).toBeTruthy();
    });
  });

  // ===== PERFORMANCE TESTS =====
  describe('Performance', () => {
    it('should render quickly with many properties', () => {
      const startTime = performance.now();

      component.variant = 'primary';
      component.size = 'large';
      component.elevated = true;
      component.outlined = true;
      component.rounded = true;
      component.header = 'Complex Header';
      component.content = 'Complex content with lots of text and formatting';
      component.footer = 'Complex Footer';
      component.customStyles = { backgroundColor: 'lightblue' };

      fixture.detectChanges();

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(100); // Should render in less than 100ms
    });
  });
});
