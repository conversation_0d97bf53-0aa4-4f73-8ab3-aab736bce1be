import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { SnackbarService } from './snackbar.service';

describe('SnackbarService', () => {
  let service: SnackbarService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SnackbarService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should emit snackbar data on show', () => {
    service.show('Test message', 'top-left', 5000, 'red', 'yellow');
    const result = service.snackbar$();
    expect(result).toEqual({
      message: 'Test message',
      duration: 5000,
      position: 'top-left',
      color: 'red',
      backgroundColor: 'yellow',
    });
  });

  it('should clear snackbar after duration', fakeAsync(() => {
    service.show('Temp message', 'bottom-right', 1000);
    tick(1000);
    expect(service.snackbar$()).toBeNull();
  }));
});
