.ava-snackbar {
  position: fixed;
  padding: 12px 20px;
  border-radius: 8px;
  z-index: 9999;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
  max-width: 500px;

  &.top-left {
    top: 20px;
    left: 20px;
  }

  &.top-right {
    top: 20px;
    right: 20px;
  }

  &.bottom-left {
    bottom: 20px;
    left: 20px;
  }

  &.bottom-right {
    bottom: 20px;
    right: 20px;
  }

  &.top-center {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  &.bottom-center {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  &.center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .snackbar-icon {
    flex-shrink: 0;
  }

  .snackbar-message {
    flex: 1;
    margin-right: auto;
  }

  .snackbar-action {
    flex-shrink: 0;
    margin-left: 12px;
  }

  .snackbar-dismiss {
    flex-shrink: 0;
    margin-left: 12px;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

// Surface-bold variant styles
.ava-snackbar.surface-bold {
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  color: #222;

  &.medium {
    background: rgba(255, 255, 255, 0.5);
  }
  &.strong {
    background: rgba(255, 255, 255, 0.75);
  }
  &.max {
    background: rgba(255, 255, 255, 1);
  }
}

/* Wrapper for custom snackbar */
.custom-snackbar-wrapper {
  position: fixed;
  display: flex;
  justify-content: center;
  padding: var(--snackbar-padding-wrapper);
  z-index: 9999;
  width: 100%;
  pointer-events: none;
}

/* Positioning for snackbar */
.custom-snackbar-wrapper.top-left {
  top: 20px;
  left: 20px;
  justify-content: flex-start;
}

.custom-snackbar-wrapper.top-right {
  top: 20px;
  right: 20px;
  justify-content: flex-end;
}

.custom-snackbar-wrapper.bottom-left {
  bottom: 20px;
  left: 20px;
  justify-content: flex-start;
}

.custom-snackbar-wrapper.bottom-right {
  bottom: 20px;
  right: 20px;
  justify-content: flex-end;
}

.custom-snackbar-wrapper.top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-snackbar-wrapper.bottom-center {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-snackbar-wrapper.center {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Snackbar box */
.custom-snackbar {
  pointer-events: auto;
  display: flex;
  align-items: center;
  max-width: 420px;
  width: 100%;
  box-sizing: border-box;
  border-radius: var(--snackbar-radius-sm);
  border: 1px solid;
  gap: 16px;
  font-family: var(--snackbar-font-family);
  box-shadow: var(--snackbar-shadowbox);
}

/* Sizes */
.custom-snackbar.sm {
  padding: var(--snackbar-padding-small);
  font-size: var(--snackbar-font-small);
}

.custom-snackbar.md {
  padding: var(--snackbar-padding-medium);
  font-size: var(--snackbar-font-medium);
}

.custom-snackbar.lg {
  padding: var(--snackbar-padding-large);
  font-size: var(--snackbar-font-large);
}
.icon-circle {
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.custom-snackbar.sm .icon-circle {
  width: 1.75rem;
  height: 1.75rem;
}

.custom-snackbar.md .icon-circle {
  width: 2.125rem;
  height: 2.125rem;
}

.custom-snackbar.lg .icon-circle {
  width: 2.5rem;
  height: 2.5rem;
}

/* Text container */
.snackbar-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.title {
  font-weight: var(--snackbar-font-weight-semibold);
  color: var(--snackbar-title-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subtitle {
  font-weight: var(--snackbar-font-weight-regular);
  color: var(--snackbar-subtitle-color);
  margin-top: 2px;
  font-size: var(--snackbar-font-title);
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
}

/* Button */
.snackbar-button {
  flex-shrink: 0;
  cursor: pointer;
  transition: filter 0.3s ease;
  border-radius: var(--snackbar-radius-sm);
  font-size: var(--snackbar-font-large);
  padding: var(--snackbar-padding-default);
}

.snackbar-button:hover {
  filter: brightness(0.9);
}

.custom-snackbar.sm .title {
  font-size: var(--snackbar-font-small);
}

.custom-snackbar.sm .subtitle {
  font-size: var(--snackbar-font-small);
}

.custom-snackbar.md .title {
  font-size: var(--snackbar-font-title);
}

.custom-snackbar.md .subtitle {
  font-size: var(--snackbar-font-subtitle);
}

.custom-snackbar.lg .title {
  font-size: var(--snackbar-font-large-title);
}

.custom-snackbar.lg .subtitle {
  font-size: var(--snackbar-font-large);
}

.message-success {
  border-color: var(--snackbar-success-color);
}

.message-warning {
  border-color: var(--snackbar-warning-color);
}

.message-danger {
  border-color: var(--snackbar-error-color);
}

.message-info {
  border-color: var(--snackbar-info-color);
}

.icon-circle-success {
  background-color: var(--snackbar-success-color);
}
.icon-circle-warning {
  background-color: var(--snackbar-warning-color);
}

.icon-circle-danger {
  background-color: var(--snackbar-error-color);
}

.icon-circle-info {
  background-color: var(--snackbar-info-color);
}
