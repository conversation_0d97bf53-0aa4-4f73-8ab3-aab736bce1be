// snackbar.service.ts
import { Injectable, signal } from '@angular/core';

export type SnackbarPosition =
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'top-center'
  | 'bottom-center'
  | 'center';

export interface SnackbarAction {
  text: string;
  color: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  href?: string;
  callback?: () => void;
  showXInside?: boolean;
}

export interface SnackbarIcon {
  name: string;
  color: string;
  size?: number;
  position?: 'left' | 'right';
  iconClose?: boolean;
}

export interface SnackbarData {
  message: string;
  title?: string;
  duration: number;
  position: SnackbarPosition;
  color: string;
  backgroundColor: string;
  action?: SnackbarAction;
  icon?: SnackbarIcon;
  dismissible?: boolean;
  persistent?: boolean;
  type?: 'medium' | 'strong' | 'max' | 'custom' | string;
  variant?: 'surface-bold' | string;
  buttonLabel?: string; // NEW
  onButtonClick?: (event: Event) => void;
  size?: 'sm' | 'md' | 'lg';
  buttonSize?: 'small' | 'medium' | 'large';
  styleType?: 'success' | 'warning' | 'danger' | 'info';
  IconName?: string;
}

@Injectable({ providedIn: 'root' })
export class SnackbarService {
  private snackbarSignal = signal<SnackbarData | null>(null);
  readonly snackbar$ = this.snackbarSignal.asReadonly();

  show(
    message: string,
    position: SnackbarPosition = 'bottom-center',
    duration = 3000,
    color = '#fff',
    backgroundColor = '#6B7280',
    options?: {
      title?: string;
      action?: SnackbarAction;
      icon?: SnackbarIcon;
      dismissible?: boolean;
      persistent?: boolean;
      variant?: 'surface-bold' | string;
      type?: 'medium' | 'strong' | 'max' | 'custom' | string;
      buttonLabel?: string;
      onButtonClick?: (event: Event) => void;
      size?: 'sm' | 'md' | 'lg';
      buttonSize?: 'small' | 'medium' | 'large';
      styleType?: 'success' | 'warning' | 'danger' | 'info';
    }
  ) {
    this.snackbarSignal.set({
      title: options?.title,
      message,
      duration,
      position,
      color,
      backgroundColor,
      action: options?.action,
      icon: options?.icon,
      dismissible: options?.dismissible ?? false,
      persistent: options?.persistent ?? false,
      variant: options?.variant,
      type: options?.type,
      buttonLabel: options?.buttonLabel,
      onButtonClick: options?.onButtonClick,
      size: options?.size,
      buttonSize: options?.buttonSize,
      styleType: options?.styleType,
    });

    // Only auto-dismiss if not persistent
    if (!options?.persistent) {
      setTimeout(() => {
        this.snackbarSignal.set(null);
      }, duration);
    }
  }

  dismiss() {
    this.snackbarSignal.set(null);
  }
}
