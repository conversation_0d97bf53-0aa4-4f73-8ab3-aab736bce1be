<div
  *ngIf="snackbar$() && snackbar$()?.type !== 'custom'"
  class="ava-snackbar"
  [ngClass]="[
    snackbar$()?.position,
    variant ? variant : '',
    variant && type ? type : ''
  ]"
  [ngStyle]="{
    color: snackbar$()?.color,
    'background-color': snackbar$()?.backgroundColor
  }"
>
  <!-- Icon on left (if provided and position is left or not set) -->
  <ava-icon
    *ngIf="
      snackbar$()?.icon &&
      (!snackbar$()?.icon?.position || snackbar$()?.icon?.position === 'left')
    "
    [iconColor]="snackbar$()?.icon?.color ?? ''"
    [iconSize]="snackbar$()?.icon?.size || 16"
    [iconName]="snackbar$()?.icon?.name ?? ''"
    class="snackbar-icon"
  >
  </ava-icon>

  <!-- Message -->
  <span class="snackbar-message">{{ snackbar$()?.message }}</span>

  <!-- Action Link (with X icon inside, closes snackbar on click) -->
  <ng-container *ngIf="snackbar$()?.action && snackbar$()?.action?.showXInside">
    <ava-link
      [label]="snackbar$()?.action?.text || ''"
      [color]="snackbar$()?.action?.color || 'primary'"
      size="small"
      [href]="snackbar$()?.action?.href || ''"
      class="snackbar-action"
    >
      <ava-icon
        [iconName]="'x'"
        [iconSize]="14"
        [iconColor]="snackbar$()?.action?.color || 'primary'"
        (userClick)="onDismiss()"
        style="margin-left: 4px; cursor: pointer"
      ></ava-icon>
    </ava-link>
  </ng-container>
  <!-- Default Action Link (if provided) -->
  <ava-link
    *ngIf="snackbar$()?.action && !snackbar$()?.action?.showXInside"
    [label]="snackbar$()?.action?.text || ''"
    [color]="snackbar$()?.action?.color || 'primary'"
    size="small"
    [href]="snackbar$()?.action?.href || ''"
    (click)="onActionClick(snackbar$()?.action)"
    class="snackbar-action"
  >
  </ava-link>

  <!-- Icon on right (if provided and position is right) -->
  <ava-icon
    *ngIf="snackbar$()?.icon && snackbar$()?.icon?.position === 'right'"
    [iconColor]="snackbar$()?.icon?.color ?? ''"
    [iconSize]="snackbar$()?.icon?.size || 16"
    [iconName]="snackbar$()?.icon?.name ?? ''"
    class="snackbar-icon"
    [cursor]="!!snackbar$()?.icon?.iconClose"
    (userClick)="snackbar$()?.icon?.iconClose ? onDismiss() : null"
  >
  </ava-icon>

  <!-- Dismiss button (if dismissible) -->
  <ava-icon
    *ngIf="snackbar$()?.dismissible"
    iconColor="snackbar$()?.color"
    [iconSize]="16"
    [iconName]="'x'"
    [cursor]="true"
    (userClick)="onDismiss()"
    class="snackbar-dismiss"
  >
  </ava-icon>
</div>

<div
  *ngIf="snackbar$() && snackbar$()?.type === 'custom'"
  class="custom-snackbar-wrapper"
  [ngClass]="[snackbar$()?.position]"
>
  <div
    class="custom-snackbar message-success"
    [ngClass]="[
      snackbar$()?.size || 'large',
      'message-' + (snackbar$()?.styleType || 'success')
    ]"
    [ngStyle]="{
      'background-color': snackbar$()?.backgroundColor,
      
    }"
  >
    <div
      class="icon-circle"
      [ngClass]="['icon-circle-' + (snackbar$()?.styleType || 'success')]"
    >
      <ava-icon
        [iconName]="snackbar$()?.icon?.name ?? ''"
        [iconSize]="getIconSize(snackbar$()?.size)"
        iconColor="white"
      ></ava-icon>
    </div>

    <div class="snackbar-text">
      <div class="title">{{ snackbar$()?.title }}</div>
      <div class="subtitle">{{ snackbar$()?.message }}</div>
    </div>

    <ava-button
      *ngIf="snackbar$()?.buttonLabel"
      [label]="snackbar$()?.buttonLabel ?? ''"
      [size]="snackbar$()?.buttonSize ?? 'medium'"
      [variant]="snackbar$()?.styleType ?? 'success'"
      (userClick)="snackbar$()?.onButtonClick?.($event)"
      class="snackbar-button"
    ></ava-button>
  </div>
</div>
