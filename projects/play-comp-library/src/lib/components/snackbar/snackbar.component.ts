import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { SnackbarService } from './snackbar.service';
import { IconComponent } from '../icon/icon.component';
import { LinkComponent } from '../link/link.component';
import { ButtonComponent } from '../button/button.component';

@Component({
  selector: 'ava-snackbar',
  standalone: true,
  imports: [CommonModule, IconComponent, LinkComponent, ButtonComponent],
  templateUrl: './snackbar.component.html',
  styleUrls: ['./snackbar.component.scss'],
})
export class SnackbarComponent {
  snackbarService = inject(SnackbarService);

  // Directly expose the readonly signal to the template
  snackbar$ = this.snackbarService.snackbar$;

  // Helper getters for variant and type
  get variant() {
    return this.snackbar$()?.variant;
  }
  get type() {
    return this.snackbar$()?.type;
  }

  onActionClick(action: any) {
    if (action.callback) {
      action.callback();
    }
  }

  onDismiss() {
    this.snackbarService.dismiss();
  }

  getIconSize(size: 'sm' | 'md' | 'lg' | undefined): number {
    switch (size) {
      case 'sm':
        return 16;
      case 'md':
        return 24;
      default:
        return 20;
    }
  }

  onButtonClick(event: Event) {
    console.log('Button clicked:', event);
  }
}
