
<ul class="tree-ul">
  <li *ngFor="let node of data" class="tree-li">
    <div class="node-container"
         [class.selected]="isSelected(node)"
         (click)="onNodeClick(node, $event)">
      <div class="node-content">
        <span class="toggle-icon" *ngIf="node.children && node.children.length > 0">
          <ava-icon [iconName]="node.isExpanded ? 'chevron-down' : 'chevron-right'" [iconColor]="isSelected(node) ? selectedIconColor : defaultIconColor" iconSize="16"></ava-icon>
        </span>
        <span class="node-icon" *ngIf="node.icon">
          <ava-icon [iconName]="node.icon" [iconColor]="isSelected(node) ? selectedIconColor : defaultIconColor" iconSize="16"></ava-icon>
        </span>
        <span class="node-name">{{ node.name }}</span>
      </div>
    </div>
    <ul class="tree-ul" *ngIf="node.children && node.isExpanded">
      <ava-tree
        [data]="node.children"
        [selectionMode]="selectionMode"
        [selectedNodes]="selectedNodes"
        (selectionChange)="onChildSelectionChange($event)"
        (nodeClick)="onChildNodeClick($event)">
        <ng-content></ng-content>
      </ava-tree>
    </ul>
  </li>
</ul>

<!-- ng-content for any custom content -->
<ng-content></ng-content>