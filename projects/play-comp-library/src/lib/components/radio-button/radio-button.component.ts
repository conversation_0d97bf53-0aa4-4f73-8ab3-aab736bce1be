import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  OnInit,
  AfterViewInit,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';

export interface RadioOption {
  label: string;
  value: string;
  disabled?: boolean;
}

@Component({
  selector: 'ava-radio-button',
  imports: [CommonModule],
  templateUrl: './radio-button.component.html',
  styleUrl: './radio-button.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioButtonComponent),
      multi: true,
    },
  ],
})
export class RadioButtonComponent
  implements ControlValueAccessor, OnInit, AfterViewInit
{
  @Input() options: RadioOption[] = [];
  @Input() name = '';
  @Input() selectedValue = '';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() orientation: 'horizontal' | 'vertical' = 'vertical';
  @Input() color = '';
  @Input() labelColor = '';
  @Input() radio: 'dot' | 'none' = 'dot';
  @Input() animation: 'none' | 'shadow' = 'none';

  hovered: string | null = null;
  focusedIndex = -1;
  @Output() selectedValueChange = new EventEmitter<string>();

  private onChange: (value: string) => void = () => {}; // eslint-disable-line @typescript-eslint/no-empty-function
  private onTouched: () => void = () => {}; // eslint-disable-line @typescript-eslint/no-empty-function
  isDisabled = false;

  ngOnInit() {
    this.initializeFocus();
  }

  ngAfterViewInit() {
    // Ensure focus is properly set after view initialization
    if (this.focusedIndex === -1) {
      this.initializeFocus();
    }
  }

  private initializeFocus() {
    // Set focus to selected option, or first enabled option if none selected
    const selectedIndex = this.options.findIndex(
      (option) => option.value === this.selectedValue
    );

    if (selectedIndex >= 0 && !this.options[selectedIndex].disabled) {
      this.focusedIndex = selectedIndex;
    } else {
      // Find first enabled option
      const firstEnabledIndex = this.options.findIndex(
        (option) => !option.disabled
      );
      this.focusedIndex = firstEnabledIndex >= 0 ? firstEnabledIndex : 0;
    }
  }

  onGroupFocus() {
    // When the group receives focus, ensure we have a focused option
    if (this.focusedIndex === -1) {
      this.initializeFocus();
    }
  }

  onGroupBlur() {
    // Optional: You might want to reset focus index when leaving the group
    // this.focusedIndex = -1;
  }

  onSelectionChange(value: string) {
    if (this.isDisabled) return;
    this.selectedValue = value;
    this.selectedValueChange.emit(this.selectedValue);
    this.onChange(this.selectedValue);
    this.onTouched();
  }

  onKeyDown(event: KeyboardEvent) {
    if (this.isDisabled) return;

    const enabledOptions = this.options
      .map((option, index) => ({ option, index }))
      .filter((item) => !item.option.disabled);

    if (enabledOptions.length === 0) return;

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        event.preventDefault();
        this.focusNextOption(enabledOptions);
        break;
      case 'ArrowUp':
      case 'ArrowLeft':
        event.preventDefault();
        this.focusPreviousOption(enabledOptions);
        break;
      case ' ':
      case 'Enter':
        event.preventDefault();
        if (this.focusedIndex >= 0 && this.focusedIndex < this.options.length) {
          const option = this.options[this.focusedIndex];
          if (!option.disabled) {
            this.onSelectionChange(option.value);
          }
        }
        break;
    }
  }

  private focusNextOption(
    enabledOptions: { option: RadioOption; index: number }[]
  ) {
    const currentEnabledIndex = enabledOptions.findIndex(
      (item) => item.index === this.focusedIndex
    );
    const nextIndex = (currentEnabledIndex + 1) % enabledOptions.length;
    this.focusedIndex = enabledOptions[nextIndex].index;
  }

  private focusPreviousOption(
    enabledOptions: { option: RadioOption; index: number }[]
  ) {
    const currentEnabledIndex = enabledOptions.findIndex(
      (item) => item.index === this.focusedIndex
    );
    const prevIndex =
      currentEnabledIndex <= 0
        ? enabledOptions.length - 1
        : currentEnabledIndex - 1;
    this.focusedIndex = enabledOptions[prevIndex].index;
  }

  // ControlValueAccessor methods
  writeValue(value: string): void {
    this.selectedValue = value;
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
  }

  /**
   * Get computed styles for custom color effects including glow
   */
  get customStyles(): Record<string, string> {
    const styles: Record<string, string> = {};
    // Set custom glow color based on the color input
    if (this.color) {
      styles['--radio-custom-glow-color'] = this.color;
    }
    return styles;
  }
}
