<div class="radio-group" [attr.data-orientation]="orientation" role="radiogroup"
  [attr.aria-label]="'Radio button group with ' + options.length + ' options'" (keydown)="onKeyDown($event)"
  (focus)="onGroupFocus()" (blur)="onGroupBlur()" tabindex="0">
  <label class="radio-wrapper" *ngFor="let option of options; let i = index" [attr.data-size]="size"
    [ngStyle]="customStyles" [ngClass]="{
      disabled: option.disabled,
      focused: focusedIndex === i
    }" [attr.id]="'radio-option-' + i + '-' + name">
    <input type="radio" class="radio-input" [name]="name" [value]="option.value"
      [checked]="selectedValue === option.value" (change)="onSelectionChange(option.value)" [disabled]="option.disabled"
      [attr.aria-describedby]="'radio-label-' + i + '-' + name" tabindex="-1" />
    <span class="custom-radio" role="radio" [attr.aria-checked]="selectedValue === option.value"
      [attr.aria-disabled]="option.disabled" [attr.aria-labelledby]="'radio-label-' + i + '-' + name"
      [class.animated-shadow]="
        animation === 'shadow' && selectedValue === option.value
      " [class.disabled]="option.disabled" [class.focused]="focusedIndex === i">
      <span class="radio-dot" *ngIf="selectedValue === option.value && radio !== 'none'" [ngStyle]="{
          backgroundColor: option.disabled
            ? 'var(--radio-dot-background-disabled)'
            : color || 'var(--radio-dot-background)'
        }" aria-hidden="true"></span>
    </span>
    <span class="radio-label" [attr.id]="'radio-label-' + i + '-' + name" [ngStyle]="
        labelColor
          ? { color: labelColor }
          : null
      " [class.disabled]="option.disabled">
      {{ option.label }}
    </span>
  </label>
</div>