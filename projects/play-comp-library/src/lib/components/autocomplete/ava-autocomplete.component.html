<div class="ava-autocomplete" [class.ava-autocomplete--full-width]="fullWidth">
  <ava-textbox #textboxRef [label]="label" [placeholder]="placeholder" [error]="error" [helper]="helper"
    [disabled]="disabled" [readonly]="readonly" [required]="required" [fullWidth]="fullWidth" [value]="query"
    (textboxInput)="onInput($event)" (textboxFocus)="onFocus()" (textboxBlur)="onBlur()"
    (textboxChange)="onInput($event)" (keydown)="onKeydown($event)" [attr.aria-autocomplete]="'list'"
    [attr.aria-controls]="'autocomplete-list'" [attr.aria-activedescendant]="
      showDropdown ? 'autocomplete-option-' + highlightedIndex : null
    " [attr.aria-label]="ariaLabel" [attr.aria-labelledby]="ariaLabelledby" [attr.aria-describedby]="ariaDescribedby"
    autocomplete="off" (iconEndClick)="!startIcon && clearable && query ? onClear() : null">
    <ava-icon *ngIf="startIcon" slot="icon-start" [iconName]="startIcon" [iconColor]="startIconColor"
      [iconSize]="startIconSize"></ava-icon>
  </ava-textbox>

  <!-- Multi-select tags using ava-tag -->
  <div *ngIf="multi && selectedOptions.length" class="ava-autocomplete__chips">
    <ava-tag *ngFor="let opt of selectedOptions; trackBy: trackByValue" [label]="opt.label"
      [color]="tagColor ?? 'default'" [variant]="tagVariant ?? 'filled'" [size]="tagSize ?? 'sm'"
      [pill]="tagPill ?? false" [removable]="tagRemovable ?? true" [disabled]="tagDisabled ?? false" [icon]="tagIcon"
      [iconPosition]="tagIconPosition ?? 'start'" [avatar]="tagAvatar" [customStyle]="tagCustomStyle"
      [customClass]="tagCustomClass" [iconColor]="tagIconColor" (removed)="removeSelectedOption(opt)"></ava-tag>
  </div>

  <!-- Dropdown -->
  <div class="ava-autocomplete__dropdown" *ngIf="showDropdown" [attr.id]="'autocomplete-list'" role="listbox"
    [attr.aria-label]="label || placeholder">
    <div *ngIf="loading || loadingState" class="ava-autocomplete__loading">
      Loading...
    </div>
    <div *ngIf="!loading && !loadingState && !filteredOptions.length" class="ava-autocomplete__empty">
      {{ noResultsText }}
    </div>
    <ul *ngIf="filteredOptions.length" class="ava-autocomplete__options-ul">
      <li *ngFor="
          let option of filteredOptions;
          let i = index;
          trackBy: trackByValue
        " class="ava-autocomplete__option" [class.ava-autocomplete__option--highlighted]="i === highlightedIndex"
        [id]="'autocomplete-option-' + i" role="option" [attr.aria-selected]="i === highlightedIndex"
        (mousedown)="onOptionClick(option)">
        <ng-container *ngIf="optionTemplate; else defaultOption">
          <ng-container *ngTemplateOutlet="optionTemplate; context: { $implicit: option }"></ng-container>
        </ng-container>
        <ng-template #defaultOption>
          <span *ngIf="option.icon" class="ava-autocomplete__option-icon"><ava-icon
              [iconName]="option.icon"></ava-icon></span>
          <span class="ava-autocomplete__option-label">{{ option.label }}</span>
        </ng-template>
      </li>
    </ul>
  </div>
</div>