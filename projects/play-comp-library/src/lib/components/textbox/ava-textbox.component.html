<div [class]="wrapperClasses">
  <!-- Label -->
  <label
    *ngIf="label"
    [for]="inputId"
    class="ava-textbox__label"
    [class.ava-textbox__label--required]="required"
  >
    {{ label }}
    <span *ngIf="required" class="ava-textbox__required" aria-hidden="true"
      >*</span
    >
  </label>

  <!-- Input Container (only for non-OTP) -->
  <div class="ava-textbox__container" [ngStyle]="computedStyles">
    <!-- Phone variant country prefix (start position) -->
    <div
      *ngIf="hasKindLabel && labelPosition === 'start'"
      class="ava-label-prefix ava-textbox__label-prefix--start"
    >
      <span *ngIf="inputKindLabel" class="ava-textbox__label-name">{{
        inputKindLabel
      }}</span>
      <ava-icon
        *ngIf="isPhone || isCurrency"
        iconName="chevron-down"
        [iconSize]="labelIconSize"
        class="ava-textbox__label-arrow"
      >
      </ava-icon>
      <ava-icon
        *ngIf="isEmail"
        iconName="mail"
        [iconSize]="labelIconSize"
        class="ava-textbox__label-email"
      >
      </ava-icon>
      <ava-icon
        *ngIf="isPassword"
        iconName="eye-off"
        [iconSize]="labelIconSize"
        class="ava-textbox__label-password"
      >
      </ava-icon>
    </div>

    <!-- Prefix Slot -->
    <div class="ava-textbox__prefix" #prefixContainer>
      <ng-content select="[slot=prefix]"></ng-content>
    </div>

    <!-- Start Projected Icons (before input/textarea) -->
    <div
      class="ava-textbox__icons ava-textbox__icons--start"
      #iconStartContainer
      (click)="onIconStartClick($event)"
      (keydown)="onIconKeydown($event, 'start')"
      tabindex="0"
      role="button"
      [attr.aria-label]="'Icon action'"
    >
      <ng-content select="[slot=icon-start]"></ng-content>
    </div>

    <!-- Icon Separator (start) -->
    <div
      *ngIf="iconSeparator && hasProjectedStartIcon"
      class="ava-textbox__icon-separator ava-textbox__icon-separator--start"
    ></div>

    <!-- Regular Input Field (masked only when mask provided) -->
    <ng-container *ngIf="mask; else plainInput">
      <input
        [id]="inputId"
        [name]="name"
        [type]="type"
        [placeholder]="placeholder"
        [value]="value"
        [disabled]="disabled"
        [readonly]="readonly"
        [required]="required"
        [attr.maxlength]="maxlength"
        [attr.minlength]="minlength"
        [autocomplete]="autocomplete"
        [class]="inputClasses"
        [attr.aria-invalid]="hasError"
        [attr.aria-describedby]="ariaDescribedBy || null"
        (input)="onInput($event)"
        (focus)="onFocus($event)"
        (blur)="onBlur($event)"
        (change)="onChange_($event)"
        [mask]="mask"
        [prefix]="maskPrefix ?? ''"
        [suffix]="maskSuffix ?? ''"
        [dropSpecialCharacters]="maskDropSpecialCharacters ?? null"
        [showMaskTyped]="maskShowMaskTyped ?? null"
        [thousandSeparator]="maskThousandSeparator ?? ''"
        [decimalMarker]="maskDecimalMarker ?? '.'"
        [patterns]="$any(maskPatterns)"
        [validation]="maskValidation ?? null"
        [allowNegativeNumbers]="maskAllowNegativeNumbers ?? null"
        [leadZeroDateTime]="maskLeadZeroDateTime ?? null"
      />
    </ng-container>
    <ng-template #plainInput>
      <input
        [id]="inputId"
        [name]="name"
        [type]="type"
        [placeholder]="placeholder"
        [value]="value"
        [disabled]="disabled"
        [readonly]="readonly"
        [required]="required"
        [attr.maxlength]="maxlength"
        [attr.minlength]="minlength"
        [autocomplete]="autocomplete"
        [class]="inputClasses"
        [attr.aria-invalid]="hasError"
        [attr.aria-describedby]="ariaDescribedBy || null"
        (input)="onInput($event)"
        (focus)="onFocus($event)"
        (blur)="onBlur($event)"
        (change)="onChange_($event)"
      />
    </ng-template>

    <!-- Icon Separator (end) -->
    <div
      *ngIf="iconSeparator && hasProjectedEndIcon"
      class="ava-textbox__icon-separator ava-textbox__icon-separator--end"
    ></div>

    <!-- End Projected Icons (before input/textarea) -->
    <div
      class="ava-textbox__icons ava-textbox__icons--end"
      #iconEndContainer
      (click)="onIconEndClick($event)"
      (keydown)="onIconKeydown($event, 'end')"
      tabindex="0"
      role="button"
      [attr.aria-label]="'Icon action'"
    >
      <ng-content select="[slot=icon-end]"></ng-content>
    </div>

    <!-- Suffix Slot -->
    <div class="ava-textbox__suffix" #suffixContainer>
      <ng-content select="[slot=suffix]"></ng-content>
    </div>

    <!-- Phone variant country prefix (end position) -->
    <div
      *ngIf="hasKindLabel && labelPosition === 'end'"
      class="ava-label-prefix ava-textbox__label-prefix ava-textbox__label-prefix--end"
      (click)="togglePasswordVisibility()"
      tabindex="0"
      role="button"
      (keydown.enter)="togglePasswordVisibility()"
      (keydown.space)="togglePasswordVisibility()"
    >
      <span class="ava-textbox__label-name">{{ inputKindLabel }} </span>
      <ava-icon
        *ngIf="isPhone || isCurrency"
        iconName="chevron-down"
        [iconSize]="labelIconSize"
        class="ava-textbox__label-arrow"
      >
      </ava-icon>
      <ava-icon
        *ngIf="isPassword"
        [iconName]="showPassword ? 'eye' : 'eye-off'"
        [iconSize]="labelIconSize"
        class="ava-textbox__label-password"
      >
      </ava-icon>
    </div>
  </div>

  <!-- Error Message -->
  <div
    *ngIf="hasError"
    [id]="errorId"
    class="ava-textbox__error"
    role="alert"
    aria-live="polite"
  >
    <ava-icon
      iconName="alert-circle"
      [iconSize]="16"
      class="ava-textbox__error-icon"
      [cursor]="false"
      [disabled]="false"
      [iconColor]="'red'"
    ></ava-icon>
    <span class="ava-textbox__error-text">{{ error }}</span>
  </div>

  <!-- Helper Message -->
  <div *ngIf="hasHelper" [id]="helperId" class="ava-textbox__helper">
    <ava-icon
      iconName="info"
      [iconSize]="14"
      class="ava-textbox__helper-icon"
      [cursor]="false"
      [disabled]="false"
    ></ava-icon>
    <span class="ava-textbox__helper-text">{{ helper }}</span>
  </div>
</div>
