import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AvaTextboxComponent } from './ava-textbox.component';

describe('AvaTextboxComponent', () => {
  let component: AvaTextboxComponent;
  let fixture: ComponentFixture<AvaTextboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AvaTextboxComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AvaTextboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('OTP functionality', () => {
    beforeEach(() => {
      component.otp = true;
      component.otpLength = 6;
      fixture.detectChanges();
    });

    it('should initialize OTP values array', () => {
      expect(component.otpValues).toEqual(['', '', '', '', '', '']);
    });

    it('should return true for isOtpVariant when otp is true', () => {
      expect(component.isOtpVariant).toBe(true);
    });

    it('should generate correct number of OTP boxes', () => {
      expect(component.otpBoxes).toEqual([0, 1, 2, 3, 4, 5]);
    });

    it('should handle OTP input correctly', () => {
      const mockEvent = {
        target: { value: '5' }
      } as any;

      component.onOtpInput(mockEvent, 2);

      expect(component.otpValues[2]).toBe('5');
      expect(component.value).toBe('  5   ');
    });

    it('should distribute value to OTP boxes on writeValue', () => {
      component.writeValue('123456');

      expect(component.otpValues).toEqual(['1', '2', '3', '4', '5', '6']);
      expect(component.value).toBe('123456');
    });

    it('should handle partial values correctly', () => {
      component.writeValue('123');

      expect(component.otpValues).toEqual(['1', '2', '3', '', '', '']);
      expect(component.value).toBe('123');
    });
  });
});
