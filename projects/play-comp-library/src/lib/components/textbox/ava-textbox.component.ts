import {
  ChangeDetectionStrategy,
  Component,
  Input,
  forwardRef,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  CUSTOM_ELEMENTS_SCHEMA,
  ViewChild,
  ElementRef,
  AfterViewInit,
  HostBinding,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
import { trigger, transition, style, animate } from '@angular/animations';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';

export type TextboxVariant =
  | 'default'
  | 'primary'
  | 'success'
  | 'error'
  | 'warning'
  | 'info';
export type TextboxSize = 'sm' | 'md' | 'lg' | 'xl' | 'xs';
export type IconPosition = 'start' | 'end';

// Text Input Specification Types (Spec-Compliant Only)
export type GlassVariant = 'glass-10' | 'glass-50'; // Surface 10 (recommended), Surface 50 (allowed)
export type HoverEffect = 'tint' | 'glow'; // Tint (recommended), Glow (allowed)
export type PressedEffect = 'solid'; // Solid (recommended and only allowed)
export type ProcessingEffect = 'shimmer'; // Text shimmer (alternative to default border pulse)
export type DecorativeEffect = 'glowBox' | 'borderFlow' | 'attention' | 'wave'; // Keep for future components
export type DisabledState = 'grey'; // Grey (recommended and only allowed)

// Legacy Types (for backward compatibility)
export type PersonalityTheme =
  | 'minimal'
  | 'professional'
  | 'modern'
  | 'vibrant';
export type MetaphorIntensity = 0 | 10 | 25 | 50 | 75 | 100;
export type InputKind = 'text' | 'phone' | 'currency';

@Component({
  selector: 'ava-textbox',
  standalone: true,
  imports: [CommonModule, IconComponent, NgxMaskDirective],
  templateUrl: './ava-textbox.component.html',
  styleUrl: './ava-textbox.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AvaTextboxComponent),
      multi: true,
    },
    provideNgxMask(),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  animations: [
    trigger('fadeIcon', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate(
          '180ms cubic-bezier(0.4,0,0.2,1)',
          style({ opacity: 1, transform: 'scale(1)' })
        ),
      ]),
      transition(':leave', [
        animate(
          '180ms cubic-bezier(0.4,0,0.2,1)',
          style({ opacity: 0, transform: 'scale(0.8)' })
        ),
      ]),
    ]),
  ],
})
export class AvaTextboxComponent
  implements ControlValueAccessor, AfterViewInit {
  // Host bindings - Apply CSS classes to component element
  @HostBinding('class') get hostClasses(): string {
    return this.wrapperClasses;
  }

  @Input() label = '';
  @Input() mapper = '';
  @Input() placeholder = '';
  @Input() variant: TextboxVariant = 'default';
  @Input() size: TextboxSize = 'md';
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() error = '';
  @Input() helper = '';
  @Input() icon = '';
  @Input() iconPosition: IconPosition = 'start';
  @Input() iconColor = 'var(--textbox-icon-color)';
  @Input() iconSeparator = false; // Add separator between icon and text area
  @Input() iconSpacing: 'compact' | 'normal' | 'relaxed' = 'normal'; // Icon spacing variant
  @Input() id = '';
  @Input() name = '';
  @Input() autocomplete = '';
  @Input() type = 'text';
  @Input() maxlength?: number;
  @Input() minlength?: number;
  @Input() required = false;
  @Input() fullWidth = false;
  @Input() style?: Record<string, string>;
  @Input() inputKind = 'text';
  @Input() inputKindLabel = '';

  // NGX-MASK inputs (kept optional; active only when mask provided)
  @Input() mask: string | null = null;
  @Input() maskPrefix?: string;
  @Input() maskSuffix?: string;
  @Input() maskDropSpecialCharacters?: boolean | string[];
  @Input() maskShowMaskTyped?: boolean;
  @Input() maskThousandSeparator?: string;
  @Input() maskDecimalMarker?: '.' | ',' | ['.', ','];
  @Input() maskPatterns?: Record<
    string,
    { pattern: RegExp; optional?: boolean; symbol?: string }
  >;
  @Input() maskValidation?: boolean;
  @Input() maskAllowNegativeNumbers?: boolean;
  @Input() maskLeadZeroDateTime?: boolean;

  // Phone variant specific inputs
  @Input() phone = false; // Enable phone functionality
  @Input() labelPosition: 'start' | 'end' = 'start'; // Position of country prefix

  // Value input with setter to ensure proper updates
  private _value = '';
  showPassword = false;
  @Input()
  set value(val: string) {
    if (this._value !== val) {
      this._value = val || '';

      this.cdr.markForCheck();
    }
  }
  get value(): string {
    return this._value;
  }

  // New Effects System Props - Text Input Specifications
  @Input() glassVariant: GlassVariant = 'glass-50'; // Default: Surface 10
  @Input() hoverEffect?: HoverEffect; // Default: 'tint' + 'glow' combination
  @Input() pressedEffect?: PressedEffect; // Default: 'solid'
  @Input() processing = false; // Processing state - triggers border pulse by default
  @Input() processingEffect?: ProcessingEffect; // Alternative: 'shimmer' for text animation
  @Input() decorativeEffect?: DecorativeEffect; // For ambient effects
  @Input() disabledState: DisabledState = 'grey'; // Default: Grey only
  @Input() customStyles?: Record<string, string>; // CSS custom properties override
  /**
   * If true, shows an animated gradient border for processing state
   */
  @Input() processingGradientBorder = false;
  /**
   * Colors for the animated processing gradient border (as array of CSS color strings)
   */
  @Input() processingGradientColors: string[] = [
    '#e91e63',
    '#fee140',
    '#ff9800',
    '#047857',
    '#ff9800',
    '#fee140',
    '#e91e63',
  ];

  // Legacy Props (for backward compatibility)
  @Input() personality?: PersonalityTheme; // DEPRECATED: Use new effect props instead
  @Input() glassIntensity?: MetaphorIntensity; // DEPRECATED: Use glassVariant instead
  @Input() lightIntensity?: MetaphorIntensity; // DEPRECATED: Use hoverEffect instead
  @Input() liquidIntensity?: MetaphorIntensity; // DEPRECATED: Use pressedEffect instead
  @Input() gradientIntensity?: MetaphorIntensity; // DEPRECATED
  @Input() enableMetaphors = true; // DEPRECATED: Effects are always enabled
  @Input() respectsGlobalPersonality = true; // DEPRECATED
  @Input() metaphor: string | string[] = ''; // DEPRECATED: Use new effect props

  @Output() textboxBlur = new EventEmitter<Event>();
  @Output() textboxFocus = new EventEmitter<Event>();
  @Output() textboxInput = new EventEmitter<Event>();
  @Output() textboxChange = new EventEmitter<Event>();
  @Output() iconStartClick = new EventEmitter<Event>();
  @Output() iconEndClick = new EventEmitter<Event>();

  @ViewChild('prefixContainer', { static: true }) prefixContainer!: ElementRef;
  @ViewChild('suffixContainer', { static: true }) suffixContainer!: ElementRef;
  @ViewChild('iconStartContainer', { static: true })
  iconStartContainer!: ElementRef;
  @ViewChild('iconEndContainer', { static: true })
  iconEndContainer!: ElementRef;

  isFocused = false;
  hasProjectedPrefix = false;
  hasProjectedSuffix = false;
  hasProjectedStartIcon = false;
  hasProjectedEndIcon = false;

  // OTP specific properties
  otpValues: string[] = [];
  currentOtpIndex = 0;

  private onChange: (value: string) => void = () => {
    /* noop */
  };
  private onTouched: () => void = () => {
    /* noop */
  };

  constructor(private cdr: ChangeDetectorRef) {

  }



  ngAfterViewInit(): void {
    if (this.inputKind === 'password') {
      this.type = 'password';
    }
    // Check for projected content in containers
    this.checkProjectedContent();
  }

  private checkProjectedContent(): void {
    // Use setTimeout to ensure content projection has completed
    setTimeout(() => {
      this.hasProjectedPrefix =
        this.prefixContainer?.nativeElement?.children?.length > 0;
      this.hasProjectedSuffix =
        this.suffixContainer?.nativeElement?.children?.length > 0;
      this.hasProjectedStartIcon =
        this.iconStartContainer?.nativeElement?.children?.length > 0;
      this.hasProjectedEndIcon =
        this.iconEndContainer?.nativeElement?.children?.length > 0;
      this.cdr.markForCheck();
    });
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this._value = value || '';
    this.cdr.markForCheck();
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.cdr.markForCheck();
  }

  // Event handlers
  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this._value = target.value;
    this.onChange(this._value);
    this.textboxInput.emit(event);
  }

  onFocus(event: Event): void {
    this.isFocused = true;
    this.textboxFocus.emit(event);
  }

  onBlur(event: Event): void {
    this.isFocused = false;
    this.onTouched();
    this.textboxBlur.emit(event);
  }

  onChange_(event: Event): void {
    this.textboxChange.emit(event);
  }

  // Icon click handlers
  onIconStartClick(event: Event): void {
    if (this.disabled || this.readonly) return;
    event.preventDefault();
    event.stopPropagation();
    this.iconStartClick.emit(event);
  }

  onIconEndClick(event: Event): void {
    if (this.disabled || this.readonly) return;
    event.preventDefault();
    event.stopPropagation();
    this.iconEndClick.emit(event);
  }

  // Keyboard accessibility for icons
  onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (position === 'start') this.onIconStartClick(event);
      else this.onIconEndClick(event);
    }
  }

  // Computed properties
  get hasError(): boolean {
    return !!this.error;
  }

  get hasHelper(): boolean {
    return !!this.helper && !this.hasError;
  }

  get hasIcon(): boolean {
    return !!this.icon;
  }

  get isPhone(): boolean {
    return this.inputKind === 'phone';
  }

  get isCurrency(): boolean {
    return this.inputKind === 'currency';
  }

  get isEmail(): boolean {
    return this.inputKind === 'email';
  }

  get isPassword(): boolean {
    return this.inputKind === 'password';
  }

  get hasKindLabel(): boolean {
    return ['phone', 'currency', 'email', 'password'].includes(this.inputKind);
  }

  get inputId(): string {
    return this.id || `ava-textbox-${Math.random().toString(36).substr(2, 9)}`;
  }

  get errorId(): string {
    return `${this.inputId}-error`;
  }

  get helperId(): string {
    return `${this.inputId}-helper`;
  }

  get ariaDescribedBy(): string {
    const ids: string[] = [];
    if (this.hasError) ids.push(this.errorId);
    if (this.hasHelper) ids.push(this.helperId);
    return ids.join(' ') || '';
  }

  /**
   * Get effective hover effect (with defaults) - Text Input Spec: Tint (recommended)
   */
  get effectiveHoverEffect(): HoverEffect {
    return this.hoverEffect || 'tint'; // Default to tint (recommended)
  }

  /**
   * Get effective pressed effect (with defaults) - Text Input Spec: Solid (only allowed)
   */
  get effectivePressedEffect(): PressedEffect {
    return this.pressedEffect || 'solid'; // Default to solid (only allowed)
  }

  /**
   * Get effective processing effect - Text Input Spec: Border pulse (default) or Shimmer (alternative)
   */
  get effectiveProcessingEffect():
    | ProcessingEffect
    | 'border-pulse'
    | undefined {
    if (!this.processing) return undefined;
    return this.processingEffect || 'border-pulse'; // Default to border pulse when processing=true
  }

  /**
   * Generate effect classes based on Text Input Specifications
   */
  get effectClasses(): string[] {
    const classes: string[] = [];

    // Glass variant - Text Input Spec: glass-10 (recommended), glass-50 (allowed)
    classes.push(`ava-textbox--${this.glassVariant}`);

    // Hover effect - Text Input Spec: tint (recommended), glow (allowed)
    classes.push(`ava-textbox--hover-${this.effectiveHoverEffect}`);

    // Pressed effect - Text Input Spec: solid (only allowed)
    classes.push(`ava-textbox--pressed-${this.effectivePressedEffect}`);

    // Processing effect - Text Input Spec: border-pulse (default), shimmer (alternative)
    const processingEffect = this.effectiveProcessingEffect;
    if (processingEffect) {
      classes.push(`ava-textbox--processing-${processingEffect}`);
    }

    // Processing gradient border effect
    if (this.processingGradientBorder) {
      classes.push('ava-textbox--processing-gradient-border');
    }

    // Disabled state - Text Input Spec: grey (only allowed)
    if (this.disabled) {
      classes.push(`ava-textbox--disabled-${this.disabledState}`);
    }

    return classes;
  }

  /**
   * Generate legacy metaphor classes for backward compatibility
   */
  get legacyMetaphorClasses(): string[] {
    const classes: string[] = [];

    // Legacy personality support
    if (this.personality) {
      classes.push(`ava-textbox--personality-${this.personality}`);
    }

    // Legacy intensity support
    if (this.glassIntensity !== undefined) {
      classes.push(`ava-textbox--glass-${this.glassIntensity}`);
    }
    if (this.lightIntensity !== undefined) {
      classes.push(`ava-textbox--light-${this.lightIntensity}`);
    }
    if (this.liquidIntensity !== undefined) {
      classes.push(`ava-textbox--liquid-${this.liquidIntensity}`);
    }
    if (this.gradientIntensity !== undefined) {
      classes.push(`ava-textbox--gradient-${this.gradientIntensity}`);
    }

    // Legacy metaphor string support
    if (this.metaphor) {
      if (Array.isArray(this.metaphor)) {
        classes.push(...this.metaphor.map((m) => `ava-textbox--${m}`));
      } else {
        classes.push(`ava-textbox--${this.metaphor}`);
      }
    }

    return classes;
  }

  /**
   * Get computed CSS custom properties for style overrides
   */
  get computedStyles(): Record<string, string> {
    const styles: Record<string, string> = {};

    // Apply any custom style overrides
    if (this.customStyles) {
      Object.assign(styles, this.customStyles);
    }

    // Apply legacy style prop
    if (this.style) {
      Object.assign(styles, this.style);
    }

    // Add processing gradient colors as a CSS variable if set
    if (
      this.processingGradientColors &&
      this.processingGradientColors.length > 0
    ) {
      styles['--processing-gradient-colors'] =
        this.processingGradientColors.join(', ');
    }

    return styles;
  }

  get inputClasses(): string {
    const classes = ['ava-textbox__input'];

    if (this.size) classes.push(`ava-textbox__input--${this.size}`);
    if (this.variant) classes.push(`ava-textbox__input--${this.variant}`);
    if (this.hasError) classes.push('ava-textbox__input--error');
    if (this.disabled) classes.push('ava-textbox__input--disabled');
    // if (this.readonly) classes.push('ava-textbox__input--readonly');
    if (this.isFocused) classes.push('ava-textbox__input--focused');
    if (this.fullWidth) classes.push('ava-textbox__input--full-width');

    // Add classes based on projected content
    if (this.hasProjectedStartIcon)
      classes.push('ava-textbox__input--icon-start');
    if (this.hasProjectedEndIcon) classes.push('ava-textbox__input--icon-end');
    if (this.hasProjectedPrefix)
      classes.push('ava-textbox__input--with-prefix');
    if (this.hasProjectedSuffix)
      classes.push('ava-textbox__input--with-suffix');
    if (this.inputKind && this.value.length > 0)
      classes.push(`ava-textbox__input--${this.inputKind}`);
    return classes.join(' ');
  }

  get wrapperClasses(): string {
    const classes = ['ava-textbox'];

    if (this.size) classes.push(`ava-textbox--${this.size}`);
    if (this.variant) classes.push(`ava-textbox--${this.variant}`);
    if (this.hasError) classes.push('ava-textbox--error');
    if (this.disabled) classes.push('ava-textbox--disabled');
    if (this.readonly) classes.push('ava-textbox--readonly');
    if (this.isFocused) classes.push('ava-textbox--focused');
    if (this.fullWidth) classes.push('ava-textbox--full-width');

    // Add icon spacing and separator classes
    if (this.iconSeparator) classes.push('ava-textbox--icon-separator');
    if (this.iconSpacing !== 'normal')
      classes.push(`ava-textbox--icon-spacing-${this.iconSpacing}`);
    if (this.hasKindLabel) {
      classes.push(`ava-textbox--label-${this.labelPosition}`);
    }

    // Add new effect classes
    const effectClasses = this.effectClasses;
    const legacyClasses = this.legacyMetaphorClasses;

    return [...classes, ...effectClasses, ...legacyClasses].join(' ');
  }

  get labelIconSize(): number {
    return (
      ({ xl: 24, lg: 24, md: 20, xs: 16 } as Record<string, number>)[
      this.size
      ] ?? 16
    );
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
    if (this.showPassword) {
      this.type = 'text';
    } else {
      this.type = 'password';
    }
  }
}
