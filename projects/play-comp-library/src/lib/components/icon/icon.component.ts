import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy, Component, Input, Output,
  EventEmitter, ViewEncapsulation
} from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';
@Component({
  selector: 'ava-icon',
  imports: [CommonModule, LucideAngularModule],
  templateUrl: './icon.component.html',
  styleUrl: './icon.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    '[style.height.px]': 'iconSize',
    '[style.width.px]': 'iconSize',
    'style': 'display: inline-flex; align-items: center; justify-content: center; vertical-align: middle;'
  }
})
export class IconComponent {
  @Input() iconName = '';
  @Input() color = '';
  @Input() disabled = false;
  @Input() iconColor = '#a1a1a1';
  @Input() iconSize: number | string = 24;
  @Input() cursor = false;
  @Output() userClick = new EventEmitter<Event>();


  get computedColor(): string {
    if (this.disabled)
      return 'var(--button-icon-color-disabled)';
    return this.iconColor;
  }
  handleClick(event: Event): void {
    if (this.disabled || !this.cursor) {
      event.preventDefault();
      return;
    }
    this.userClick.emit(event);
  }
}



