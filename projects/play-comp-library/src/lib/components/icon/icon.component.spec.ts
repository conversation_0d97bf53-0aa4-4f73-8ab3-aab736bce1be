import { ComponentFixture, TestBed } from '@angular/core/testing';
import { IconComponent } from './icon.component';
import { By } from '@angular/platform-browser';

describe('IconComponent', () => {
    let component: IconComponent;
    let fixture: ComponentFixture<IconComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [IconComponent]
        }).compileComponents();

        fixture = TestBed.createComponent(IconComponent);
        component = fixture.componentInstance;
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    describe('computedColor', () => {
        it('should return disabled color when disabled is true', () => {
            component.disabled = true;
            component.iconColor = '#ff0000';
            expect(component.computedColor).toBe('var(--button-icon-color-disabled)');
        });

        it('should return iconColor when not disabled', () => {
            component.disabled = false;
            component.iconColor = '#123456';
            expect(component.computedColor).toBe('#123456');
        });
    });

    describe('handleClick()', () => {
        it('should emit event if not disabled and cursor is true', () => {
            component.disabled = false;
            component.cursor = true;
            spyOn(component.userClick, 'emit');

            const mockEvent = new Event('click');
            component.handleClick(mockEvent);

            expect(component.userClick.emit).toHaveBeenCalledWith(mockEvent);
        });

        it('should not emit event if disabled is true', () => {
            component.disabled = true;
            component.cursor = true;
            spyOn(component.userClick, 'emit');
            const event = new MouseEvent('click');
            spyOn(event, 'preventDefault');

            component.handleClick(event);
            expect(event.preventDefault).toHaveBeenCalled();
            expect(component.userClick.emit).not.toHaveBeenCalled();
        });

        it('should not emit event if cursor is false', () => {
            component.disabled = false;
            component.cursor = false;
            spyOn(component.userClick, 'emit');
            const event = new MouseEvent('click');
            spyOn(event, 'preventDefault');

            component.handleClick(event);
            expect(event.preventDefault).toHaveBeenCalled();
            expect(component.userClick.emit).not.toHaveBeenCalled();
        });
    });
});
