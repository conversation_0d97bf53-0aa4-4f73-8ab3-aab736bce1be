<div
  [class]="badgeClasses"
  tabindex="0"
  (keydown.enter)="onKeyPress()"
  (keydown.space)="onKeyPress()"
>
  <!-- Dots variant - just a simple dot -->
  <ng-container *ngIf="isDots">
    <!-- Empty div for dot styling -->
  </ng-container>
 
  <!-- Default variant with content -->
  <ng-container *ngIf="hasContent">
    <!-- Display ava-icon if iconName is provided and no count -->
    <ng-container *ngIf="iconName && !count">
      <ava-icon
        class="badge__icon"
        [iconName]="iconName"
        [iconColor]="iconColor!"
        [iconSize]="iconSize!"
      >
      </ava-icon>
    </ng-container>
    <!-- Display count if provided -->
    <span *ngIf="count" class="badge__count">{{ displayCount }}</span>
  </ng-container>
</div>
 
 