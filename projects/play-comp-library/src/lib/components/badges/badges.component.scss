

 
:host {
    display: inline-block;
  }
 
  .badge {
    display: flex;
    flex-direction: column;
    justify-content: var(--badge-justify-content);
    align-items: var(--badge-align-items);
    gap: 10px;
    flex-shrink: 0;
    font-weight: var(--badge-weight);
    font-family:var(--badge-font-family);
    box-sizing: border-box;
 
    // Size variants - default circular dimensions
    &--large {
      width: var(--badge-size-lg-min-width);
      height: var(--badge-size-lg-height);
      padding: var(--badge-size-sm-padding);
      font-size: 14px;
      line-height: 1;
    }
 
    &--medium {
      width: var(--badge-size-md-min-width);
      height: var(--badge-size-md-height);
      padding: var(--badge-size-sm-padding);
      font-size: 12px;
      line-height: 1;
    }
 
    &--small {
      width: var(--badge-size-sm-min-width);
      height: var(--badge-size-sm-height);
      padding: var(--badge-size-sm-padding);
      font-size: 10px;
      line-height: 1;
    }
   
    &--xsmall {
       width: var(--badge-size-xs-min-width);
      height: var(--badge-size-xs-height);
      padding:var(--badge-size-sm-padding);
      font-size: 10px;
      line-height: 1;
    }
    // State variants
    &--high-priority {
      background: var(--badge-error-background);
      color: var(--badge-error-text);
    }
 
    &--medium-priority {
      background: var(--badge-warning-background);
      color: var(--badge-warning-text);
    }
 
    &--low-priority {
      background: var(--badge-success-background);
      color: var(--badge-success-text);
    }
 
    &--neutral {
      background: var(--badge-default-background);
      color: var(--badge-default-text);
    }
 
    &--information {
      background: var(--badge-info-background);
      color: var(--badge-info-text);
    }
 
    // // Variant styles
    // &--default {
    //   border-radius: 50%;
    // }
 
    &--dots {
      border-radius: 50%;
      padding: 0;
 
      &.badge--large {
        width: 10px;
        height: 10px;
      }
 
      &.badge--medium {
        width: 8px;
        height: 8px;
      }
 
      &.badge--small {
        width: 6px;
        height: 6px;
      }
    }
  }
 
  .badge__count {
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
  }
 
  // Icon styling for perfect centering within badge
  .badge__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
  }
 
 
  .badge--expanded {
    width: auto !important;
 
    &.badge--large {
      min-width: var(--badge-size-lg-min-width);
      padding-left: 6px;
      padding-right: 6px;
    }
 
    &.badge--medium {
      min-width: var(--badge-size-md-min-width);
      padding-left: 6px;
      padding-right: 6px;
    }
 
    &.badge--small {
      min-width: var(--badge-size-sm-min-width);
      padding-left: 6px;
      padding-right: 6px;
    }
 
    &.badge--xsmall {
      min-width: var(--badge-size-xs-min-width);
      padding-left: 4px;
      padding-right: 4px;
    }
  }
 
 
 
 