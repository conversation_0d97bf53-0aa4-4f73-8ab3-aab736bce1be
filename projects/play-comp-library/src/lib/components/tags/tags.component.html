<ng-container>
  <span *ngIf="type==='tag'" [ngClass]="[
      'ava-tag',
      'ava-tag--' + color,
      'ava-tag--' + variant,
      'ava-tag--' + size,
      pill ? 'ava-tag--pill' : '',
      disabled ? 'ava-tag--disabled' : '',
      customClass || '',
      clickable ? 'ava-tag--clickable' : ''
    ]" [ngStyle]="customStyle" [attr.tabindex]="clickable ? 0 : null" [attr.role]="clickable ? 'button' : null"
    [attr.aria-disabled]="disabled ? 'true' : null" (click)="onClick()" (keyup.enter)="onClick()"
    (mouseenter)="onMouseEnter()" (mouseleave)="onMouseLeave()">
    <ng-container *ngIf="shouldShowAvatar">
      <ava-avatars
        *ngIf="avatar && avatar.startsWith('http')"
        [size]="getAvatarSize()"
        shape="pill"
        [imageUrl]="avatar"
        class="ava-tag__avatar">
      </ava-avatars>
      <span class="ava-tag__avatar ava-tag__avatar--initials" *ngIf="avatar && !avatar.startsWith('http')">
        {{ avatar }}
      </span>
    </ng-container>
    <ng-container *ngIf="icon && iconPosition === 'start'">
      <ava-icon
        [iconName]="icon || ''"
        [iconSize]="getIconSize()"
        [iconColor]="getIconColor('start')"
        [cursor]="false"
        [disabled]="disabled"
        class="ava-tag__icon ava-tag__icon--start">
      </ava-icon>
    </ng-container>
    <span class="ava-tag__label">{{ label }}</span>
    <ng-container *ngIf="icon && iconPosition === 'end' && !removable">
      <ava-icon
        [iconName]="icon || ''"
        [iconSize]="getIconSize()"
        [iconColor]="getIconColor('end')"
        [cursor]="false"
        [disabled]="disabled"
        class="ava-tag__icon ava-tag__icon--end">
      </ava-icon>
    </ng-container>
    <button *ngIf="removable" type="button" class="ava-tag__remove-btn" [disabled]="disabled" tabindex="0"
      aria-label="Remove tag" (click)="onRemove($event)" (keyup.enter)="onRemove($event)">
      <ava-icon
        iconName="x"
        [iconSize]="getIconSize()"
        [iconColor]="getIconColor('end')"
        [cursor]="true"
        [disabled]="disabled"
        class="ava-tag__remove">
      </ava-icon>
    </button>
  </span>

  <span *ngIf="type==='badge'" [ngClass]="[
      'ava-status-badge',
      'ava-status-badge--' + color,
      'ava-status-badge--' + size,
      pill ? 'ava-status-badge--pill' : '',
      customClass || '',
    ]" [ngStyle]="customStyle" role="status">
    {{ label }}</span>
</ng-container>