// Tag base styles
.ava-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--tags-icon-gap);
  /* Use gap instead of margin for spacing */
  background: var(--tags-filled-background);
  color: var(--tags-filled-text);
  border: var(--tags-filled-border);
  transition: box-shadow 0.18s, outline 0.18s, background 0.18s, color 0.18s, border 0.18s, transform 0.18s ease;
  cursor: default;
  outline: none;
  min-width: 0;
  max-width: 100%;
  white-space: nowrap;
  user-select: none;
  position: relative;
  box-shadow: none;
  transform: scale(1);

  &.ava-tag--pill {
    border-radius: var(--tags-pill-border-radius) !important;
  }

  &.ava-tag--disabled {
    cursor: var(--tags-disabled-cursor);
    background: var(--tags-disabled-background) !important;
    color: var(--tags-disabled-text) !important;
    border: var(--tags-disabled-border) !important;
    pointer-events: none;

    /* For filled variant: show background and border */
    &:not(.ava-tag--outlined) {
      background: var(--tags-disabled-background) !important;
      border: var(--tags-disabled-border) !important;
    }
  }

  &.ava-tag--clickable:not(.ava-tag--disabled) {
    cursor: pointer;
  }



  &.ava-tag--xl {
    font-family: var(--tags-xl-font-family);
    font-size: var(--tags-xl-font-size);
    font-weight: var(--tags-xl-font-weight);
    line-height: var(--tags-xl-line-height);
    padding: var(--tags-xl-padding);
    border-radius: var(--tags-xl-border-radius);
    gap: var(--tags-xl-left-icon-gap);
    height: 32px;

    .ava-tag__icon--end,
    .ava-tag__remove-btn {
      margin-left: calc(var(--tags-xl-right-icon-gap) - var(--tags-xl-left-icon-gap));
    }
  }

  &.ava-tag--lg {
    font-family: var(--tags-lg-font-family);
    font-size: var(--tags-lg-font-size);
    font-weight: var(--tags-lg-font-weight);
    line-height: var(--tags-lg-line-height);
    padding: var(--tags-lg-padding);
    border-radius: var(--tags-lg-border-radius);
    gap: var(--tags-lg-left-icon-gap);
    height: 28px;

    .ava-tag__icon--end,
    .ava-tag__remove-btn {
      margin-left: calc(var(--tags-lg-right-icon-gap) - var(--tags-lg-left-icon-gap));
    }
  }

  &.ava-tag--md {
    font-family: var(--tags-md-font-family);
    font-size: var(--tags-md-font-size);
    font-weight: var(--tags-md-font-weight);
    line-height: var(--tags-md-line-height);
    padding: var(--tags-md-padding);
    border-radius: var(--tags-md-border-radius);
    gap: var(--tags-md-left-icon-gap);
    height: 24px;

    .ava-tag__icon--end,
    .ava-tag__remove-btn {
      margin-left: calc(var(--tags-md-right-icon-gap) - var(--tags-md-left-icon-gap));
    }
  }

  &.ava-tag--sm {
    font-family: var(--tags-sm-font-family);
    font-size: var(--tags-sm-font-size);
    font-weight: var(--tags-sm-font-weight);
    line-height: var(--tags-sm-line-height);
    padding: var(--tags-sm-padding);
    border-radius: var(--tags-sm-border-radius);
    gap: var(--tags-sm-left-icon-gap);
    height: 20px;

    .ava-tag__icon--end,
    .ava-tag__remove-btn {
      margin-left: calc(var(--tags-sm-right-icon-gap) - var(--tags-sm-left-icon-gap));
    }
  }

  &.ava-tag--xs {
    font-family: var(--tags-xs-font-family);
    font-size: var(--tags-xs-font-size);
    font-weight: var(--tags-xs-font-weight);
    line-height: var(--tags-xs-line-height);
    padding: var(--tags-xs-padding);
    border-radius: var(--tags-xs-border-radius);
    gap: var(--tags-xs-left-icon-gap);
    height: 16px;

    .ava-tag__icon--end,
    .ava-tag__remove-btn {
      margin-left: calc(var(--tags-xs-right-icon-gap) - var(--tags-xs-left-icon-gap));
    }
  }



  &.ava-tag--outlined {
    background: transparent !important;
    color: var(--tags-outlined-text);
    border: var(--tags-outlined-border);

    &.ava-tag--disabled {
      background: transparent !important;
      /* Keep transparent background for disabled outline */
      color: var(--tags-disabled-text) !important;
      border: var(--tags-disabled-border) !important;
    }

    &.ava-tag--primary {
      color: var(--tags-outlined-primary-text);
      border: var(--tags-outlined-primary-border);
    }

    &.ava-tag--success {
      color: var(--tags-outlined-success-text);
      border: var(--tags-outlined-success-border);
    }

    &.ava-tag--warning {
      color: var(--tags-outlined-warning-text);
      border: var(--tags-outlined-warning-border);
    }

    &.ava-tag--error {
      color: var(--tags-outlined-error-text);
      border: var(--tags-outlined-error-border);
    }

    &.ava-tag--info {
      color: var(--tags-outlined-info-text);
      border: var(--tags-outlined-info-border);
    }

    &.ava-tag--custom {
      background: var(--tag-custom-bg) !important;
      color: var(--tag-custom-color);
      border: var(--tag-custom-border);
    }
  }

  &.ava-tag--primary {
    background: var(--tags-filled-primary-background);
    color: var(--tags-filled-primary-text);
    border: var(--tags-filled-primary-border);
  }

  &.ava-tag--success {
    background: var(--tags-filled-success-background);
    color: var(--tags-filled-success-text);
    border: var(--tags-filled-success-border);
  }

  &.ava-tag--warning {
    background: var(--tags-filled-warning-background);
    color: var(--tags-filled-warning-text);
    border: var(--tags-filled-warning-border);
  }

  &.ava-tag--error {
    background: var(--tags-filled-error-background);
    color: var(--tags-filled-error-text);
    border: var(--tags-filled-error-border);
  }

  &.ava-tag--info {
    background: var(--tags-filled-info-background);
    color: var(--tags-filled-info-text);
    border: var(--tags-filled-info-border);
  }

  &.ava-tag--custom {
    background: var(--tag-custom-bg);
    color: var(--tag-custom-color);
    border: var(--tag-custom-border);
  }
}

.ava-tag__icon {
  display: inline-flex;
  align-items: center;
  transition: color 0.18s;
  margin: 0;
}



.ava-tag__avatar {
  width: var(--tags-avatar-size);
  height: var(--tags-avatar-size);
  border-radius: 50%;
  background: var(--tags-avatar-bg);
  color: var(--tags-avatar-color);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
  }
}

.ava-tag__avatar--initials {
  background: var(--tags-avatar-initials-bg);
  color: var(--tags-avatar-initials-color);
  font-size: var(--tags-avatar-font-size);
  font-weight: 600;
}

.ava-tag--disabled .ava-tag__avatar {
  opacity: 1;
  filter: grayscale(100%);

  img {
    opacity: 1;
    filter: grayscale(100%);
  }
}

.ava-tag__label {
  display: inline-block;
  vertical-align: middle;
  max-width: 12em;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.01em;
  margin: 0;
}

.ava-tag__remove-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 50%;
  transition: color 0.18s;
  color: var(--tags-removable-button-text);

  &:hover:not(:disabled),
  &:active:not(:disabled) {
    background: none;
    color: var(--tags-removable-button-active-text);
  }

  &:focus-visible {
    outline: var(--tags-focus-border);
    outline-offset: 2px;
  }

  &:disabled {
    cursor: not-allowed;
    color: var(--tags-remove-button-disabled-color);
  }
}

.ava-tag__remove {
  pointer-events: none;
}



.ava-status-badge {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-family: var(--status-badge-font-family);
  font-style: normal;
  line-height: calc(var(--status-badge-line-height) * 1rem);
  border-radius: var(--status-badge-border-radius);

  &--xs {
    height: var(--status-badge-xs-height);
    padding: var(--status-badge-xs-padding);
    font-size: var(--status-badge-xs-font-size);
    letter-spacing: 0.02px;
  }

  &--sm {
    height: var(--status-badge-sm-height);
    padding: var(--status-badge-sm-padding);
    font-size: var(--status-badge-sm-font-size);
    letter-spacing: 0.02px;
  }

  &--md {
    height: var(--status-badge-md-height);
    padding: var(--status-badge-md-padding);
    font-size: var(--status-badge-md-font-size);
    letter-spacing: 0.02px;
  }

  &--lg {
    height: var(--status-badge-lg-height);
    padding: var(--status-badge-lg-padding);
    font-size: var(--status-badge-lg-font-size);
    letter-spacing: 0.028px;
  }

  &--xl {
    height: var(--status-badge-xl-height);
    padding: var(--status-badge-xl-padding);
    font-size: var(--status-badge-xl-font-size);
    letter-spacing: 0.028px;
  }

  &--error {
    background: var(--status-badge-error-background);
    color: var(--status-badge-error-text);
  }

  &--success {
    background: var(--status-badge-success-background);
    color: var(--status-badge-success-text);
  }

  &--warning {
    background: var(--status-badge-warning-background);
    color: var(--status-badge-warning-text);
  }

  &--info {
    background: var(--status-badge-info-background);
    color: var(--status-badge-info-text);
  }

  &--pill {
    border-radius: var(--status-badge-pill-border-radius);
  }
}