import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AvaTagComponent } from './tags.component';
import { By } from '@angular/platform-browser';
import { IconComponent } from '../icon/icon.component';

describe('AvaTagComponent', () => {
  let component: AvaTagComponent;
  let fixture: ComponentFixture<AvaTagComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AvaTagComponent, IconComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(AvaTagComponent);
    component = fixture.componentInstance;
    component.label = 'Test Tag';
    fixture.detectChanges();
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should apply input class combinations correctly', () => {
    component.color = 'success';
    component.variant = 'outlined';
    component.size = 'lg';
    component.pill = true;
    component.customClass = 'custom-class';
    fixture.detectChanges();

    const tagEl = fixture.debugElement.query(By.css('.ava-tag'));
    const classList = tagEl.nativeElement.classList;

    expect(classList).toContain('ava-tag--success');
    expect(classList).toContain('ava-tag--outlined');
    expect(classList).toContain('ava-tag--lg');
    expect(classList).toContain('ava-tag--pill');
    expect(classList).toContain('custom-class');
  });

  it('should show image avatar if URL is provided', () => {
    component.avatar = 'http://example.com/avatar.png';
    fixture.detectChanges();

    const avatarImg = fixture.debugElement.query(By.css('img'));
    expect(avatarImg).toBeTruthy();
    expect(avatarImg.nativeElement.src).toContain('http://example.com/avatar.png');
  });

  it('should show initials if avatar is not a URL', () => {
    component.avatar = 'SA';
    fixture.detectChanges();

    const avatarInitials = fixture.debugElement.query(By.css('.ava-tag__avatar--initials'));
    expect(avatarInitials.nativeElement.textContent).toContain('SA');
  });

  it('should emit clicked event if clickable is true', () => {
    spyOn(component.clicked, 'emit');
    component.disabled = false;
    fixture.detectChanges();

    component.clicked.subscribe(() => { });
    fixture.detectChanges();

    const tagEl = fixture.debugElement.query(By.css('.ava-tag'));
    tagEl.nativeElement.click();

    expect(component.clicked.emit).toHaveBeenCalled();
  });

  it('should not emit clicked event if disabled', () => {
    spyOn(component.clicked, 'emit');
    component.disabled = true;
    fixture.detectChanges();

    const tagEl = fixture.debugElement.query(By.css('.ava-tag'));
    tagEl.nativeElement.click();

    expect(component.clicked.emit).not.toHaveBeenCalled();
  });

  it('should trigger onClick with enter key', () => {
    spyOn(component, 'onClick');
    const tagEl = fixture.debugElement.query(By.css('.ava-tag'));
    tagEl.triggerEventHandler('keyup.enter', {});
    expect(component.onClick).toHaveBeenCalled();
  });

  it('should apply accessibility attributes when clickable', () => {
    component.clicked.subscribe(() => { }); // make it clickable
    component.disabled = false;
    fixture.detectChanges();

    const tagEl = fixture.debugElement.query(By.css('.ava-tag'));
    expect(tagEl.attributes['role']).toBe('button');
    expect(tagEl.attributes['tabindex']).toBe('0');
  });

  it('should apply aria-disabled when disabled', () => {
    component.disabled = true;
    fixture.detectChanges();

    const tagEl = fixture.debugElement.query(By.css('.ava-tag'));
    expect(tagEl.attributes['aria-disabled']).toBe('true');
  });
});
