import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
import { AvatarsComponent } from '../avatars/avatars.component';

/**
 * AvaTagComponent: Modern, accessible, and highly customizable tag/chip component.
 * Supports filled/outlined, color variants, pill/rect, removable, icons, avatars, sizes, and custom styles.
 */
@Component({
  selector: 'ava-tag',
  templateUrl: './tags.component.html',
  styleUrls: ['./tags.component.scss'],
  standalone: true,
  imports: [CommonModule, IconComponent, AvatarsComponent],
})
export class AvaTagComponent {
  /** Tag label text */
  @Input() label!: string;
  /** Color variant */
  @Input() color: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' | 'custom' = 'default';
  /** Outlined or filled */
  @Input() variant: 'filled' | 'outlined' = 'filled';
  /** Tag size */
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  /** Pill/rounded shape */
  @Input() pill = false;
  /** Removable (shows close icon) */
  @Input() removable = false;
  /** Disabled state */
  @Input() disabled = false;
  /** Icon name (ava-icon) */
  @Input() icon?: string;
  /** Icon position (end position only shows when removable=false) */
  @Input() iconPosition: 'start' | 'end' = 'start';
  /** Avatar: image URL or initials */
  @Input() avatar?: string;
  /** Custom icon color */
  @Input() iconColor?: string;
  /** Custom style object for CSS vars */
  @Input() customStyle?: Record<string, string>;
  /** Custom class for tag */
  @Input() customClass?: string;
  /** Tag type: tag or badge */
  @Input() type: 'badge' | 'tag' = 'tag';
  /** Emits when tag is removed (close icon) */
  @Output() removed = new EventEmitter<void>();
  /** Emits when tag is clicked (if handler provided) */
  @Output() clicked = new EventEmitter<void>();

  /** Internal hover state */
  isHovered = false;

  /** True if tag is clickable (handler attached and not disabled) */
  get clickable(): boolean {
    return this.clicked.observed && !this.disabled;
  }

  /** Get icon size based on tag size */
  getIconSize(): number {
    switch (this.size) {
      case 'xs':
        return 12;
      case 'sm':
        return 12;
      case 'md':
        return 14;
      case 'lg':
        return 16;
      case 'xl':
        return 20;
      default:
        return 12;
    }
  }

  /** Get icon color based on position and hover state */
  getIconColor(position: 'start' | 'end'): string {
    // Return custom iconColor if set
    if (this.iconColor) {
      return this.iconColor;
    }

    // Handle disabled state
    if (this.disabled) {
      if (position === 'start') {
        return 'var(--global-color-gray-400)';
      }
      if (position === 'end') {
        return 'var(--global-color-gray-400)';
      }
    }

    // Handle start icons (always black)
    if (position === 'start') {
      return 'var(--global-color-black)';
    }

    // Handle end icons (gray by default, black on hover)
    if (position === 'end') {
      return this.isHovered ? 'var(--global-color-black)' : 'var(--global-color-gray-400)';
    }

    return 'var(--global-color-gray-400)';
  }

  /** Check if avatar should be shown */
  get shouldShowAvatar(): boolean {
    return !!this.avatar;
  }

  /** Get avatar size based on tag size */
  getAvatarSize(): 'ultra-small' | 'small' | 'medium' | 'large' {
    switch (this.size) {
      case 'xl':
        return 'small';
      case 'lg':
        return 'ultra-small';
      case 'md':
        return 'ultra-small';
      case 'sm':
        return 'ultra-small';
      case 'xs':
        return 'ultra-small';
      default:
        return 'ultra-small';
    }
  }

  /** Remove handler (close icon) */
  onRemove(event: Event) {
    event.stopPropagation();
    if (!this.disabled) {
      this.removed.emit();
    }
  }

  /** Click handler (entire tag) */
  onClick() {
    if (this.clickable) {
      this.clicked.emit();
    }
  }

  /** Mouse enter handler */
  onMouseEnter() {
    if (!this.disabled) {
      this.isHovered = true;
    }
  }

  /** Mouse leave handler */
  onMouseLeave() {
    this.isHovered = false;
  }
} 