.info-dialog {
    display: flex;
    flex-direction: column;

    .content-wrapper {
        display: flex;
        align-items: flex-start;
        gap: var(--dialog-content-gap);
    }

    .text-content {
        flex: 1;
        min-width: 0;
    }

    .icon-container {
        flex-shrink: 0;
        align-self: flex-start;

        ava-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--dialog-info-color);
            border-radius: 50%;
            color: var(--dialog-icon-text-color);
            padding: var(--dialog-icon-border-width);
            min-width: var(--dialog-md-icon-size);
            min-height: var(--dialog-md-icon-size);
        }
    }

    // Size-specific icon adjustments
    &.sm .icon-container ava-icon {
        min-width: var(--dialog-sm-icon-size);
        min-height: var(--dialog-sm-icon-size);
        padding: var(--dialog-icon-border-width);
    }

    &.md .icon-container ava-icon {
        min-width: var(--dialog-md-icon-size);
        min-height: var(--dialog-md-icon-size);
        padding: var(--dialog-icon-border-width);
    }

    &.lg .icon-container ava-icon {
        min-width: var(--dialog-lg-icon-size);
        min-height: var(--dialog-lg-icon-size);
        padding: var(--dialog-icon-border-width);
    }

    .text-content {
        flex: 1;
        min-width: 0;
    }

    .popup-title {
        color: var(--dialog-text-color);
        font-family: var(--dialog-title-font-family);
        font-weight: var(--dialog-title-font-weight);
        text-align: left;
        margin: 0;
        margin-top: 0;
        margin-bottom: 0;
        padding: 0;
        line-height: var(--dialog-title-line-height-tight);
    }

    .inline-text {
        color: var(--dialog-text-color);
        font-family: var(--dialog-text-font-family);
        font-weight: var(--dialog-text-font-weight);
        margin: 0;
        margin-top: 0;
        margin-bottom: 0;
        padding: 0;
        text-align: left;
        word-wrap: break-word;
        overflow-wrap: break-word;
        line-height: var(--dialog-text-line-height);
    }

    .dialog-actions {
        display: flex;
        gap: var(--dialog-actions-gap);
        margin-top: var(--dialog-actions-margin-top);
        justify-content: center;
        flex-wrap: wrap;
    }

    // Large variant
    &.lg {
        width: var(--dialog-lg-width);

        .popup-title {
            font-size: var(--dialog-lg-title-font-size);
            line-height: var(--dialog-lg-title-line-height);
        }

        .inline-text {
            font-size: var(--dialog-lg-text-font-size);
            line-height: var(--dialog-lg-text-line-height);
        }
    }

    // Medium variant
    &.md {
        width: var(--dialog-md-width);

        .popup-title {
            font-size: var(--dialog-md-title-font-size);
            line-height: var(--dialog-md-title-line-height);
        }

        .inline-text {
            font-size: var(--dialog-md-text-font-size);
            line-height: var(--dialog-md-text-line-height);
        }
    }

    // Small variant
    &.sm {
        width: var(--dialog-sm-width);

        .popup-title {
            font-size: var(--dialog-sm-title-font-size);
            font-weight: var(--dialog-title-font-weight);
            line-height: var(--dialog-sm-title-line-height);
        }

        .inline-text {
            font-size: var(--dialog-sm-text-font-size);
            line-height: var(--dialog-sm-text-line-height);
        }
    }
}