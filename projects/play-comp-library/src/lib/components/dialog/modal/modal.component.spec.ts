import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ModalComponent } from './modal.component';

describe('ModalComponent', () => {
  let component: ModalComponent;
  let fixture: ComponentFixture<ModalComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit closed event when onClose is called', () => {
    spyOn(component.closed, 'emit');
    component.onClose();
    expect(component.closed.emit).toHaveBeenCalledWith({ action: 'close' });
  });

  it('should apply modal styles correctly', () => {
    component.width = '500px';
    component.height = '300px';
    component.maxWidth = '90vw';
    component.maxHeight = '90vh';

    const styles = component.modalStyles;
    expect(styles.width).toBe('500px');
    expect(styles.height).toBe('300px');
    expect(styles['max-width']).toBe('90vw');
    expect(styles['max-height']).toBe('90vh');
  });
});
