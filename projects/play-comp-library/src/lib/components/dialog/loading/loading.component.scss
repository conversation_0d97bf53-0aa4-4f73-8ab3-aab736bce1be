.loading-dialog {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex: 1;
    gap: var(--dialog-content-gap);
    text-align: center;
    background: var(--dialog-background);
    border-radius: var(--dialog-border-radius);
    overflow: hidden;

    .spinner-container {
        text-align: center;
        margin-bottom: var(--dialog-icon-margin-bottom);
    }

    .spinner {
        width: var(--dialog-spinner-size);
        height: var(--dialog-spinner-size);
        border: var(--dialog-spinner-border-width) solid var(--dialog-spinner-border-color);
        border-top: var(--dialog-spinner-border-width) solid var(--dialog-spinner-border-top-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .popup-title {
        color: var(--popup-heading-text);
        margin: 0;
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        line-height: var(--popup-heading-line-height);
    }

    .inline-text {
        color: var(--popup-black-color);
        font-size: var(--popup-description-size);
        line-height: var(--popup-description-line-height);
        margin: 0;
        max-width: var(--dialog-max-width);
    }

    .progress-container {
        width: 100%;
        max-width: var(--dialog-progress-max-width);
        display: flex;
        flex-direction: column;
        gap: var(--dialog-content-gap);
    }

    .progress-bar {
        width: 100%;
        height: var(--dialog-progress-bar-height);
        background-color: var(--dialog-spinner-border-color);
        border-radius: var(--dialog-progress-bar-border-radius);
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background-color: var(--dialog-spinner-border-top-color);
        transition: width 0.3s ease;
        border-radius: var(--dialog-progress-bar-border-radius);
    }

    .progress-text {
        font-size: var(--dialog-progress-text-font-size);
        color: var(--popup-black-color);
        text-align: center;
    }

    .dialog-actions {
        display: flex;
        gap: var(--dialog-actions-gap);
        margin-top: var(--dialog-actions-margin-top);
        justify-content: center;
        flex-wrap: wrap;
    }
}