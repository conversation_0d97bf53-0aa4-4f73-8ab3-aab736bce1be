<div class="feedback-dialog" [ngClass]="[bottomBorder ? 'has-bottom-border' : '', destructive ? 'destructive' : '']">
    <h3 class="popup-title" id="popup-title" [ngClass]="{ 'destructive': destructive }">
        {{ title }}
    </h3>

    <p class="inline-text" [innerHTML]="message"></p>
    <div class="input-container">
        <ava-textarea [label]="label" placeholder="Type here..." name="feedback" #feedbackText>
        </ava-textarea>
    </div>

    <div class="dialog-actions">
        <ava-button [label]="cancelButtonText" [variant]="cancelButtonVariant" size="medium" (userClick)="onCancel()">
        </ava-button>

        <ava-button [label]="confirmButtonText" [variant]="effectiveConfirmVariant" size="small"
            (userClick)="onConfirm()">
        </ava-button>
    </div>
</div>