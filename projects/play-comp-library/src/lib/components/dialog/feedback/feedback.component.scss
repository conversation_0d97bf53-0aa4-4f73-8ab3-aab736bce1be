.feedback-dialog {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex: 1;
    gap: var(--dialog-content-gap);
    text-align: center;
    background: var(--dialog-background);
    border-radius: var(--dialog-border-radius);
    overflow: hidden;

    .icon-container {
        text-align: center;
        margin-bottom: var(--dialog-icon-margin-bottom);
    }

    .popup-title {
        color: var(--popup-heading-text);
        margin: 0;
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        line-height: var(--popup-heading-line-height);

        &.destructive {
            color: var(--dialog-error-color);
        }
    }

    .inline-text {
        color: var(--popup-black-color);
        font-size: var(--popup-description-size);
        line-height: var(--popup-description-line-height);
        margin: 0;
        max-width: var(--dialog-max-width);
    }

    .input-container {
        width: 100%;
    }

    .dialog-actions {
        display: flex;
        gap: var(--dialog-actions-gap);
        margin-top: var(--dialog-actions-margin-top);
        justify-content: center;
        flex-wrap: wrap;
    }
}