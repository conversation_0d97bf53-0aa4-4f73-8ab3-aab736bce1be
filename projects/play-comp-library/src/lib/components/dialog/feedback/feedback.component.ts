import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
import { AvaTextareaComponent } from '../../textarea/ava-textarea.component';
@Component({
  selector: 'ava-feedback',
  imports: [CommonModule, ButtonComponent, AvaTextareaComponent],
  templateUrl: './feedback.component.html',
  styleUrl: './feedback.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FeedbackComponent {
  @Input() title = 'Confirm Action';
  @Input() message = 'Are you sure you want to proceed with this action?';
  @Input() icon = 'help-circle';
  @Input() iconColor = 'var(--dialog-text-color)';
  @Input() iconSize = 50;
  @Input() confirmButtonText = 'Confirm';
  @Input() cancelButtonText = 'Cancel';
  @Input() confirmButtonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() cancelButtonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'secondary';
  @Input() destructive = false; // For dangerous actions
  @Input() label = '';
  @Input() bottomBorder = false;

  @Output() closed = new EventEmitter<{ action: string; confirmed?: boolean, feedback?: string }>();
  @ViewChild('feedbackText') feedbackTextRef!: AvaTextareaComponent;
  get effectiveConfirmVariant() {
    return this.destructive ? 'danger' : this.confirmButtonVariant;
  }

  get effectiveIconColor() {
    return this.destructive ? 'var(--dialog-error-color)' : this.iconColor;
  }

  get effectiveIcon() {
    return this.destructive ? 'alert-triangle' : this.icon;
  }

  onConfirm() {
    const feedbackValue = this.feedbackTextRef ? this.feedbackTextRef.value : '';
    this.closed.emit({ action: 'feedback', confirmed: true, feedback: feedbackValue });
  }

  onCancel() {
    this.closed.emit({ action: 'cancel', confirmed: false });
  }
}

