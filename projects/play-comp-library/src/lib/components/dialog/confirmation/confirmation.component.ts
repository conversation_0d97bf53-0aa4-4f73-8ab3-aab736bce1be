import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

@Component({
  selector: 'ava-confirmation',
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './confirmation.component.html',
  styleUrl: './confirmation.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ConfirmationComponent {
  @Input() title = 'Confirm Action';
  @Input() message = 'Are you sure you want to proceed with this action?';
  @Input() icon = 'help-circle';
  @Input() iconColor = 'var(--dialog-text-color)';
  @Input() iconSize = 50;
  @Input() confirmButtonText = 'Confirm';
  @Input() cancelButtonText = 'Cancel';
  @Input() confirmButtonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() cancelButtonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'secondary';
  @Input() destructive = false; // For dangerous actions
  @Input() bottomBorder = false;
  @Input() size: 'lg' | 'md' | 'sm' = 'lg';

  @Output() closed = new EventEmitter<{ action: string; confirmed?: boolean }>();

  get effectiveConfirmVariant() {
    return this.destructive ? 'danger' : this.confirmButtonVariant;
  }

  get effectiveIconColor() {
    return this.destructive ? 'var(--dialog-error-color)' : this.iconColor;
  }

  get effectiveIcon() {
    return this.destructive ? 'alert-triangle' : this.icon;
  }

  onConfirm() {
    this.closed.emit({ action: 'confirm', confirmed: true });
  }

  onCancel() {
    this.closed.emit({ action: 'cancel', confirmed: false });
  }
}
