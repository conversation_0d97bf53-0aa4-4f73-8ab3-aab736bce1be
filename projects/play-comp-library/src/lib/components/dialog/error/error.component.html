<div class="error-dialog"
    [ngClass]="[size, showButtons && buttons.length > 0 ? 'has-actions' : '', bottomBorder ? 'has-bottom-border' : '']">
    <div class="content-wrapper">
        <div class="icon-container">
            <ava-icon [iconName]="icon" [iconSize]="effectiveIconSize" iconColor="white" aria-hidden="true">
            </ava-icon>
        </div>
        <div class="text-content">
            <h3 class="popup-title" id="popup-title">
                {{ title }}
            </h3>
            <p class="inline-text" [innerHTML]="message"></p>
        </div>
    </div>

    <div class="dialog-actions" *ngIf="showButtons && buttons.length > 0">
        <ava-button *ngFor="let button of buttons" [label]="button.label" [variant]="button.variant || 'secondary'"
            [disabled]="button.disabled || false" size="small"
            (userClick)="onButtonClick(button.action || button.label)">
        </ava-button>
    </div>
</div>