import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SuccessComponent } from './success.component';

describe('SuccessComponent', () => {
  let component: SuccessComponent;
  let fixture: ComponentFixture<SuccessComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SuccessComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SuccessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display success dialog with buttons only', () => {
    component.title = 'Success';
    component.message = 'Operation completed successfully';
    component.showButtons = true;
    component.buttons = [
      { label: 'OK', variant: 'primary', action: 'ok' },
      { label: 'Cancel', variant: 'secondary', action: 'cancel' }
    ];
    fixture.detectChanges();

    expect(component.showButtons).toBeTruthy();
    expect(component.buttons.length).toBe(2);
  });

  it('should display success dialog without buttons', () => {
    component.title = 'Success';
    component.message = 'Operation completed successfully';
    component.showButtons = false;
    component.buttons = [];
    fixture.detectChanges();

    expect(component.showButtons).toBeFalsy();
    expect(component.buttons.length).toBe(0);
  });
});
