import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';

export interface SuccessButton {
  label: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  action?: string;
  disabled?: boolean;
}

@Component({
  selector: 'ava-success',
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './success.component.html',
  styleUrl: './success.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SuccessComponent {
  @Input() title = 'Success';
  @Input() message = 'Operation completed successfully!';
  @Input() icon = 'circle-check';
  @Input() iconColor = 'var(--dialog-success-color)';
  @Input() size: 'lg' | 'md' | 'sm' | 'xl' = 'lg';
  @Input() buttons: SuccessButton[] = [];
  @Input() showButtons = false;
  @Input() bottomBorder = true;

  @Output() closed = new EventEmitter<{ action: string }>();
  @Output() buttonClick = new EventEmitter<string>();

  get effectiveIconSize(): number {
    switch (this.size) {
      case 'lg':
        return 24;
      case 'md':
        return 20;
      case 'sm':
        return 16;
      default:
        return 24;
    }
  }

  onClose() {
    this.closed.emit({ action: 'close' });
  }

  onButtonClick(action: string): void {
    this.buttonClick.emit(action);
    this.closed.emit({ action });
  }
}
