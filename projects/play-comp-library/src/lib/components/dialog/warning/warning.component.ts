import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
export interface WarningButton {
  label: string;
  action?: string;
  variant?: 'primary' | 'secondary' | 'warning' | 'danger';
  disabled?: boolean;
}

@Component({
  selector: 'ava-warning',
  imports: [CommonModule, IconComponent, ButtonComponent],
  templateUrl: './warning.component.html',
  styleUrl: './warning.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WarningComponent {
  @Input() title = 'Warning';
  @Input() message = 'Please review the following information carefully.';
  @Input() icon = 'alert-triangle';
  @Input() iconColor = 'var(--dialog-warning-color)';
  @Input() size: 'lg' | 'md' | 'sm' | 'xl' = 'lg';
  @Input() buttons: WarningButton[] = [];
  @Input() showButtons = false;
  @Input() bottomBorder = true;

  @Output() closed = new EventEmitter<{ action: string }>();
  @Output() buttonClick = new EventEmitter<string>();

  get effectiveIconSize(): number {
    switch (this.size) {
      case 'lg':
        return 24;
      case 'md':
        return 20;
      case 'sm':
        return 16;
      default:
        return 24;
    }
  }

  onButtonClick(action: string): void {
    this.buttonClick.emit(action);
    this.closed.emit({ action });
  }
}
