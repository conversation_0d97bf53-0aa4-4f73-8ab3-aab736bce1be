.custom-dialog {
    display: flex;
    align-items: center;
    flex-direction: column;
    flex: 1;
    gap: var(--dialog-content-gap);
    text-align: center;

    .icon-container {
        text-align: center;
        margin-bottom: var(--dialog-icon-margin-bottom);
    }

    .popup-title {
        color: var(--dialog-text-color);
        font-family: var(--dialog-title-font-family);
        font-style: normal;
        font-weight: var(--dialog-text-font-weight);
        text-align: center;
        margin: 0;

        &.success {
            color: var(--dialog-success-color);
        }

        &.error {
            color: var(--dialog-error-color);
        }

        &.warning {
            color: var(--dialog-warning-color);
        }

        &.info {
            color: var(--dialog-info-color);
        }
    }

    .inline-text {
        color: var(--dialog-text-color);
        font-family: var(--dialog-text-font-family);
        font-style: normal;
        font-weight: var(--dialog-text-font-weight);
        margin: 0;
        margin-top: var(--dialog-text-margin-top);
        max-width: var(--dialog-max-width);
        text-align: center;
    }

    .custom-content {
        color: var(--popup-black-color);
        font-size: var(--popup-description-size);
        line-height: var(--popup-description-line-height);
        max-width: var(--dialog-max-width);
        text-align: center;

        // Allow custom styling within content
        * {
            max-width: 100%;
        }
    }

    .dialog-actions {
        display: flex;
        gap: var(--dialog-actions-gap);
        margin-top: var(--dialog-actions-margin-top);
        justify-content: center;
        flex-wrap: wrap;
    }

    // Large variant
    &.lg {
        width: var(--dialog-lg-width);

        .popup-title {
            font-size: var(--dialog-lg-title-font-size);
            line-height: var(--dialog-lg-title-line-height);
        }

        .inline-text {
            font-size: var(--dialog-lg-text-font-size);
            line-height: var(--dialog-lg-text-line-height);
        }
    }

    // Medium variant
    &.md {
        width: var(--dialog-md-width);

        .popup-title {
            font-size: var(--dialog-md-title-font-size);
            line-height: var(--dialog-md-title-line-height);
        }

        .inline-text {
            font-size: var(--dialog-md-text-font-size);
            line-height: var(--dialog-md-text-line-height);
        }
    }

    // Small variant
    &.sm {
        width: var(--dialog-sm-width);

        .popup-title {
            font-size: var(--dialog-sm-title-font-size);
            font-weight: var(--dialog-title-font-weight);
            line-height: var(--dialog-sm-title-line-height);
        }

        .inline-text {
            font-size: var(--dialog-sm-text-font-size);
            line-height: var(--dialog-sm-text-line-height);
        }
    }

    // Variant-specific styling
    &.success {
        .icon-container {
            color: var(--dialog-success-color);
        }
    }

    &.error {
        .icon-container {
            color: var(--dialog-error-color);
        }
    }

    &.warning {
        .icon-container {
            color: var(--dialog-warning-color);
        }
    }

    &.info {
        .icon-container {
            color: var(--dialog-info-color);
        }
    }
}