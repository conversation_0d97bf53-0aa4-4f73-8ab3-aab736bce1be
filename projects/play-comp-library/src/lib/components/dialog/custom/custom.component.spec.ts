import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CustomComponent } from './custom.component';

describe('CustomComponent', () => {
  let component: CustomComponent;
  let fixture: ComponentFixture<CustomComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(CustomComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default dialog title', () => {
    expect(component.title).toBe('Dialog');
  });

  it('should return correct variant icon', () => {
    component.variant = 'success';
    expect(component.variantIcon).toBe('circle-check');
    
    component.variant = 'error';
    expect(component.variantIcon).toBe('alert-circle');
    
    component.variant = 'warning';
    expect(component.variantIcon).toBe('alert-triangle');
    
    component.variant = 'info';
    expect(component.variantIcon).toBe('info');
  });

  it('should use custom icon when provided', () => {
    component.icon = 'custom-icon';
    expect(component.variantIcon).toBe('custom-icon');
  });
});
