import {
    ApplicationRef,
    ComponentRef,
    EmbeddedViewRef,
    Injectable,
    Injector,
    Type,
    createComponent,
    EnvironmentInjector
} from '@angular/core';
import { DialogContainerComponent } from './dialog-container/dialog-container.component'
import { ModalComponent } from './modal/modal.component';
import { SuccessComponent } from './success/success.component';
import { ErrorComponent, ErrorButton } from './error/error.component';
import { WarningComponent, WarningButton } from './warning/warning.component';
import { InfoComponent, InfoButton } from './info/info.component';
import { ConfirmationComponent } from './confirmation/confirmation.component';
import { LoadingComponent } from './loading/loading.component';
import { CustomComponent } from './custom/custom.component';
import { FeedbackComponent } from './feedback/feedback.component';

// Dialog Types and Interfaces
export interface DialogConfig {
    title?: string;
    message?: string;
    icon?: string;
    iconColor?: string;
    iconSize?: number;
    showCloseButton?: boolean;
    backdrop?: boolean;
    width?: string;
    height?: string;
    data?: any;
}

export interface SuccessDialogConfig extends DialogConfig {
    buttons?: DialogButton[];
    showButtons?: boolean;
    bottomBorder?: boolean;
    size?: 'lg' | 'md' | 'sm';
}

export interface ErrorDialogConfig extends DialogConfig {
    showRetryButton?: boolean;
    retryButtonText?: string;
    closeButtonText?: string;
    bottomBorder?: boolean;
    buttons?: ErrorButton[];
    showButtons?: boolean;
    size?: 'lg' | 'md' | 'sm';
}



export interface WarningDialogConfig extends DialogConfig {
    showProceedButton?: boolean;
    proceedButtonText?: string;
    showCancelButton?: boolean;
    cancelButtonText?: string;
    bottomBorder?: boolean;
    buttons?: WarningButton[];
    showButtons?: boolean;
    size?: 'lg' | 'md' | 'sm';
}



export interface InfoDialogConfig extends DialogConfig {
    showOkButton?: boolean;
    okButtonText?: string;
    showLearnMoreButton?: boolean;
    learnMoreButtonText?: string;
    bottomBorder?: boolean;
    buttons?: InfoButton[];
    showButtons?: boolean;
    size?: 'lg' | 'md' | 'sm';
}



export interface ConfirmationDialogConfig extends DialogConfig {
    confirmButtonText?: string;
    cancelButtonText?: string;
    confirmButtonVariant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    cancelButtonVariant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    destructive?: boolean;
    bottomBorder?: boolean;
}

export interface LoadingDialogConfig extends DialogConfig {
    progress?: number;
    showProgress?: boolean;
    showCancelButton?: boolean;
    cancelButtonText?: string;
    spinnerColor?: string;
    indeterminate?: boolean;
    bottomBorder?: boolean;
}

export interface CustomDialogConfig extends DialogConfig {
    buttons?: DialogButton[];
    variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
    customContent?: string;
    showIcon?: boolean;
    showTitle?: boolean;
    showMessage?: boolean;
    bottomBorder?: boolean;
    label?: string;
    confirmButtonText?: string;
    cancelButtonText?: string;
    destructive?: boolean;
}

export interface ModalDialogConfig extends DialogConfig {
    maxWidth?: string;
    maxHeight?: string;
    showCloseButton?: boolean;
}

export interface DialogButton {
    label: string;
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    action?: string;
    disabled?: boolean;
}

export interface DialogResult {
    action?: string;
    data?: any;
    confirmed?: boolean;
}

export type DialogType = 'success' | 'error' | 'warning' | 'info' | 'confirmation' | 'loading' | 'custom';


@Injectable({ providedIn: 'root' })
export class DialogService {
    private dialogRef!: ComponentRef<DialogContainerComponent>;
    constructor(
        private appRef: ApplicationRef,
        private injector: Injector,
        private envInjector: EnvironmentInjector
    ) { }

    open<T extends object>(component: Type<T>, data?: Partial<T>): Promise<any> {
        // Prevent body scrolling
        document.body.style.overflow = 'hidden';

        // Create container
        this.dialogRef = createComponent(DialogContainerComponent, {
            environmentInjector: this.envInjector,
            elementInjector: this.injector
        });

        this.appRef.attachView(this.dialogRef.hostView);
        const containerElem = (this.dialogRef.hostView as EmbeddedViewRef<any>)
            .rootNodes[0] as HTMLElement;
        document.body.appendChild(containerElem);

        // Create the target component dynamically inside the container
        const viewRef = this.dialogRef.instance.container.createComponent(component, {
            injector: this.injector,
            environmentInjector: this.envInjector
        });

        if (data) {
            Object.assign(viewRef.instance, data);

            // Apply CSS classes to dialog container based on component properties
            requestAnimationFrame(() => {
                const dialogContent = containerElem.querySelector('.ava-dialog-content') as HTMLElement;
                if (dialogContent && data) {
                    // Apply bottom border class
                    if ((data as any).bottomBorder) {
                        dialogContent.classList.add('has-bottom-border');

                        // For non-custom dialogs, determine variant from component type
                        if (component === SuccessComponent) {
                            dialogContent.classList.add('success');
                            console.log('Applied success class to dialog');
                        } else if (component === ErrorComponent) {
                            dialogContent.classList.add('error');
                            console.log('Applied error class to dialog');
                        } else if (component === WarningComponent) {
                            dialogContent.classList.add('warning');
                            console.log('Applied warning class to dialog');
                        } else if (component === InfoComponent) {
                            dialogContent.classList.add('info');
                            console.log('Applied info class to dialog');
                        } else if ((data as any).destructive) {
                            dialogContent.classList.add('error'); // Destructive actions use error color
                        } else {
                            dialogContent.classList.add('default');
                        }

                        // Apply variant class for border color (for custom dialogs)
                        const variant = (data as any).variant;
                        if (variant && variant !== 'default') {
                            dialogContent.classList.add(variant);
                        }
                    }
                }
            });
        }

        return new Promise(resolve => {
            // Forward close from dialog shell
            this.dialogRef.instance.closed.subscribe((result: any) => {
                this.close();
                resolve(result);
            });

            // Optionally: Listen to `closed` output from the inner component
            const inner = viewRef.instance as any;
            if (inner.closed && typeof inner.closed.subscribe === 'function') {
                inner.closed.subscribe((result: any) => {
                    this.close();
                    resolve(result);
                });
            }
        });
    }

    close() {
        if (this.dialogRef) {
            // Restore body scrolling
            document.body.style.overflow = '';

            this.appRef.detachView(this.dialogRef.hostView);
            this.dialogRef.destroy();
        }
    }
    success(config?: Partial<SuccessDialogConfig>): Promise<DialogResult> {
        return this.open(SuccessComponent, {
            title: 'Success',
            message: 'Operation completed successfully!',
            buttons: [],
            showButtons: false,
            bottomBorder: true,
            size: 'lg',
            ...config
        });
    }

    error(config?: Partial<ErrorDialogConfig>): Promise<DialogResult> {
        return this.open(ErrorComponent, {
            title: 'Error',
            message: 'An error occurred. Please try again.',
            showRetryButton: false,
            showCloseButton: true,
            bottomBorder: true,
            buttons: [],
            showButtons: false,
            size: 'lg',
            ...config
        });
    }

    warning(config?: Partial<WarningDialogConfig>): Promise<DialogResult> {
        return this.open(WarningComponent, {
            title: 'Warning',
            message: 'Please review the following information carefully.',
            showProceedButton: false,
            showCancelButton: true,
            bottomBorder: true,
            buttons: [],
            showButtons: false,
            size: 'lg',
            ...config
        });
    }

    info(config?: Partial<InfoDialogConfig>): Promise<DialogResult> {
        return this.open(InfoComponent, {
            title: 'Information',
            message: 'Here is some important information for you.',
            showOkButton: true,
            showLearnMoreButton: false,
            bottomBorder: true,
            buttons: [],
            showButtons: false,
            size: 'lg',
            ...config
        });
    }

    confirmation(config?: Partial<ConfirmationDialogConfig>): Promise<DialogResult> {
        return this.open(ConfirmationComponent, {
            title: 'Confirm Action',
            message: 'Are you sure you want to proceed with this action?',
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            confirmButtonVariant: 'primary',
            cancelButtonVariant: 'secondary',
            destructive: false,
            bottomBorder: false,
            ...config
        });
    }

    loading(config?: Partial<LoadingDialogConfig>): Promise<DialogResult> {
        return this.open(LoadingComponent, {
            title: 'Loading...',
            message: 'Please wait while we process your request.',
            showProgress: false,
            progress: 0,
            showCancelButton: false,
            indeterminate: true,
            bottomBorder: false,
            ...config
        });
    }

    custom(config?: Partial<CustomDialogConfig>): Promise<DialogResult> {
        return this.open(CustomComponent, {
            title: 'Dialog',
            message: '',
            variant: 'default',
            showIcon: true,
            showTitle: true,
            showMessage: true,
            buttons: [],
            bottomBorder: false,
            ...config
        });
    }

    feedback(config?: Partial<CustomDialogConfig>): Promise<DialogResult> {
        return this.open(FeedbackComponent, {
            title: 'Feedback',
            message: 'Please provide your feedback',
            variant: 'default',
            showIcon: true,
            showTitle: true,
            showMessage: true,
            buttons: [],
            bottomBorder: false,
            label: 'Your feedback',
            confirmButtonText: 'Submit',
            cancelButtonText: 'Cancel',
            destructive: false,
            ...config
        });
    }

    /**
     * Opens a modal dialog with content projection support
     * Use this method when you want to provide custom content with header, body, and footer sections
     */
    openModal<T extends object>(component: Type<T>, config?: Partial<ModalDialogConfig>, data?: Partial<T>): Promise<any> {
        // Prevent body scrolling
        document.body.style.overflow = 'hidden';

        // Create container
        this.dialogRef = createComponent(DialogContainerComponent, {
            environmentInjector: this.envInjector,
            elementInjector: this.injector
        });

        this.appRef.attachView(this.dialogRef.hostView);
        const containerElem = (this.dialogRef.hostView as EmbeddedViewRef<any>)
            .rootNodes[0] as HTMLElement;

        // Add class to indicate modal usage (removes default padding)
        const dialogContent = containerElem.querySelector('.ava-dialog-content');
        if (dialogContent) {
            dialogContent.classList.add('has-modal');
        }

        document.body.appendChild(containerElem);

        // Create the ModalComponent as a wrapper
        const modalRef = this.dialogRef.instance.container.createComponent(ModalComponent, {
            injector: this.injector,
            environmentInjector: this.envInjector
        });

        // Apply modal configuration
        if (config) {
            Object.assign(modalRef.instance, {
                width: config.width,
                height: config.height,
                maxWidth: config.maxWidth,
                maxHeight: config.maxHeight,
                showCloseButton: config.showCloseButton ?? true
            });
        }

        // Create the target component inside the modal's dynamic container
        const componentRef = modalRef.instance.container.createComponent(component, {
            injector: this.injector,
            environmentInjector: this.envInjector
        });

        if (data) {
            Object.assign(componentRef.instance, data);
        }

        return new Promise(resolve => {
            // Forward close from dialog container
            this.dialogRef.instance.closed.subscribe((result: any) => {
                this.close();
                resolve(result);
            });

            // Forward close from modal component
            modalRef.instance.closed.subscribe((result: any) => {
                this.close();
                resolve(result);
            });

            // Optionally: Listen to `closed` output from the inner component
            const inner = componentRef.instance as any;
            if (inner.closed && typeof inner.closed.subscribe === 'function') {
                inner.closed.subscribe((result: any) => {
                    this.close();
                    resolve(result);
                });
            }
        });
    }


}
