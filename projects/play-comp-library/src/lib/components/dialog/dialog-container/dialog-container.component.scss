/* ava-dialog-container.component.scss */
.ava-dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: rgba(0, 0, 0, 0.3);
    z-index: 999;
    overflow: hidden;
    overscroll-behavior: contain;
}

.ava-dialog-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--dialog-background);
    padding: var(--dialog-content-padding);
    border-radius: var(--dialog-border-radius);
    z-index: 1000;
    min-width: unset;
    box-shadow: var(--dialog-shadow);

    &.has-bottom-border {
        &.success {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-success-color);
        }

        &.error {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-error-color);
        }

        &.warning {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-warning-color);
        }

        &.info {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-info-color);
        }

        &.default {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-primary-color);
        }

        &.confirmation {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-primary-color);
        }

        &.custom {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-primary-color);
        }

        &.feedback {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-primary-color);
        }

        &.loading {
            border-bottom: var(--dialog-border-bottom-height) solid var(--dialog-primary-color);
        }
    }

    /* Remove padding when using new modal component */
    &.has-modal {
        padding: 0;
    }

    &:has(.ava-modal) {
        padding: 0;
        border-radius: 8px;
    }

    .title {
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        margin: var(--popup-heading-margin);
        color: var(--popup-heading-text);
        line-height: var(--popup-heading-line-height);
        text-align: center;
    }

    .inline-text {
        font-size: var(--popup-description-size);
        color: var(--popup-black-color);
        line-height: var(--popup-description-line-height);
        word-break: break-word;
        overflow-wrap: anywhere;
        flex: 1;
        display: block;
        margin: 0;
    }

    .close-container {
        position: relative;
        height: 0;
        margin-bottom: var(--dialog-icon-border-width);

        ava-icon {
            position: absolute;
            top: -5px;
            right: -5px;
            z-index: 10;
        }
    }

    &.has-modal .close-container ava-icon {
        top: 13px;
        right: 13px
        }
}
