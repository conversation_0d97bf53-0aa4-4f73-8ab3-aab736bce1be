<table class="ava-table">
  <thead>
    <tr>
      <th *ngFor="let col of columns" [style.background-color]="col.bgColor" [style.color]="col.textColor"> {{ col.label
        }}
      </th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let row of data">
      <td *ngFor="let col of columns">
        <ng-container [ngSwitch]="col.type">
          <!-- Text Column -->
          <ng-container *ngSwitchCase="'text'">
            {{ getValue(row, col.key) }}
          </ng-container>

          <!-- Status Column -->
          <ng-container *ngIf="col.type === 'status'">
            <ava-tag *ngFor="let tag of row.tags" [label]="tag.label" [color]="tag.color ?? 'default'"
              [variant]="tag.variant ?? 'filled'" [icon]="tag.icon" size="sm"
              [iconPosition]="tag.iconPosition ?? 'start'">
            </ava-tag>

          </ng-container>

          <!-- Actions Column -->
          <ng-container *ngIf="col.type === 'actions'">
            <ava-icon class="ava-icon" *ngFor="let action of row.actions" [iconName]="action.icon" [iconSize]="16"
              [iconColor]="action.iconColor" [cursor]="true" (click)="handleAction(action.icon, row)">
            </ava-icon>
          </ng-container>

          <!-- Default case for any other type -->
          <ng-container *ngSwitchDefault>
            {{ getValue(row, col.key) | json }}
          </ng-container>
        </ng-container>
      </td>
    </tr>
  </tbody>
</table>