import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { IconComponent } from '../icon/icon.component';
import { AvaTagComponent } from '../tags/tags.component';

export interface TableColumn {
 key: string;
  label: string;
  type: string;
  bgColor?: string; 
  textColor?: string; 
}

export interface TableAction {
  icon: string;
}

@Component({
  selector: 'ava-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
  standalone: true,
  imports: [CommonModule, IconComponent, AvaTagComponent]
})
export class TableComponent {
  @Input() columns: TableColumn[] = [];
  @Input() data: any[] = [];
   getValue(row: any, key: string): any {
    // Safely access nested properties
    return row[key];
  }
   handleAction(action: string, row: any): void {
  // console.log(`Action ${action} clicked for row`, row);
  // // Implement action handling logic here
}
}
