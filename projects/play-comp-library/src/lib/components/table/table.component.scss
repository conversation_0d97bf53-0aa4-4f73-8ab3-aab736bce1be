.ava-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--table-font-family-body);
  border: 1px solid var(--table-border);

  th, td {
    padding: var( --table-padding);
    text-align: left;

  }

  thead {
    background-color: var(--table-header-background-color);
    
  }

  tbody tr {
    transition: background-color 0.3s ease;

    &:nth-child(odd) {
      background-color: var(--table-background-color-odd);
    }

    &:nth-child(even) {
      background: var(--table-background-color-even);
    }
  }

  // Style for action icons
.ava-icon{
   margin: var(--table-icon-margin);
   padding:var(--table-icon-padding);
}
}
