import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TableComponent, TableColumn } from './table.component';
import { By } from '@angular/platform-browser';
import { AvaTagComponent } from '../tags/tags.component';
import { IconComponent } from '../icon/icon.component';

describe('TableComponent', () => {
  let component: TableComponent;
  let fixture: ComponentFixture<TableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TableComponent, AvaTagComponent, IconComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(TableComponent);
    component = fixture.componentInstance;
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should render table headers correctly', () => {
    component.columns = [
      { key: 'name', label: 'Name', type: 'text' },
      { key: 'status', label: 'Status', type: 'tag' }
    ];
    fixture.detectChanges();

    const headerCells = fixture.debugElement.queryAll(By.css('th'));
    expect(headerCells.length).toBe(2);
    expect(headerCells[0].nativeElement.textContent).toContain('Name');
    expect(headerCells[1].nativeElement.textContent).toContain('Status');
  });

  it('should render rows with data values', () => {
    component.columns = [
      { key: 'name', label: 'Name', type: 'text' },
      { key: 'age', label: 'Age', type: 'text' }
    ];
    component.data = [
      { name: 'Sairam', age: 25 },
      { name: 'Ram', age: 30 }
    ];
    fixture.detectChanges();

    const rows = fixture.debugElement.queryAll(By.css('tbody tr'));
    expect(rows.length).toBe(2);

    const firstRowCells = rows[0].queryAll(By.css('td'));
    expect(firstRowCells[0].nativeElement.textContent).toContain('Sairam');
    expect(firstRowCells[1].nativeElement.textContent).toContain('25');
  });

  it('should use getValue method to access row values', () => {
    const row = { name: 'Ava', status: 'Active' };
    const result = component.getValue(row, 'status');
    expect(result).toBe('Active');
  });

  it('should handle action when handleAction is called', () => {
    spyOn(component, 'handleAction');

    const dummyRow = { id: 1, name: 'Test' };
    component.handleAction('edit', dummyRow);

    expect(component.handleAction).toHaveBeenCalledWith('edit', dummyRow);
  });
});
