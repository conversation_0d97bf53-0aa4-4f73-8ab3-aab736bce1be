.popup-backdrop {
    position: fixed;
    inset: 0;
    z-index: var(--popup-z-index);
    background-color: var(--popup-overlay-background);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: var(--popup-shadow);
    overflow: auto; // Allows scrolling when popup exceeds screen

    &.popup-position-center {
        justify-content: center;
        align-items: center;
    }

    &.popup-position-top {
        justify-content: center;
        align-items: flex-start;
    }

    &.popup-position-bottom {
        justify-content: center;
        align-items: flex-end;
    }

    &.popup-position-top-left {
        justify-content: flex-start;
        align-items: flex-start;
    }

    &.popup-position-top-right {
        justify-content: flex-end;
        align-items: flex-start;
    }

    &.popup-position-bottom-left {
        justify-content: flex-start;
        align-items: flex-end;
    }

    &.popup-position-bottom-right {
        justify-content: flex-end;
        align-items: flex-end;
    }

}

.popup-container {
    position: relative;
    padding: var(--popup-content-padding-xl);
    width: 100%;
    max-height: var(--popup-max-height);
    overflow-y: auto; // Enables vertical scrolling
    background-color: var(--popup-background);
    border: var(--popup-border);
    border-radius: var(--popup-border-radius);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--popup-content-gap);
    transition: all 0.3s ease;

    .popup-title {
        font-size: var(--popup-heading-size);
        font-weight: var(--popup-heading-weight);
        margin: var(--popup-heading-margin);
        color: var(--popup-heading-text);
        line-height: var(--popup-heading-line-height);
        text-align: center;
    }

    .popup-inline-message.single-line {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        text-align: left;
        margin: 0;

        .inline-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            margin: 0;
        }

        .inline-text {
            font-size: var(--popup-description-size);
            color: var(--popup-black-color);
            line-height: var(--popup-description-line-height);
            word-break: break-word;
            overflow-wrap: anywhere;
            flex: 1;
            display: block;
            margin: 0;
        }
    }

    .popup-message {
        font-size: var(--popup-description-font);
        font-weight: var(--popup-description-weight);
        margin: var(--popup-description-margin);
        letter-spacing: -0.022em;
        color: var(--popup-description-text);
        line-height: var(--popup-description-line-height);
        padding-bottom: 8px;
        width: 100%;

        &.left-align {
            text-align: left;
        }

        &.center-align {
            text-align: center;
        }

        &.right-align {
            text-align: right;
        }
    }

    .popup-actions {
        display: flex;
        justify-content: center;
        gap: 12px;
        margin: 0;
        padding-top: 10px;
    }

    .close-btn {
        position: absolute;
        top: 10px;
        right: 12px;
        background: none;
        border: none;
    }
}