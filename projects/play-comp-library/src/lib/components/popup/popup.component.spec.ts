import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PopupComponent } from './popup.component';
import { IconComponent } from '../../components/icon/icon.component';
import { ButtonComponent } from '../../components/button/button.component';
import { By } from '@angular/platform-browser';

describe('PopupComponent', () => {
    let component: PopupComponent;
    let fixture: ComponentFixture<PopupComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [PopupComponent, IconComponent, ButtonComponent]
        }).compileComponents();

        fixture = TestBed.createComponent(PopupComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should emit confirm and closed when onConfirm is called', () => {
        spyOn(component.confirm, 'emit');
        spyOn(component.closed, 'emit');

        component.onConfirm();

        expect(component.confirm.emit).toHaveBeenCalled();
        expect(component.closed.emit).toHaveBeenCalled();
    });

    it('should emit cancel and closed when onCancel is called', () => {
        spyOn(component.cancel, 'emit');
        spyOn(component.closed, 'emit');

        component.onCancel();

        expect(component.cancel.emit).toHaveBeenCalled();
        expect(component.closed.emit).toHaveBeenCalled();
    });

    it('should emit closed only when closePopup is called', () => {
        spyOn(component.closed, 'emit');
        component.closePopup();
        expect(component.closed.emit).toHaveBeenCalled();
    });

    it('should set and get popup width from number', () => {
        component.popupWidth = 500;
        expect(component.popupWidth).toBe('500px');
    });

    it('should set and get popup width from string', () => {
        component.popupWidth = '70%';
        expect(component.popupWidth).toBe('70%');
    });

    it('should return split message lines with trimming from getMessageLines()', () => {
        component.message = 'Line 1<br> Line 2 <br/>Line 3<br >Line 4';
        const lines = component.getMessageLines();
        expect(lines.length).toBe(4);
        expect(lines).toEqual(['Line 1', 'Line 2', 'Line 3', 'Line 4']);
    });

    it('should hide header icon if showHeaderIcon is false', () => {
        component.show = true;
        component.showHeaderIcon = false;
        fixture.detectChanges();

        const icon = fixture.debugElement.query(By.css('ava-icon[iconName="circle-check"]'));
        expect(icon).toBeFalsy();
    });

    //     component.show = true;
    //     component.title = 'My Title';
    //     component.message = 'Message';
    //     fixture.detectChanges();

    //     const title = fixture.debugElement.query(By.css('.popup-title'));
    //     const msg = fixture.debugElement.query(By.css('.popup-message'));
    //     expect(title.nativeElement.textContent).toContain('My Title');
    //     expect(msg.nativeElement.textContent).toContain('Message');
    // });

    // it('should show inline message when showInlineMessage is true', () => {
    //     component.show = true;
    //     component.showInlineMessage = true;
    //     component.inlineMessage = 'Inline text';
    //     fixture.detectChanges();

    //     const inlineText = fixture.debugElement.query(By.css('.inline-text'));
    //     expect(inlineText.nativeElement.textContent).toContain('Inline text');
    // });

    // it('should show cancel and confirm buttons if flags are true', () => {
    //     component.show = true;
    //     component.showCancel = true;
    //     component.showConfirm = true;
    //     fixture.detectChanges();

    //     const cancelButton = fixture.debugElement.query(By.css('ava-button[label="Cancel"]'));
    //     const confirmButton = fixture.debugElement.query(By.css('ava-button[label="Confirm"]'));

    //     expect(cancelButton).toBeTruthy();
    //     expect(confirmButton).toBeTruthy();
    // });

    // it('should emit close event on close icon click', () => {
    //     component.show = true;
    //     spyOn(component, 'closePopup');
    //     fixture.detectChanges();

    //     const closeIcon = fixture.debugElement.query(By.css('.close-btn'));
    //     closeIcon.nativeElement.click();
    //     expect(component.closePopup).toHaveBeenCalled();
    // });
});
