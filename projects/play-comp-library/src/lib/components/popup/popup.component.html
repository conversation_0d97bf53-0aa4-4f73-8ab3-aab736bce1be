<div class="popup-backdrop" *ngIf="show" role="dialog" [ngClass]="'popup-position-' + position" aria-modal="true"
    [attr.aria-labelledby]="showTitle ? 'popup-title' : null"
    [attr.aria-describedby]="message ? 'popup-description' : null">

    <div class="popup-container" [ngStyle]="{ width: popupWidth }">

        <ava-icon *ngIf="showClose" class="close-btn" [iconName]="closeIconName" [iconSize]="closeIconSize"
            [iconColor]="closeIconColor" [cursor]="true" (click)="closePopup()" role="button" aria-label="Close popup"
            tabindex="0">
        </ava-icon>

        <div *ngIf="showHeaderIcon">
            <ava-icon [iconName]="headerIconName" [iconSize]="iconSize" [iconColor]="iconColor" aria-hidden="true">
            </ava-icon>
        </div>

        <h3 class="popup-title" id="popup-title" *ngIf="showTitle">
            {{ title }}
        </h3>

        <div class="popup-inline-message single-line" *ngIf="showInlineMessage">
            <span class="inline-icon">
                <ava-icon [iconName]="inlineIconName" [iconSize]="inlineIconSize" [iconColor]="inlineIconColor"
                    aria-hidden="true">
                </ava-icon>
            </span>
            <span class="inline-text">{{ inlineMessage }}</span>
        </div>

        <p *ngIf="message" class="popup-message" id="popup-description" [ngClass]="{
    'left-align': messageAlignment === 'left',
    'center-align': messageAlignment === 'center',
    'right-align': messageAlignment === 'right'
  }">
            <ng-container *ngFor="let line of getMessageLines(); let last = last">
                {{ line }}<br *ngIf="!last" />
            </ng-container>
        </p>

        <!-- Slot for custom content -->
        <ng-content></ng-content>

        <div class="popup-actions" *ngIf="showCancel || showConfirm">

            <ava-button *ngIf="showCancel" [label]="cancelButtonLabel" [size]="cancelButtonSize"
                [variant]="cancelButtonVariant" [background]="cancelButtonBackground" (click)="onCancel()">
            </ava-button>

            <ava-button *ngIf="showConfirm" [label]="confirmButtonLabel" [size]="confirmButtonSize"
                [variant]="confirmButtonVariant" [background]="confirmButtonBackground" (click)="onConfirm()">
            </ava-button>

        </div>

    </div>
</div>