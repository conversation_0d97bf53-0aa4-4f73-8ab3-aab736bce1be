<button
  [class]="buttonClasses"
  [ngStyle]="computedStyles"
  [disabled]="disabled"
  [attr.aria-disabled]="disabled"
  [attr.aria-pressed]="isActive"
  (click)="handleClick($event)"
  (keydown)="onKeydown($event)"
  (focus)="onFocus()"
  (blur)="onBlur()"
>
  <div class="button-content">
    <!-- Left Icon (ava-icon or custom SVG) -->
    <ng-container
      *ngIf="hasIcon && (iconPosition === 'left' || iconPosition === 'only')"
    >
      <ava-icon
        class="button-icon"
        [iconName]="iconName"
        [iconColor]="computedIconColor"
        [iconSize]="iconSize"
        [disabled]="disabled"
      ></ava-icon>
    </ng-container>
    <ng-content select="[slot=icon-left]"></ng-content>

    <!-- Button Label -->
    <span *ngIf="iconPosition !== 'only' && label" class="button-label">
      {{ label }}
    </span>

    <!-- Right Icon (ava-icon or custom SVG) -->
    <ng-container *ngIf="hasIcon && iconPosition === 'right'">
      <ava-icon
        class="button-icon"
        [iconName]="iconName"
        [iconColor]="computedIconColor"
        [iconSize]="iconSize"
        [disabled]="disabled"
      ></ava-icon>
    </ng-container>
    <ng-content select="[slot=icon-right]"></ng-content>
  </div>
</button>
