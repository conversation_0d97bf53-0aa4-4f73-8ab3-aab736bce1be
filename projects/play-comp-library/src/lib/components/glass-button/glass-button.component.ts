import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  Output,
  EventEmitter,
  ElementRef,
} from '@angular/core';
import { LucideAngularModule } from 'lucide-angular';
import { IconComponent } from '../icon/icon.component';

export type GlassButtonGlassVariant =
  | 'glass-medium'
  | 'glass-strong'
  | 'glass-bold';

export type GlassButtonSize = 'small' | 'medium' | 'large';
export type GlassButtonHoverEffect =
  | 'torch'
  | 'glow'
  | 'tint'
  | 'scale'
  | 'none';
export type GlassButtonPressedEffect = 'ripple' | 'inset' | 'solid' | 'none';
export type GlassButtonProcessingEffect = 'pulse' | 'none';
export type GlassButtonFocusEffect = 'border' | 'none';
export type GlassButtonDisabledEffect = 'dim' | 'none';
export type GlassButtonTone =
  | 'accent'
  | 'neutral'
  | 'positive'
  | 'negative'
  | 'warning'
  | 'info';

@Component({
  selector: 'ava-glass-button',
  standalone: true,
  imports: [CommonModule, LucideAngularModule, IconComponent],
  templateUrl: './glass-button.component.html',
  styleUrls: ['./glass-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class GlassButtonComponent {
  @Input() label = '';
  @Input() glassVariant: GlassButtonGlassVariant = 'glass-medium';
  @Input() tone?: GlassButtonTone; // Default is undefined, so no tone is applied
  @Input() size: GlassButtonSize = 'large';
  @Input() hoverEffect: GlassButtonHoverEffect = 'torch';
  @Input() pressedEffect: GlassButtonPressedEffect = 'ripple';
  @Input() processingEffect: GlassButtonProcessingEffect = 'pulse';
  @Input() focusEffect: GlassButtonFocusEffect = 'border';
  @Input() disabledEffect: GlassButtonDisabledEffect = 'dim';
  @Input() disabled = false;
  @Input() processing = false;
  @Input() customStyles: Record<string, string> = {};
  @Input() pill = false;
  @Input() width?: string;
  @Input() height?: string;
  @Input() outlined = false;
  @Input() clear = false;
  @Input() iconName = '';
  @Input() iconColor = '';
  @Input() iconSize = 20;
  @Input() iconPosition: 'left' | 'right' | 'only' = 'left';
  @Output() userClick = new EventEmitter<Event>();

  isActive = false;
  timeoutRef: ReturnType<typeof setTimeout> | null = null;

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    this.isActive = false;
  }
  handleClick(event: Event): void {
    if (this.disabled) {
      event.preventDefault();
      return;
    }
    if (this.pressedEffect === 'ripple' && event instanceof MouseEvent) {
      this.createRipple(event);
    }
    this.setActiveState();
    this.userClick.emit(event);
  }
  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (!this.disabled) {
        if (this.pressedEffect === 'ripple') {
          this.createKeyboardRipple();
        }
        this.setActiveState();
        this.userClick.emit(event);
      }
    }
  }
  onFocus(): void {
    console.log('onFocus');
  }
  onBlur(): void {
    console.log('onBlur');
  }
  setActiveState(): void {
    this.isActive = true;
    this.timeoutRef = setTimeout(() => {
      this.isActive = false;
    }, 200);
  }
  createRipple(event: MouseEvent): void {
    const button = event.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();
    const buttonStyle = window.getComputedStyle(button);
    const buttonColor = buttonStyle.color;
    const baseSize = Math.max(rect.width, rect.height);
    const centerX = event.clientX - rect.left;
    const centerY = event.clientY - rect.top;
    const rippleCount = 2;
    const sizeMultipliers = [1.2, 0.9, 0.6];
    const delays = [0, 150, 300];
    const animationClasses = [
      'ava-button-ripple-1',
      'ava-button-ripple-2',
      'ava-button-ripple-3',
    ];
    for (let i = 0; i < rippleCount; i++) {
      setTimeout(() => {
        const size = baseSize * sizeMultipliers[i];
        const x = centerX - size / 2;
        const y = centerY - size / 2;
        const ripple = document.createElement('span');
        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
        ripple.style.setProperty('--ripple-color', buttonColor);
        ripple.classList.add(animationClasses[i]);
        button.appendChild(ripple);
        ripple.addEventListener('animationend', () => {
          ripple.remove();
        });
      }, delays[i]);
    }
  }
  createKeyboardRipple(): void {
    const button = this.elementRef.nativeElement;
    const rect = button.getBoundingClientRect();
    const buttonStyle = window.getComputedStyle(button);
    const buttonColor = buttonStyle.color;
    const baseSize = Math.max(rect.width, rect.height);
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const rippleCount = 3;
    const sizeMultipliers = [1.2, 0.9, 0.6];
    const delays = [0, 150, 300];
    const animationClasses = [
      'ava-button-ripple-1',
      'ava-button-ripple-2',
      'ava-button-ripple-3',
    ];
    for (let i = 0; i < rippleCount; i++) {
      setTimeout(() => {
        const size = baseSize * sizeMultipliers[i];
        const x = centerX - size / 2;
        const y = centerY - size / 2;
        const ripple = document.createElement('span');
        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;
        ripple.style.setProperty('--ripple-color', buttonColor);
        ripple.classList.add(animationClasses[i]);
        button.appendChild(ripple);
        ripple.addEventListener('animationend', () => {
          ripple.remove();
        });
      }, delays[i]);
    }
  }
  get hasIcon(): boolean {
    return !!this.iconName;
  }
  get computedIconColor(): string {
    if (this.disabled) {
      return 'var(--button-icon-color-disabled)';
    }
    if (this.iconColor && this.isValidColor(this.iconColor)) {
      return this.iconColor;
    }
    return 'var(--color-text-on-brand)';
  }
  get buttonClasses(): string {
    const classes = [
      'ava-button',
      this.size,
      `ava-button--${this.glassVariant}`,
      `ava-button--hover-${this.hoverEffect}`,
      `ava-button--pressed-${this.pressedEffect}`,
      `ava-button--focus-${this.focusEffect}`,
      `ava-button--disabled-${this.disabledEffect}`,
    ];
    if (this.processing) {
      classes.push(`ava-button--processing-${this.processingEffect}`);
    }
    if (this.disabled) {
      classes.push('ava-button--disabled');
    }
    if (this.pill) {
      classes.push('ava-button--pill');
    }
    if (this.outlined) {
      classes.push('ava-button--outlined');
    }
    if (this.clear) {
      classes.push('ava-button--clear');
    }
    if (this.isActive) {
      classes.push('ava-button--active');
    }
    if (this.iconPosition === 'only') {
      classes.push('ava-button--icon-only');
    }
    return classes.filter((cls) => cls).join(' ');
  }
  get computedStyles(): Record<string, string> {
    const styles: Record<string, string> = {
      ...(this.width && { width: this.width }),
      ...(this.height && { height: this.height }),
      ...this.customStyles,
    };
    if (this.tone) {
      const intensity = this.glassVariant.replace('glass-', '');
      styles['--glass-bg'] = `var(--glass-${this.tone}-${intensity}-bg)`;
      styles[
        '--button-effect-color'
      ] = `var(--glass-${this.tone}-effect-color)`;
    }
    return styles;
  }
  isValidColor(value: string): boolean {
    const s = new Option().style;
    s.color = value;
    return s.color !== '';
  }
  ngOnDestroy(): void {
    if (this.timeoutRef) {
      clearTimeout(this.timeoutRef);
    }
  }
}
