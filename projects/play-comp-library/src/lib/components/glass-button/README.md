# GlassButton Component

The `GlassButton` component is a Play+ Design System button variant that supports only the glass-medium, glass-strong, and glass-bold glass intensity styles. It is otherwise identical to the standard Button component, including support for icons, shapes, effects, and all sizing options.

## Usage

```html
<ava-glass-button [label]="'Click Me'" [glassVariant]="'glass-strong'" [size]="'large'" [iconName]="'star'" [pill]="true" (userClick)="onClick($event)"></ava-glass-button>
```

## Props

- `label`: string — The button label text.
- `glassVariant`: 'glass-medium' | 'glass-strong' | 'glass-bold' — Glass intensity (required, default: 'glass-medium').
- `size`: 'small' | 'medium' | 'large' — Button size.
- `iconName`: string — Optional icon name.
- `iconColor`: string — Optional icon color.
- `iconSize`: number — Icon size in px.
- `iconPosition`: 'left' | 'right' | 'only' — Icon position.
- `pill`: boolean — Pill-shaped button.
- `outlined`: boolean — Outlined style.
- `clear`: boolean — Clear style.
- `disabled`: boolean — Disabled state.
- `processing`: boolean — Processing/loading state.
- `hoverEffect`, `pressedEffect`, `processingEffect`, `focusEffect`, `disabledEffect`: Effect system props.
- `customStyles`: Record<string, string> — Custom CSS properties.
- `width`, `height`: string — Custom width/height.
- `userClick`: EventEmitter<Event> — Emits on click.

## Notes

- All sizing, icon, and effect props are supported as in the standard Button.
- Only glass-medium, glass-strong, and glass-bold are allowed for `glassVariant`.
- All color and style tokens are inherited from the design system.
