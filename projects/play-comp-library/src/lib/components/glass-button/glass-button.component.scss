/* Remove example tone variables, as they are now set in @_button.css */

/* GlassButton SCSS: Only glass-50, glass-75, glass-100 variants. Based on button.component.scss */

.ava-button {
  --button-torch-color: var(--button-default-torch-color);
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  gap: var(--button-gap);
  backdrop-filter: var(--button-glass-default-backdrop-filter);
  -webkit-backdrop-filter: var(--button-glass-default-backdrop-filter);
  border: var(--button-glass-default-border);
  background: var(--button-glass-default-background);
  box-shadow: var(--button-glass-default-shadow);
  border-radius: var(--button-border-radius);
  cursor: var(--button-cursor);
  font-family: var(--button-font);
  font-weight: var(--button-font-weight);
  line-height: var(--button-line-height);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
  overflow: hidden;
  box-sizing: border-box;
  color: inherit;
  text-decoration: none;
  user-select: none;
  z-index: 0;

  &.small {
    padding: var(--button-size-sm-padding);
    font-size: var(--button-size-sm-font);
    height: var(--button-size-sm-height);
    min-width: var(--button-size-sm-min-width);
    .button-icon {
      width: var(--button-icon-size-sm);
      height: var(--button-icon-size-sm);
    }
  }
  &.medium {
    padding: var(--button-size-md-padding);
    font-size: var(--button-size-md-font);
    height: var(--button-size-md-height);
    min-width: var(--button-size-md-min-width);
    .button-icon {
      width: var(--button-icon-size-md);
      height: var(--button-icon-size-md);
    }
  }
  &.large {
    padding: var(--button-size-lg-padding);
    font-size: var(--button-size-lg-font);
    height: var(--button-size-lg-height);
    min-width: var(--button-size-lg-min-width);
    .button-icon {
      width: var(--button-icon-size-lg);
      height: var(--button-icon-size-lg);
    }
  }

  // Glass-50: Medium glass intensity
  &.ava-button--glass-medium {
    color: var(--button-text-on-color-primary);
    backdrop-filter: var(--button-glass-50-backdrop-filter);
    -webkit-backdrop-filter: var(--button-glass-50-backdrop-filter);
    border: var(--button-glass-50-border);
    box-shadow: var(--button-glass-50-shadow);
    background: var(--glass-bg) !important;
  }
  // Glass-75: Strong glass intensity
  &.ava-button--glass-strong {
    color: var(--button-text-on-color-primary);
    backdrop-filter: var(--button-glass-75-backdrop-filter);
    -webkit-backdrop-filter: var(--button-glass-75-backdrop-filter);
    border: var(--button-glass-75-border);
    box-shadow: var(--button-glass-75-shadow);
    background: var(--glass-bg) !important;
  }
  // Glass-100: Maximum glass intensity
  &.ava-button--glass-bold {
    color: var(--button-text-on-color-primary);
    backdrop-filter: var(--button-glass-100-backdrop-filter);
    -webkit-backdrop-filter: var(--button-glass-100-backdrop-filter);
    border: var(--button-glass-100-border);
    box-shadow: var(--button-glass-100-shadow);
    background: var(--glass-bg) !important;
  }

  // Hover Effects
  &.ava-button--hover-torch {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 2px;
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 120%;
      height: 80%;
      border-radius: 50%;
      transform: translateX(-50%);
      transition: opacity 0.8s cubic-bezier(0.25,0.8,0.25,1),filter 0.8s cubic-bezier(0.25,0.8,0.25,1);
      z-index: 10;
      opacity: 0;
      pointer-events: none;
      background: radial-gradient(ellipse at 50% 100%,currentColor 0%,rgba(255, 255, 255, 0.863) 20%,rgba(255,255,255,0.18) 40%,rgba(255,255,255,0.04) 80%,transparent 100%);
      filter: blur(16px) brightness(1.18) saturate(1.08);
      mix-blend-mode: lighten;
    }
    &:hover:not(:disabled):not(.ava-button--disabled):not(:active) {
      transform: scale(1.02);
      &::before {
        opacity: 1;
        filter: blur(18px) brightness(1.22) saturate(1.12);
      }
    }
    &:active:not(:disabled):not(.ava-button--disabled) {
      &::before {
        opacity: 0;
        transition: opacity 0.2s ease-out;
      }
    }
  }
  &.ava-button--hover-glow {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 8px;
    &:hover:not(:disabled):not(.ava-button--disabled) {
      transform: translateY(-2px) scale(1.02);
      box-shadow: 0 0 30px rgba(var(--button-effect-color), 0.5), 0 0 60px rgba(var(--button-effect-color), 0.2);
    }
  }
  &.ava-button--hover-tint {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 2px;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(var(--button-effect-color), 0);
      border-radius: inherit;
      pointer-events: none;
      transition: background 0.3s ease-out;
    }
    &:hover:not(:disabled):not(.ava-button--disabled) {
      filter: brightness(1.15);
      transform: translateY(-1px) scale(1.02);
      &::after {
        background: rgba(var(--button-effect-color), 0.15);
      }
    }
  }
  &.ava-button--hover-scale {
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    transform-origin: center center;
    margin: 4px;
    &:hover:not(:disabled):not(.ava-button--disabled) {
      transform: scale(1.05) translateY(-2px);
    }
  }
  // Pressed Effects
  &.ava-button--pressed-ripple {
    position: relative;
    overflow: hidden;
    margin: 2px;
    &:active:not(:disabled):not(.ava-button--disabled) {
      transform: scale(0.98);
      transition: transform 0.1s ease-out;
    }
  }
  &.ava-button--pressed-inset {
    margin: 2px;
    &:active:not(:disabled):not(.ava-button--disabled) {
      box-shadow: inset 0 4px 8px rgba(var(--button-effect-color), 0.3), inset 0 0 12px rgba(var(--button-effect-color), 0.1);
      transform: scale(0.97) translateY(1px);
      transition: all 0.15s cubic-bezier(0.4, 0, 0.6, 1);
    }
  }
  &.ava-button--pressed-solid {
    margin: 2px;
    &:active:not(:disabled):not(.ava-button--disabled) {
      background: rgb(var(--button-effect-color)) !important;
      transform: scale(0.94);
      transition: all 0.15s cubic-bezier(0.4, 0, 0.6, 1);
    }
  }

  &.ava-button--pill {
    border-radius: 50px;
  }

  &.ava-button--icon-only {
    aspect-ratio: 1;
    padding: var(--button-icon-margin);
    min-width: auto;
  }

  .button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--global-spacing-2);
    position: relative;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;
  }

  .button-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--button-icon-color);
  }

  .button-label {
    font-weight: inherit;
    line-height: inherit;
  }
}

// Copy ripple and animation keyframes as in button.component.scss

@keyframes ava-button-processing-pulse {
  0%, 100% {
    box-shadow: 
      var(--button-glass-default-shadow,var(--glass-25-shadow)),
      0 0 0 0 rgba(var(--button-effect-color), 0);
  }
  50% {
    box-shadow: 
      var(--button-glass-default-shadow,var(--glass-25-shadow)),
      0 0 0 8px rgba(var(--button-effect-color), 0.3);
  }
}

%ripple-base {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  transform-origin: center;
}

.ava-button-ripple-1 {
  @extend %ripple-base;
  background-color: var(--ripple-color, rgba(255, 255, 255, 0.4));
  opacity: 0.4;
  animation: ava-button-ripple-animation-1 1.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.ava-button-ripple-2 {
  @extend %ripple-base;
  background-color: var(--ripple-color, rgba(255, 255, 255, 0.3));
  opacity: 0.3;
  animation: ava-button-ripple-animation-2 1.4s cubic-bezier(0.4, 0, 0.2, 1);
}
.ava-button-ripple-3 {
  @extend %ripple-base;
  background-color: var(--ripple-color, rgba(255, 255, 255, 0.2));
  opacity: 0.2;
  animation: ava-button-ripple-animation-3 1.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes ava-button-ripple-animation-1 {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    opacity: 0.4;
  }
  70% {
    opacity: 0.2;
  }
  100% {
    transform: scale(3.5);
    opacity: 0;
  }
}
@keyframes ava-button-ripple-animation-2 {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  25% {
    opacity: 0.3;
  }
  65% {
    opacity: 0.15;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}
@keyframes ava-button-ripple-animation-3 {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  30% {
    opacity: 0.2;
  }
  60% {
    opacity: 0.1;
  }
  100% {
    transform: scale(4.5);
    opacity: 0;
  }
} 