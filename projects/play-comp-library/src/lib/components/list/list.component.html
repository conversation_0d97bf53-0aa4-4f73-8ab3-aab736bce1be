<!-- Error Message - Top Position -->
<div class="list-error-message error-top" 
     *ngIf="hasError && errorPosition === 'top'">
  <span class="error-text">{{ errorMessage }}</span>
</div>

<div class="ava-list-container"      
     [class.multi-select]="multiSelect"      
     [class.disabled]="disabled"
     [class.has-error]="hasError"      
     [style.height]="height"      
     [style.width]="width">      
  
  <!-- List Title -->   
  <div class="ava-list-title" *ngIf="title">{{ title }}</div>      
  
  <!-- List Items -->   
  <div class="ava-list-items" *ngIf="items.length > 0">     
    <div class="ava-list-item"          
         *ngFor="let item of items; trackBy: trackByFn"          
         [class.selected]="isItemSelected(item)"          
         [class.disabled]="item.disabled || disabled"          
         [class.has-checkbox]="showCheckboxes && multiSelect"          
         [class.clickable]="!item.disabled && !disabled"          
         (click)="onItemClick(item, $event)">              
      
      <!-- Checkbox for multi-select -->       
      <div class="item-checkbox"             
           *ngIf="showCheckboxes && multiSelect">                
        <ava-checkbox                
             class="list-checkbox"                     
             [isChecked]="isItemSelected(item)"                     
             (change)="onCheckboxChange(item, $event)">
        </ava-checkbox>       
      </div>              
      
      <!-- Avatar -->       
      <div class="item-avatar" *ngIf="hasAvatar(item)">         
        <ava-avatars           
             [size]="item.avatar!.size || 'medium'"           
             [shape]="item.avatar!.shape || 'pill'"           
             [imageUrl]="item.avatar!.imageUrl || ''"           
             [statusText]="item.avatar!.statusText"           
             [profileText]="item.avatar!.profileText"           
             [badgeState]="item.avatar!.badgeState"           
             [badgeSize]="item.avatar!.badgeSize"           
             [badgeCount]="item.avatar!.badgeCount"           
             [active]="item.avatar!.active || false"           
             [processedanddone]="item.avatar!.processedanddone || false">         
        </ava-avatars>       
      </div>              
      
      <!-- Icon -->       
      <div class="item-icon"            
           *ngIf="hasIcon(item)"            
           [class.clickable]="isIconClickable(item)"            
           (click)="isIconClickable(item) ? onItemIconClick(item, $event) : null">         
        <ava-icon           
             [iconName]="item.icon!.iconName"           
             [color]="item.icon!.color || ''"           
             [iconColor]="item.icon!.iconColor || '#a1a1a1'"           
             [iconSize]="item.icon!.iconSize || 24"           
             [disabled]="item.icon!.disabled || item.disabled || disabled"           
             [cursor]="item.icon!.cursor || false">         
        </ava-icon>       
      </div>              
      
      <!-- Content -->       
      <div class="item-content">         
        <div class="item-title">{{ item.title }}</div>         
        <div class="item-subtitle" *ngIf="item.subtitle">{{ item.subtitle }}</div>       
      </div>              
      
      <!-- Buttons Array -->       
      <div class="item-buttons" *ngIf="hasButtons(item)">         
        <ava-button           
             *ngFor="let button of item.buttons; trackBy: trackByButtonFn; let i = index"           
             [label]="button.label || ''"           
             [variant]="button.variant || 'default'"           
             [size]="button.size || 'medium'"           
             [iconName]="button.iconName || ''"           
             [iconColor]="button.iconColor || ''"           
             [iconSize]="button.iconSize || 20"           
             [iconPosition]="button.iconPosition || 'left'"           
             [disabled]="button.disabled || item.disabled || disabled"           
             [processing]="button.processing || false"           
             [pill]="button.pill || false"           
             [width]="button.width"           
             [height]="button.height"           
             (userClick)="onItemButtonClick(item, button, i, $event)">         
        </ava-button>       
      </div>     
    </div>        
  </div>      
  
  <!-- Empty State -->   
  <div class="empty-list-item" 
       *ngIf="items.length === 0" 
       [style.height]="height" 
       [style.width]="width">     
    <div>       
      <span class="no-data">{{emptyLabel}}</span>     
    </div>   
  </div>  
</div>

<!-- Error Message - Bottom Position -->
<div class="list-error-message error-bottom" 
     *ngIf="hasError && errorPosition === 'bottom'">
  <span class="error-text">{{ errorMessage }}</span>
</div>