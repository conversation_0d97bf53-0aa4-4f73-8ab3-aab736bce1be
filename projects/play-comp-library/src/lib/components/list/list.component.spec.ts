// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { ListComponent, ListItem } from './list.component';
// import { By } from '@angular/platform-browser';

// describe('ListComponent', () => {
//   let component: ListComponent;
//   let fixture: ComponentFixture<ListComponent>;

//   const mockItems: ListItem[] = [
//     { id: '1', title: 'Item 1', subtitle: 'Sub 1', icon: 'icon1.png' },
//     { id: '2', title: 'Item 2' },
//     { id: '3', title: 'Item 3', icon: 'icon3.png' }
//   ];

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [ListComponent]
//     }).compileComponents();

//     fixture = TestBed.createComponent(ListComponent);
//     component = fixture.componentInstance;
//   });

//   it('should create the component', () => {
//     expect(component).toBeTruthy();
//   });

//   describe('Inputs and rendering', () => {
//     it('should display title if provided', () => {
//       component.title = 'List Header';
//       fixture.detectChanges();

//       const titleEl = fixture.debugElement.query(By.css('.ava-list-title'));
//       expect(titleEl.nativeElement.textContent).toContain('List Header');
//     });

//     it('should not display title if not provided', () => {
//       component.title = '';
//       fixture.detectChanges();

//       const titleEl = fixture.debugElement.query(By.css('.ava-list-title'));
//       expect(titleEl).toBeNull();
//     });

//     it('should apply height and width styles', () => {
//       component.height = '300px';
//       component.width = '50%';
//       fixture.detectChanges();

//       const container = fixture.debugElement.query(By.css('.ava-list-container'));
//       expect(container.styles['height']).toBe('300px');
//       expect(container.styles['width']).toBe('50%');
//     });

//     it('should render all list items', () => {
//       component.items = mockItems;
//       fixture.detectChanges();

//       const itemEls = fixture.debugElement.queryAll(By.css('.ava-list-item'));
//       expect(itemEls.length).toBe(3);
//     });

//     it('should render item icon if provided', () => {
//       component.items = [mockItems[0]];
//       fixture.detectChanges();

//       const imgEl = fixture.debugElement.query(By.css('.item-icon img'));
//       expect(imgEl).toBeTruthy();
//       expect(imgEl.attributes['src']).toBe('icon1.png');
//       expect(imgEl.attributes['alt']).toBe('Item 1');
//     });

//     it('should render subtitle if provided', () => {
//       component.items = [mockItems[0]];
//       fixture.detectChanges();

//       const subtitleEl = fixture.debugElement.query(By.css('.item-subtitle'));
//       expect(subtitleEl.nativeElement.textContent).toContain('Sub 1');
//     });

//     it('should not render subtitle if not provided', () => {
//       component.items = [mockItems[1]];
//       fixture.detectChanges();

//       const subtitleEl = fixture.debugElement.query(By.css('.item-subtitle'));
//       expect(subtitleEl).toBeNull();
//     });
//   });

//   describe('trackByFn()', () => {
//     it('should return item.id', () => {
//       const result = component.trackByFn(0, mockItems[0]);
//       expect(result).toBe('1');
//     });
//   });

//   describe('onItemClick()', () => {
//     it('should emit the clicked item', () => {
//       spyOn(component.onOptionSelected, 'emit');
//       const item = mockItems[1];

//       component.onItemClick(item);
//       expect(component.onOptionSelected.emit).toHaveBeenCalledWith(item);
//     });

//     it('should emit item on clicking DOM element', () => {
//       component.items = mockItems;
//       fixture.detectChanges();

//       spyOn(component, 'onItemClick').and.callThrough();
//       spyOn(component.onOptionSelected, 'emit');

//       const itemEls = fixture.debugElement.queryAll(By.css('.ava-list-item'));
//       itemEls[1].nativeElement.click();

//       expect(component.onItemClick).toHaveBeenCalledWith(mockItems[1]);
//       expect(component.onOptionSelected.emit).toHaveBeenCalledWith(mockItems[1]);
//     });
//   });

//   describe('isItemActive()', () => {
//     it('should return true for selected item', () => {
//       component.selectedItemId = '2';
//       const result = component.isItemActive(mockItems[1]);
//       expect(result).toBeTrue();
//     });

//     it('should return false for non-selected item', () => {
//       component.selectedItemId = '1';
//       const result = component.isItemActive(mockItems[2]);
//       expect(result).toBeFalse();
//     });

//     it('should add "active" class to selected item in template', () => {
//       component.items = mockItems;
//       component.selectedItemId = '3';
//       fixture.detectChanges();

//       const activeItem = fixture.debugElement.query(By.css('.ava-list-item.active'));
//       expect(activeItem.nativeElement.textContent).toContain('Item 3');
//     });
//   });
// });
