// CSS Custom Properties
$list-container-border-radius: var(--list-container-border-radius);
$list-container-padding: var(--list-container-padding);
$list-container-gap: var(--list-container-gap);
$list-title-color: var(--list-title-color);
$list-title-size: var(--list-title-size);
$list-title-weight: var(--list-title-weight);
$list-subtitle-weight: var(--list-subtitle-weight);
$list-active-bg: var(--list-active-bg);
$list-border-color: var(--list-item-border-color);
$list-container-border: var(--list-container-border);
$list-item-bg: var(--list-item-background);
$list-item-active-border: var(--list-item-active-border);
$list-item-subtitle-color: var(--list-item-subtitle-color);
$list-title-font-family: var(--list-title-font-family);
$list-item-color: var(--list-item-color);
$list-items-gap: var(--list-items-gap);
$list-item-gap:var(--list-item-gap);
$list-item-border-radius:var(--list-item-border-radius);
$list-buttons-gap:var(--list-buttons-gap);
$list-item-padding:var(--list-item-padding);
$list-background-color:var(--list-background-color);
$list-disable-color:var(--list-disable-color);

// Error message variables
$list-error-color: var(--list-error-text);
$list-error-text-size: var(--list-error-font-size);

// Animation keyframes
@keyframes click-press {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes error-shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}

// Error message styles
.list-error-message {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 4px 0;
  transition: all 0.3s ease;
  animation: error-shake 0.5s ease-in-out;
  
  &.error-top {
    margin-bottom: 8px;
  }
  
  &.error-bottom {
    margin-top: 8px;
  }
  
  .error-text {
    font-size: $list-error-text-size;
    color: $list-error-color;
    line-height: 1.4;
    margin: 0;
  }
}

.ava-list-container {
  display: flex;
  padding: $list-container-padding;
  flex-direction: column;
  align-items: flex-start;
  gap: $list-container-gap;
  flex: 1 0 0;
  border-radius: $list-container-border-radius;
  border: 1px solid $list-container-border;
  overflow: hidden;
  transition: border-color 0.3s ease;
  
  &.empty-list-item {
    align-items: center;
    justify-content: center;

    span {
      font-size: var(--list-title-size);
    }
  }
  
  &.multi-select {
    .ava-list-item {
      user-select: none;
    }
  }
  
 
}

.ava-list-title {
  color: $list-title-color;
  font-family: $list-title-font-family;
  font-size: $list-title-size;
  font-weight: $list-title-weight;
  letter-spacing: -0.44px;
  align-self: stretch;
}

.ava-list-items {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: $list-items-gap;
  align-self: stretch;
  flex: 1 0 0;
  overflow-y: auto;
}

.ava-list-item {
  display: flex;
  padding: $list-item-padding;
  align-items: center;
  gap: $list-item-gap;
  align-self: stretch;
  border-radius: $list-item-border-radius;
  border: 1px solid $list-border-color;
  background: $list-item-bg;
  transition: all 0.2s ease-in-out;
  position: relative;
  
  // Click animation
  &.clickable:active {
    animation: click-press 0.15s ease-in-out;
  }

  &.clickable {
    cursor: pointer;
    border-radius: 4px;
  }

  &.selected {
    border: $list-item-active-border;
    background: $list-active-bg;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  // Hover animation for active color
  &.clickable:not(.disabled):hover {
    background: color-mix(in srgb, $list-active-bg 80%, white);
    transition: background 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.item-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  &.clickable {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
  }
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.item-title {
  font-size: $list-subtitle-weight;
  color: $list-item-color;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: $list-title-font-family;
  letter-spacing: -0.352px;
  font-weight: $list-title-weight;
}

.item-subtitle {
  font-size: 14px;
  color: $list-item-subtitle-color;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: $list-title-font-family;
}

.item-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  gap: $list-buttons-gap;
}

.empty-list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  text-align: center;

  .no-data {
    font-size: var(--list-title-size);
    color: $list-disable-color; 
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .ava-list-item {
    gap: calc(#{$list-items-gap} - 2px);
    
    .item-title {
      font-size: calc(#{$list-subtitle-weight} - 1px);
    }
    
    .item-subtitle {
      font-size: calc(#{$list-subtitle-weight} - 2px);
    }
    
    // Reduce animation intensity on mobile
    &.clickable:active {
      animation: click-press 0.15s ease-in-out;
    }
  }
  
  .item-buttons {
    gap: 6px;
  }
  
  // Mobile error message adjustments
  .list-error-message {
    padding: 6px 10px;
    margin: 2px 0;
    
    .error-text {
      font-size: calc(#{$list-error-text-size} - 1px);
    }
  }
}

.item-checkbox {
  ava-checkbox {
    &.list-checkbox {
      .checkbox {
        margin-right: 4px;
      }
    }
  }
}