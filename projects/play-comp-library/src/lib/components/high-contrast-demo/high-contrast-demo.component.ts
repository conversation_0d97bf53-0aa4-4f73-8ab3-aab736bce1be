import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'ava-high-contrast-demo',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="high-contrast-demo">
      <div class="demo-header">
        <h2>High Contrast Mode Demo</h2>
        <p>
          This component demonstrates Play+ High Contrast Mode functionality.
        </p>
      </div>

      <div class="demo-section">
        <h3>System Detection</h3>
        <div class="status-indicator">
          <span class="label">High Contrast Mode:</span>
          <span class="value" [class.active]="isHighContrastActive">
            {{ isHighContrastActive ? 'Active' : 'Inactive' }}
          </span>
        </div>
        <p class="description">
          High Contrast Mode is automatically detected from your operating
          system settings.
        </p>
      </div>

      <div class="demo-section">
        <h3>Component Examples</h3>
        <div class="component-grid">
          <div class="component-example">
            <h4>Buttons</h4>
            <button class="btn-primary">Primary Button</button>
            <button class="btn-secondary">Secondary Button</button>
            <button class="btn-disabled" disabled>Disabled Button</button>
          </div>

          <div class="component-example">
            <h4>Form Elements</h4>
            <input type="text" placeholder="Text input" />
            <select>
              <option>Select option</option>
              <option>Option 1</option>
              <option>Option 2</option>
            </select>
            <textarea placeholder="Textarea input"></textarea>
          </div>

          <div class="component-example">
            <h4>Interactive Elements</h4>
            <a href="#" class="link">Sample Link</a>
            <div class="card">
              <h5>Sample Card</h5>
              <p>This is a sample card component.</p>
            </div>
          </div>

          <div class="component-example">
            <h4>Focus Indicators</h4>
            <button class="focus-demo" tabindex="0">Focus Me</button>
            <input type="text" placeholder="Focus this input" />
            <a href="#" tabindex="0">Focus this link</a>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>Testing Instructions</h3>
        <div class="testing-instructions">
          <div class="instruction">
            <h4>Windows</h4>
            <ol>
              <li>Open Settings > Ease of Access > High contrast</li>
              <li>Turn on "Turn on high contrast"</li>
              <li>Refresh this page to see the changes</li>
            </ol>
          </div>

          <div class="instruction">
            <h4>macOS</h4>
            <ol>
              <li>Open System Preferences > Accessibility > Display</li>
              <li>Check "Increase contrast"</li>
              <li>Refresh this page to see the changes</li>
            </ol>
          </div>

          <div class="instruction">
            <h4>Browser Testing</h4>
            <ol>
              <li>Open Developer Tools (F12)</li>
              <li>Go to Rendering tab</li>
              <li>Under "Emulate CSS media feature prefers-contrast"</li>
              <li>Select "more" to simulate high contrast mode</li>
            </ol>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>Accessibility Features</h3>
        <ul class="accessibility-features">
          <li>✅ Automatic system preference detection</li>
          <li>✅ Simplified color palette (black/white)</li>
          <li>✅ Enhanced focus indicators (3px solid outline)</li>
          <li>✅ Removed decorative effects (shadows, gradients)</li>
          <li>✅ Structural borders for element separation</li>
          <li>✅ Legacy browser support (IE/Edge)</li>
        </ul>
      </div>
    </div>
  `,
  styles: [
    `
      .high-contrast-demo {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
        font-family: var(--font-family-body);
      }

      .demo-header {
        text-align: center;
        margin-bottom: 3rem;
      }

      .demo-header h2 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
      }

      .demo-header p {
        color: var(--color-text-secondary);
        font-size: 1.1rem;
      }

      .demo-section {
        margin-bottom: 3rem;
        padding: 2rem;
        border: 1px solid var(--color-border-default);
        border-radius: 8px;
        background-color: var(--color-background-secondary);
      }

      .demo-section h3 {
        color: var(--color-text-primary);
        margin-bottom: 1rem;
        border-bottom: 2px solid var(--color-border-default);
        padding-bottom: 0.5rem;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .status-indicator .label {
        font-weight: bold;
        color: var(--color-text-primary);
      }

      .status-indicator .value {
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-weight: bold;
        background-color: var(--color-surface-subtle);
        border: 1px solid var(--color-border-default);
      }

      .status-indicator .value.active {
        background-color: var(--color-surface-interactive-success);
        color: var(--color-text-on-brand);
      }

      .description {
        color: var(--color-text-secondary);
        font-style: italic;
      }

      .component-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
      }

      .component-example {
        padding: 1.5rem;
        border: 1px solid var(--color-border-subtle);
        border-radius: 6px;
        background-color: var(--color-background-primary);
      }

      .component-example h4 {
        color: var(--color-text-primary);
        margin-bottom: 1rem;
        font-size: 1.1rem;
      }

      .component-example > * {
        margin-bottom: 0.75rem;
      }

      .component-example > *:last-child {
        margin-bottom: 0;
      }

      /* Button Styles */
      .btn-primary {
        background-color: var(--color-surface-interactive-primary);
        color: var(--color-text-on-brand);
        border: 1px solid var(--color-border-primary);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
      }

      .btn-secondary {
        background-color: var(--color-surface-interactive-secondary);
        color: var(--color-text-on-secondary);
        border: 1px solid var(--color-border-secondary);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
      }

      .btn-disabled {
        background-color: var(--color-surface-disabled);
        color: var(--color-text-disabled);
        border: 1px solid var(--color-border-disabled);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: not-allowed;
        font-weight: 500;
      }

      /* Form Elements */
      input,
      select,
      textarea {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--color-border-default);
        border-radius: 4px;
        background-color: var(--color-background-primary);
        color: var(--color-text-primary);
        font-family: inherit;
      }

      textarea {
        min-height: 80px;
        resize: vertical;
      }

      /* Link Styles */
      .link {
        color: var(--color-text-interactive);
        text-decoration: underline;
        font-weight: 500;
      }

      /* Card Styles */
      .card {
        padding: 1rem;
        border: 1px solid var(--color-border-default);
        border-radius: 6px;
        background-color: var(--color-background-primary);
      }

      .card h5 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
      }

      .card p {
        color: var(--color-text-secondary);
        margin: 0;
      }

      /* Focus Demo */
      .focus-demo {
        background-color: var(--color-surface-interactive-default);
        color: var(--color-text-on-brand);
        border: 1px solid var(--color-border-interactive);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
      }

      /* Testing Instructions */
      .testing-instructions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .instruction h4 {
        color: var(--color-text-primary);
        margin-bottom: 1rem;
      }

      .instruction ol {
        color: var(--color-text-secondary);
        padding-left: 1.5rem;
      }

      .instruction li {
        margin-bottom: 0.5rem;
      }

      /* Accessibility Features */
      .accessibility-features {
        list-style: none;
        padding: 0;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
      }

      .accessibility-features li {
        color: var(--color-text-primary);
        padding: 0.5rem;
        border: 1px solid var(--color-border-subtle);
        border-radius: 4px;
        background-color: var(--color-surface-subtle);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .high-contrast-demo {
          padding: 1rem;
        }

        .component-grid {
          grid-template-columns: 1fr;
        }

        .testing-instructions {
          grid-template-columns: 1fr;
        }

        .accessibility-features {
          grid-template-columns: 1fr;
        }
      }
    `,
  ],
})
export class HighContrastDemoComponent implements OnInit, OnDestroy {
  isHighContrastActive = false;
  private mediaQuery: MediaQueryList | null = null;

  ngOnInit() {
    this.checkHighContrastMode();
    this.setupMediaQueryListener();
  }

  ngOnDestroy() {
    if (this.mediaQuery) {
      this.mediaQuery.removeEventListener(
        'change',
        this.handleMediaQueryChange
      );
    }
  }

  private checkHighContrastMode() {
    // Check for modern browsers
    if (window.matchMedia) {
      this.mediaQuery = window.matchMedia('(prefers-contrast: more)');
      this.isHighContrastActive = this.mediaQuery.matches;
    }

    // Check for legacy browsers (IE/Edge)
    if (!this.isHighContrastActive && window.matchMedia) {
      const legacyQuery = window.matchMedia('(-ms-high-contrast: active)');
      this.isHighContrastActive = legacyQuery.matches;
    }
  }

  private setupMediaQueryListener() {
    if (this.mediaQuery) {
      this.mediaQuery.addEventListener('change', this.handleMediaQueryChange);
    }
  }

  private handleMediaQueryChange = (event: MediaQueryListEvent) => {
    this.isHighContrastActive = event.matches;
  };
}
