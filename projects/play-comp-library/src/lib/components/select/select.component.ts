import {
  Component, ContentChildren, QueryList, AfterContentInit,
  forwardRef, Input, HostListener, ChangeDetectionStrategy,
  ChangeDetectorRef,
  Output,
  EventEmitter,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { SelectOptionComponent } from './select-option/select-option.component';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
export type SelectboxSize = 'xs' | 'sm' | 'md' | 'lg';
@Component({
  selector: 'ava-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss'],
  imports: [CommonModule, IconComponent],
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => SelectComponent),
    multi: true
  }],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None

})
export class SelectComponent implements AfterContentInit, ControlValueAccessor {
  private valueSetFromOutside = false;
  private focusTimeoutId: any;

  @ViewChild('inputSearchElement') inputRef!: ElementRef<HTMLInputElement>;
  @Input() placeholder = 'Select';
  @Input() label = '';
  @Input() id = '';
  @Input() required = false;
  @Input() dropdownIcon: string = 'chevron-down';
  @Input() error = '';
  @Input() disabled = false;
  @Input() multiple: boolean = false;
  @Input() size: SelectboxSize = 'md';
  @Input() showSearch = true;

  displayText = this.placeholder;
  searchTerm: string = '';

  filteredOptions: SelectOptionComponent[] = [];
  hasSearchedOptions = true;
  highlightedIndex = -1; // Track highlighted option for keyboard navigation

  @ContentChildren(SelectOptionComponent) options!: QueryList<SelectOptionComponent>;

  selectedValue: any[] = [];
  isOpen = false;
  private _inputId!: string;
  hasValue = true;
  @Output() selectionChange = new EventEmitter<any>();
  constructor(private cdr: ChangeDetectorRef) { }

  onChange = (_: any) => { };
  onTouched = () => { };
  ngOnInit(): void {
    this._inputId = `ava-select-${Math.random().toString(36).substr(2, 9)}`;
  }

  ngAfterContentInit(): void {
    Promise.resolve().then(() => {
      this.options.forEach(option => {
        option.optionSelected.subscribe((val: any) => {
          this.selectValue(val);
        });

        // Initialize visibility to true for all options
        if (option.visible === undefined) {
          option.visible = true;
        }
      });
      // Only apply [selected] fallback if no ngModel/formControl has set a value
      if (!this.valueSetFromOutside) {
        const defaultSelectedOptions = this.options.filter(opt => opt.selected);
        if (defaultSelectedOptions.length > 0) {
          if (this.multiple) {
            this.selectedValue = defaultSelectedOptions.map(opt => opt.value);
            this.onChange(this.selectedValue);
          } else {
            this.selectedValue = [defaultSelectedOptions[0].value];

            // Clear all selections first
            this.options.forEach(option => {
              option.selected = false;
            });

            // Set only the first one as selected
            defaultSelectedOptions[0].selected = true;
            this.onChange(defaultSelectedOptions[0].value);
          }
        }
      }
      this.updateSelectedStates();
      this.filteredOptions = this.options.toArray();
      this.cdr.markForCheck();
    });

  }
  focus(): void {
    this.clearFocusTimeout(); // clear any previous timeout
    this.focusTimeoutId = setTimeout(() => {
      this.inputRef?.nativeElement?.focus();
      this.focusTimeoutId = null; // clear after use
    });
  }

  clearFocusTimeout(): void {
    if (this.focusTimeoutId) {
      clearTimeout(this.focusTimeoutId);
      this.focusTimeoutId = null;
    }
  }
  toggleDropdown() {
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.highlightedIndex = this.getSelectedOptionIndex();
    } else {
      this.highlightedIndex = -1;
    }
    this.updateHighlightedStates();
    this.focus();
  }

  selectValue(val: any) {
    if (this.multiple) {
      const exists = this.selectedValue.some(v => this.compare(v, val));
      if (exists) {
        this.selectedValue = this.selectedValue.filter(v => !this.compare(v, val));
      } else {
        this.selectedValue = [...this.selectedValue, val];
      }
      this.markSelectedMultiple();
      this.onChange(this.selectedValue);
      this.selectionChange.emit(this.selectedValue);
    } else {

      this.selectedValue = [val];
      this.markSelected(val);
      this.onChange(val);
      this.selectionChange.emit(val);
      this.isOpen = false;
    }

    this.onTouched();
    this.cdr.markForCheck();
  }
  private compare(a: any, b: any): boolean {
    return a === b;
  }

  writeValue(val: any): void {
    this.valueSetFromOutside = true;
    if (this.multiple) {
      this.selectedValue = Array.isArray(val) ? val : [];
      this.markSelectedMultiple();
    } else {
      this.selectedValue = val ? [val] : [];
      this.markSelected(val);
    }
    this.cdr.markForCheck();
  }

  private markSelectedMultiple() {
    this.options?.forEach(option => {
      option.selected = this.selectedValue.some(val => this.compare(val, option.value));
    });
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  private markSelected(val: any) {
    this.options?.forEach(option => {
      option.selected = option.value === val;
    });
  }

  @HostListener('document:click', ['$event'])
  onOutsideClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('ava-select')) {
      this.isOpen = false;
      this.highlightedIndex = -1;
    }
  }

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    if (!this.isOpen) {
      // Open dropdown with Enter or Space when closed
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        this.toggleDropdown();
        return;
      }
      // Open dropdown and navigate with arrow keys
      if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        event.preventDefault();
        this.isOpen = true;
        this.highlightedIndex = event.key === 'ArrowDown' ? 0 : this.getVisibleOptionsCount() - 1;
        this.updateHighlightedStates();
        this.focus();
        return;
      }
    }

    if (this.isOpen) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          this.navigateDown();
          break;
        case 'ArrowUp':
          event.preventDefault();
          this.navigateUp();
          break;
        case 'Enter':
          event.preventDefault();
          this.selectHighlightedOption();
          break;
        case 'Escape':
          event.preventDefault();
          this.closeDropdown();
          break;
        case 'Tab':
          this.closeDropdown();
          break;
      }
    }
  }


  getDisplayText(): string {
    this.hasValue = true;
    if (this.multiple && this.selectedValue.length) {
      return this.options
        .filter(option => this.selectedValue.includes(option.value))
        .map(option => option.elementRef.nativeElement.textContent.trim())
        .join(', ');
    }

    if (!this.multiple && this.selectedValue.length) {
      const selected = this.options.find(opt => opt.value === this.selectedValue[0]);
      return selected?.elementRef.nativeElement.textContent.trim() || this.placeholder;
    }
    this.hasValue = false;
    return this.placeholder;
  }


  private updateSelectedStates() {
    if (!this.options) return;
    if (this.multiple) {
      this.markSelectedMultiple();
    } else {
      this.options.forEach(option => {
        option.selected = this.selectedValue.length > 0 && option.value === this.selectedValue[0];
      });
    }
    this.cdr.markForCheck();
  }


  get inputId(): string {
    return this.id ?? this._inputId;
  }

  get hasError(): boolean {
    return !!this.error;
  }
  get errorId(): string {
    return `${this.inputId}-error`;
  }

  get inputClasses(): string {
    const classes = ['ava-select__input'];
    if (this.hasError) classes.push('ava-select__input--error');
    if (this.disabled) classes.push('ava-select__input--disabled');
    return classes.join(' ');
  }

  get wrapperClasses(): string {
    const classes = [];
    if (this.hasError) classes.push('ava-select--error');
    if (this.disabled) classes.push('ava-select--disabled');
    if (this.size) classes.push(this.size);

    return [...classes].join(' ');
  }


  onSearchChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.searchTerm = input.value.toLowerCase();
    this.options.forEach(option => {
      option.visible = option.label.toLowerCase().includes(this.searchTerm);
    });
    this.hasSearchedOptions = this.options.some(option => option.visible);
    console.log(this.hasSearchedOptions);
    // Reset highlighted index when search changes
    this.highlightedIndex = 0;
    this.updateHighlightedStates();
    this.cdr.markForCheck();
  }

  // Keyboard navigation helper methods
  private navigateDown() {
    const visibleOptions = this.getVisibleOptions();
    if (visibleOptions.length === 0) return;

    this.highlightedIndex = (this.highlightedIndex + 1) % visibleOptions.length;
    this.updateHighlightedStates();
    this.scrollToHighlightedOption();
  }

  private navigateUp() {
    const visibleOptions = this.getVisibleOptions();
    if (visibleOptions.length === 0) return;

    this.highlightedIndex = this.highlightedIndex <= 0
      ? visibleOptions.length - 1
      : this.highlightedIndex - 1;
    this.updateHighlightedStates();
    this.scrollToHighlightedOption();
  }

  private selectHighlightedOption() {
    const visibleOptions = this.getVisibleOptions();
    if (this.highlightedIndex >= 0 && this.highlightedIndex < visibleOptions.length) {
      const highlightedOption = visibleOptions[this.highlightedIndex];
      this.selectValue(highlightedOption.value);
    }
  }

  private closeDropdown() {
    this.isOpen = false;
    this.highlightedIndex = -1;
    this.updateHighlightedStates();
    this.cdr.markForCheck();
  }

  private getVisibleOptions(): SelectOptionComponent[] {
    return this.options?.filter(option => option.visible) || [];
  }

  private getVisibleOptionsCount(): number {
    return this.getVisibleOptions().length;
  }

  private getSelectedOptionIndex(): number {
    if (!this.selectedValue.length) return -1;

    const visibleOptions = this.getVisibleOptions();
    const selectedValue = this.multiple ? this.selectedValue[0] : this.selectedValue[0];

    const index = visibleOptions.findIndex(option => option.value === selectedValue);
    return index >= 0 ? index : 0;
  }

  private scrollToHighlightedOption() {
    // Scroll the highlighted option into view if needed
    setTimeout(() => {
      const panel = document.querySelector('.ava-select-panel');
      const highlightedElement = panel?.querySelector('.option.highlighted');

      if (panel && highlightedElement) {
        const panelRect = panel.getBoundingClientRect();
        const optionRect = highlightedElement.getBoundingClientRect();

        if (optionRect.bottom > panelRect.bottom) {
          panel.scrollTop += optionRect.bottom - panelRect.bottom + 5;
        } else if (optionRect.top < panelRect.top) {
          panel.scrollTop -= panelRect.top - optionRect.top + 5;
        }
      }
    });
  }

  // Helper method to check if an option is highlighted
  isOptionHighlighted(optionIndex: number): boolean {
    const visibleOptions = this.getVisibleOptions();
    return this.highlightedIndex === optionIndex && this.isOpen;
  }

  // Get the actual index of a visible option
  getVisibleOptionIndex(option: SelectOptionComponent): number {
    const visibleOptions = this.getVisibleOptions();
    return visibleOptions.findIndex(visibleOption => visibleOption === option);
  }

  // Update highlighted states for all options
  private updateHighlightedStates() {
    const visibleOptions = this.getVisibleOptions();
    this.options?.forEach((option, index) => {
      const visibleIndex = visibleOptions.findIndex(visibleOption => visibleOption === option);
      option.highlighted = visibleIndex === this.highlightedIndex && this.isOpen;
    });
    this.cdr.markForCheck();
  }

  /** Get icon size based on tag size */
  get getIconSize(): number {
    switch (this.size) {
      case 'xs':
        return 16;
      case 'sm':
        return 12;
      case 'md':
        return 20;
      case 'lg':
        return 24;
      default:
        return 12;
    }
  }




}
