.ava-select-option {

    .ava-select__label {
        display: block;
        color: var(--select-label-color);

        &--required::after {
            content: "";
        }
    }

    .ava-select__required {
        color: var(--select-required-color);
        margin-left: 0.25rem;
    }

    .ava-select-container {
        cursor: pointer;
        user-select: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--select-toggle-padding);
        background: var(--select-toggle-background);
        border: var(--select-toggle-background) solid 1px;
        border-radius: var(--select-toggle-border-radius);
        box-shadow: var(--select-box-shadow);
        border: var(--glass-50-border);
        box-shadow: var(--glass-50-shadow);

        &.sm {
            padding: var(--select-input-padding-sm);
            min-height: var(--select-input-min-height-sm);
            font-size: var(--select-input-font-size-sm);
        }

        &.md {
            padding: var(--select-input-padding-md);
            min-height: var(--select-input-min-height-md);
            font-size: var(--select-input-font-size-md);
        }

        &.lg {
            padding: var(--select-input-padding-lg);
            min-height: var(--select-input-min-height-lg);
            font-size: var(--select-input-font-size-lg);
        }

        &:hover {
            border-color: var(--select-brand-color);
        }

        &:focus {
            outline: none;
            border-color: var(--select-brand-color);
        }

        &.open {
            border-color: var(--select-brand-color);
            border: var(--select-brand-color) solid 2px;
        }

        .ava-select {
            .ava-select-placeholder {
                &.has-value {
                    color: var(--textbox-input-color);
                }

                color: var(--select-place-holder-color);
                font-weight: normal;

                &.sm {
                    font-size: var(--select-input-font-size-sm);
                }

                &.md {
                    font-size: var(--select-input-font-size-md);
                }

            }
        }

        &.ava-select--error {
            border-color: var(--select-error-border);
        }

        &.ava-select--disabled {
            cursor: not-allowed;
            opacity: 0.6;
            background-color: var(--select-background-disabled);

            &:hover {
                border-color: var(--select-toggle-background);
            }

            .ava-select-placeholder {
                color: var(--select-disabled-text);
            }
        }

    }

    .select-option-container {
        position: relative;

        .ava-select-panel {
            position: absolute;
            margin-top: 4px;
            z-index: 1000;
            width: 100%;
            overflow-y: auto;
            background: var(--select-menu-background);
            border: var(--select-menu-border) solid 1px;
            border-radius: var(--select-menu-border-radius);
            box-shadow: var(--select-menu-shadow);
            transform: translateY(-8px);
            animation: dropdownSlideDown 0.2s ease forwards;
            cursor: pointer;
            font: var(--select-toggle-font);

            &.single {
                ava-select-option.selected {
                    .option {
                        // background: var(--select-brand-color);
                        color: var(--select-brand-text-color);
                        border-left-color: var(--select-brand-color);
                    }
                }

            }

            .option {
                display: flex;
                align-items: center;
                padding: var(--select-option-padding);
                cursor: pointer;
                transition: var(--select-item-transition);
                font: var(--select-item-font);
                color: var(--select-item-text);
                border-radius: 0;
                margin: 0;
                position: relative;
                min-height: var(--select-size-md-height);
                background: var(--select-item-background);
                border-left: 3px solid transparent;



                &:focus &.focused {
                    background: var(--select-item-background-hover);
                    color: var(--select-item-text-hover);
                }

                &:hover,
                &:focus,
                &.focused {
                    background-attachment: fixed;
                    background: var(--select-item-background-hover);
                    color: var(--select-item-text-hover);
                }

                &.highlighted {
                    background: var(--select-item-background-hover);
                    color: var(--select-item-text-hover);
                    // outline: 2px solid var(--select-brand-color);
                    outline-offset: -2px;
                    // border-left-color: var(--select-brand-color);
                }

                &.selected {
                    background: var(--select-brand-color);
                    color: var(--select-brand-text-color);
                }

                &.selected.highlighted {
                    background: var(--select-brand-color);
                    color: var(--select-brand-text-color);

                }

                &.disabled {
                    cursor: not-allowed;
                    opacity: 0.5;
                    color: var(--select-disabled-text);

                    &:hover,
                    &:focus,
                    &.focused {
                        background: var(--select-item-background);
                        color: var(--select-disabled-text);
                        box-shadow: none;
                    }



                    &.selected {
                        background: var(--select-item-background);
                        color: --select-disabled-text;
                        border-left-color: transparent;
                    }
                }

                ava-icon {
                    color: currentColor;
                    transition: all 0.2s ease;
                    margin-right: 5px;
                    position: relative;
                    top: 1px;
                }


            }

        }

        .search-box {
            position: relative;
            padding: var(--select-toggle-padding);
            background: var(--select-search-background);

            input {
                width: 100%;
                height: var(--select-size-sm-height);
                border: none;
                border-radius: var(--select-search-border-radius);
                color: var(--search-input-text);
                background: transparent;
                box-sizing: border-box;
                padding-left: 28px;

                &::placeholder {
                    color: var(--select-place-holder-color);
                }

                &:focus {
                    outline: none;
                    background: transparent;
                }
            }

            ava-icon {
                position: absolute;
                left: 10px;
                top: 50%;
                transform: translateY(-50%);
                color: var(--select-place-holder-color);
                pointer-events: none;
            }


        }

        .no-results {
            padding: var(--select-option-padding);
            text-align: center;
            color: var(--select-place-holder-color);
            font: var(--select-item-font);
            font-style: italic;
        }


    }

    .ava-select__error-icon {
        flex-shrink: 0;
        margin-top: var(--global-spacing-1);
    }

    .ava-select__error-text {
        flex: 1;
    }

    .ava-select__error {
        display: flex;
        align-items: flex-start;
        gap: var(--select-error-gap);
        color: var(--select-error-color);
    }

    &.lg {
        .ava-select__label {
            font: var(--select-label-font);
            margin-bottom: var(--select-label-margin);
            font-weight: var(--select-label-weight);
        }

        .ava-select-container {
            padding: var(--select-input-padding-lg);
            min-height: var(--select-input-min-height-lg);
            font-size: var(--select-input-font-size-lg);

            .ava-select {
                .ava-select-placeholder {
                    font-size: var(--select-input-font-size-lg);
                }
            }

        }

        .ava-select__error {
            font-size: var(--select-error-font-size);
            line-height: var(--select-line-height);
            margin-top: .5rem;
        }

        .select-option-container {
            .ava-select-panel {
                .option {
                    padding: var(--select-option-padding);
                    font: var(--select-item-font);
                    min-height: var(--select-size-md-height);
                }

            }

            .search-box {
                height: var(--select-size-lg-height);

                input {
                    font: var(--select-search-font);
                }

                ava-icon {
                    padding-right: 12px;
                }

                .no-results {
                    font: var(--select-item-font);
                }
            }

        }
    }
}

@keyframes dropdownSlideDown {
    0% {
        opacity: 0;
        transform: translateY(-8px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}