<div class="user-table-wrapper" #tableRef>
  <div class="table-wrapper">
    <table class="ava-user-table">
      <thead>
        <tr>
          <ng-container *ngFor="let col of columns">
            <th
              *ngIf="col.visible"
              [class.sortable]="col.sortable"
              [class.filterable]="col.filterable"
              [class.sorted-asc]="
                sortColumn === col.field && sortDirection === 'asc'
              "
              [class.sorted-desc]="
                sortColumn === col.field && sortDirection === 'desc'
              "
              (mouseenter)="hoveredCol = col.field"
              (mouseleave)="hoveredCol = null"
              (click)="col.sortable ? onSort(col) : null"
            >
              <ng-container
                *ngIf="
                  col.field === 'select' && showCheckbox;
                  else normalHeader
                "
              >
                <!-- Checkbox header -->
                <ava-checkbox
                  [isChecked]="parentChecked"
                  [indeterminate]="indeterminate"
                  (isCheckedChange)="onSelectAllChange($event)"
                ></ava-checkbox>
              </ng-container>

              <ng-template #normalHeader>
                <div class="th-content">
                  <span class="header-label">{{ col.label }}</span>

                  <!-- Sort Icon -->
                  <ava-icon
                    *ngIf="col.sortable && sortColumn === col.field"
                    [iconName]="
                      sortDirection === 'asc' ? sortAscIcon : sortDescIcon
                    "
                    [iconSize]="16"
                    class="sort-icon"
                  ></ava-icon>
                </div>

                <!-- Filter Icon -->
                <ava-icon
                  *ngIf="
                    (col.filterable && useDefaultFilter) ||
                    (col.filterable && useCustomFilter)
                  "
                  class="filter-icon"
                  [iconName]="filterIcon"
                  [class.active]="isFilterActive(col.field)"
                  [iconSize]="16"
                  title="Filter"
                  (click)="onFilterIconClick($event, col.field)"
                ></ava-icon>
              </ng-template>
            </th>
          </ng-container>
        </tr>
      </thead>

      <tbody>
        <tr *ngFor="let row of paginatedData">
          <ng-container *ngFor="let col of columns">
            <td
              *ngIf="col.visible"
              [ngClass]="{ 'actions-cell': col.field === 'actions' }"
            >
              <ng-container [ngSwitch]="col.field">
                <!-- Checkbox cell -->
                <ng-container *ngSwitchCase="'select'">
                  <ava-checkbox
                    [isChecked]="row.isSelected"
                    (isCheckedChange)="onRowCheckboxChange(row, $event)"
                  ></ava-checkbox>
                </ng-container>

                <!-- Status column -->
                <ng-container *ngSwitchCase="'status'">
                  <ava-tag
                    type="badge"
                    *ngFor="let tag of row[col.field]"
                    [label]="tag.label"
                    [color]="tag.color ?? 'default'"
                    [variant]="tag.variant ?? 'filled'"
                    [icon]="tag.icon"
                    [iconColor]="tag.iconColor"
                    size="sm"
                    [iconPosition]="tag.iconPosition ?? 'start'"
                    [ngStyle]="tag.customStyle"
                  ></ava-tag>
                </ng-container>

                <!--  Actions column -->
                <ng-container *ngSwitchCase="'actions'">
                  <div class="actions-wrapper">
                    <!-- Inline Action Buttons -->
                    <ng-container *ngFor="let entry of getInlineActions(row)">
                      <div
                        class="action-icon"
                        [title]="entry[1].label"
                        (click)="handleAction(row, entry[0])"
                      >
                        <span class="dynamic-icon" *ngIf="entry[1].icon">
                          <ng-container
                            *ngIf="
                              isUrl(entry[1].icon);
                              else renderActionAvaIcon
                            "
                          >
                            <span
                              [innerHTML]="getActionIconHtml(entry[1].icon)"
                            ></span>
                          </ng-container>
                          <ng-template #renderActionAvaIcon>
                            <ava-icon
                              [iconName]="entry[1].icon"
                              [iconSize]="'16'"
                              aria-hidden="true"
                              class="ava-icon"
                            ></ava-icon>
                          </ng-template>
                        </span>
                      </div>
                    </ng-container>

                    <!-- Dropdown for overflow actions -->
                    <div class="dropdown" *ngIf="shouldShowDropdown(row)">
                      <ava-icon
                        class="ava-icon"
                        iconName="ellipsis-vertical"
                        [iconSize]="16"
                        [cursor]="true"
                        (click)="toggleDropdown(row)"
                      ></ava-icon>
                      <div
                        class="dropdown-menu"
                        *ngIf="dropdownRow === row"
                        #dropdownMenu
                        (mouseleave)="dropdownRow = null"
                      >
                        <ng-container
                          *ngFor="let entry of getDropdownActions(row)"
                        >
                          <div
                            class="dropdown-item"
                            role="button"
                            tabindex="0"
                            (click)="handleAction(row, entry[0])"
                          >
                            <span class="dropdown-icon" *ngIf="entry[1].icon">
                              <ng-container
                                *ngIf="
                                  isUrl(entry[1].icon);
                                  else renderDropdownAvaIcon
                                "
                              >
                                <span
                                  [innerHTML]="getActionIconHtml(entry[1].icon)"
                                ></span>
                              </ng-container>
                              <ng-template #renderDropdownAvaIcon>
                                <ava-icon
                                  [iconName]="entry[1].icon"
                                  [iconSize]="'16'"
                                  aria-hidden="true"
                                  class="ava-icon"
                                ></ava-icon>
                              </ng-template>
                            </span>
                            {{ entry[1].label }}
                          </div>
                        </ng-container>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <!-- Default cell -->
                <ng-container *ngSwitchDefault>
                  <ng-container
                    *ngIf="row[col.field]?.clickable; else normalText"
                  >
                    <a
                      href="javascript:void(0)"
                      (click)="handleCellClick(row, col.field)"
                      class="cell-link"
                    >
                      <ng-container *ngIf="row[col.field]?.iconName">
                        <ng-container
                          *ngIf="
                            isUrl(row[col.field]?.iconName);
                            else renderAvaIcon
                          "
                        >
                          <span
                            class="dynamic-icon"
                            [innerHTML]="getIcon(row[col.field]?.iconName)"
                          ></span>
                        </ng-container>
                        <ng-template #renderAvaIcon>
                          <ava-icon
                            [iconName]="row[col.field]?.iconName"
                            [iconSize]="'16'"
                            aria-hidden="true"
                            class="ava-icon"
                          ></ava-icon>
                        </ng-template>
                      </ng-container>
                      <span>{{ getCellValue(row, col.field) }}</span>
                    </a>
                  </ng-container>

                  <ng-template #normalText>
                    <ng-container *ngIf="row[col.field]?.iconName">
                      <ng-container
                        *ngIf="
                          isUrl(row[col.field]?.iconName);
                          else renderAvaIconText
                        "
                      >
                        <span
                          class="dynamic-icon"
                          [innerHTML]="getIcon(row[col.field]?.iconName)"
                        ></span>
                      </ng-container>
                      <ng-template #renderAvaIconText>
                        <ava-icon
                          [iconName]="row[col.field]?.iconName"
                          [iconSize]="'16'"
                          aria-hidden="true"
                          class="ava-icon"
                        ></ava-icon>
                      </ng-template>
                    </ng-container>
                    <span>{{ getCellValue(row, col.field) }}</span>
                  </ng-template>
                </ng-container>
              </ng-container>
            </td>
          </ng-container>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Default Filter Modal rendered outside table -->
  <div
    class="default-filter-modal"
    *ngIf="
      useDefaultFilter &&
      openFilterField &&
      defaultColumnFilters[openFilterField!]
    "
    [style.left.px]="filterDropdownPosition?.x"
    [style.top.px]="filterDropdownPosition?.y"
  >
    <ava-dropdown
      [dropdownTitle]="getSelectedFilterLabel(openFilterField!)"
      [options]="defaultFilterConditions"
      [(ngModel)]="defaultColumnFilters[openFilterField!].type"
    ></ava-dropdown>
    <ava-textbox
      *ngIf="!isFilterTypeEmpty(defaultColumnFilters[openFilterField!].type)"
      label=""
      placeholder="Enter value"
      [(ngModel)]="defaultColumnFilters[openFilterField!].value"
    ></ava-textbox>

    <div class="default-filter-actions">
      <ava-button
        label="Filter"
        variant="primary"
        (userClick)="applyAndCloseFilter()"
        pressedEffect="ripple"
      ></ava-button>
      <ava-button
        label="Clear"
        variant="secondary"
        (userClick)="clearDefaultFilter()"
        pressedEffect="ripple"
      ></ava-button>
    </div>
  </div>
  <!-- Custom filter-->
  <div
    class="default-filter-modal"
    *ngIf="useCustomFilter && openFilterField"
    [style.left.px]="filterDropdownPosition?.x"
    [style.top.px]="filterDropdownPosition?.y"
  >
    <!-- Select All (Parent Checkbox) -->
    <ava-checkbox
      label="Select All"
      [isChecked]="isAllFilterSelected(openFilterField)"
      [indeterminate]="isIndeterminate(openFilterField)"
      (isCheckedChange)="toggleAllFilterValues(openFilterField, $event)"
    ></ava-checkbox>

    <!-- Individual (Child) Checkboxes -->
    <label *ngFor="let val of columnFilters[openFilterField]">
      <ava-checkbox
        [label]="val"
        [isChecked]="tempSelectedFilters[openFilterField].has(val)"
        (isCheckedChange)="toggleFilterValue(openFilterField, val, $event)"
      ></ava-checkbox>
    </label>

    <!-- Filter Button -->
    <ava-button
      label="Filter"
      variant="primary"
      (userClick)="updateFilter()"
      pressedEffect="ripple"
    ></ava-button>
  </div>
</div>
