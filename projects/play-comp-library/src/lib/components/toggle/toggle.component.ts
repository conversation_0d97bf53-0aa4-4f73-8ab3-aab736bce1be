/* eslint-disable @typescript-eslint/no-inferrable-types */
// toggle.component.ts
import {
  Component,
  ChangeDetectionStrategy,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export type ToggleSize = 'small' | 'medium' | 'large';
export type TogglePosition = 'left' | 'right';

@Component({
  selector: 'ava-toggle',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './toggle.component.html',
  styleUrls: ['./toggle.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToggleComponent {
  @Input() size: ToggleSize = 'medium';
  @Input() title: string = '';
  @Input() position: TogglePosition = 'left';
  @Input() disabled: boolean = false;
  @Input() checked: boolean = false;
  @Input() animation: boolean = true;
  @Input() showIcons: boolean = false;
  @Input() uncheckedIcon: string = 'x';
  @Input() checkedIcon: string = 'check';

  @Output() checkedChange = new EventEmitter<boolean>();

  // Generate unique ID for the toggle input
  private readonly _uniqueId = `ava-toggle-${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  onToggle(): void {
    if (this.disabled) return;

    this.checked = !this.checked;
    this.checkedChange.emit(this.checked);
  }

  onLabelClick(event: Event): void {
    if (this.disabled) return;
    // Prevent the label click from propagating to avoid double toggle
    event.preventDefault();
    this.onToggle();
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === ' ' || event.key === 'Enter') {
      event.preventDefault();
      this.onToggle();
    }
  }

  get titleName(): string | null {
    return this.title
      ? `toggle-title-${this.title.replace(/\s+/g, '-').toLowerCase()}`
      : null;
  }

  get toggleId(): string {
    return this._uniqueId;
  }

  getIconSize(): number {
    switch (this.size) {
      case 'small':
        return 10;
      case 'medium':
        return 12;
      case 'large':
        return 14;
      default:
        return 12;
    }
  }

  getCheckedIconColor(): string {
    return 'rgba(var(--rgb-brand-primary))';
  }

  getUncheckedIconColor(): string {
    return 'color-mix(in srgb, rgba(var(--rgb-brand-primary)) 60%, var(--text-color-secondary, #666666))';
  }
}
