<!-- toggle.component.html -->
<div
  class="ava-toggle-container"
  [class.toggle-left]="position === 'left'"
  [class.toggle-right]="position === 'right'"
  [class.disabled]="disabled"
>
  <!-- Title - always show when it exists, let CSS handle positioning -->
  <label
    *ngIf="title"
    class="toggle-title"
    [class.disabled]="disabled"
    [id]="titleName"
    [for]="toggleId"
    [tabindex]="disabled ? -1 : 0"
    (click)="onLabelClick($event)"
    (keydown)="onLabelClick($event)"
  >
    {{ title }}
  </label>

  <!-- Hidden checkbox for form integration and accessibility -->
  <input
    type="checkbox"
    [id]="toggleId"
    [checked]="checked"
    [disabled]="disabled"
    class="sr-only"
    (change)="onToggle()"
    [attr.aria-labelledby]="titleName"
    [attr.aria-label]="!title ? 'Toggle switch' : null"
  />

  <!-- Toggle Switch -->
  <div
    class="toggle-wrapper"
    [class.toggle-small]="size === 'small'"
    [class.toggle-medium]="size === 'medium'"
    [class.toggle-large]="size === 'large'"
    [class.checked]="checked"
    [class.disabled]="disabled"
    [class.animated]="animation"
    [class.toggle-with-icons]="showIcons"
    [tabindex]="disabled ? -1 : 0"
    [attr.role]="'switch'"
    [attr.aria-checked]="checked"
    [attr.aria-disabled]="disabled"
    [attr.aria-labelledby]="titleName"
    [attr.aria-label]="!title ? 'Toggle switch' : null"
    (click)="onToggle()"
    (keydown)="onKeyDown($event)"
  >
    <div class="toggle-slider">
      <!-- Icons inside the slider -->
      <div *ngIf="showIcons" class="toggle-icons">
        <ava-icon
          *ngIf="checked"
          [iconName]="checkedIcon"
          class="toggle-icon toggle-icon-checked"
          [iconSize]="getIconSize()"
          [iconColor]="getCheckedIconColor()"
        >
        </ava-icon>
        <ava-icon
          *ngIf="!checked"
          [iconName]="uncheckedIcon"
          class="toggle-icon toggle-icon-unchecked"
          [iconSize]="getIconSize()"
          [iconColor]="getUncheckedIconColor()"
        >
        </ava-icon>
      </div>
    </div>

    <!-- Screen reader support -->
    <span class="sr-only">
      {{ title || "Toggle switch" }} {{ checked ? "enabled" : "disabled" }}
    </span>
  </div>
</div>
