import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { DrawerComponent } from './drawer.component';

describe('DrawerComponent', () => {
  let component: DrawerComponent;
  let fixture: ComponentFixture<DrawerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DrawerComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DrawerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should not be visible when isOpen is false', () => {
    component.isOpen = false;
    fixture.detectChanges();
    
    const drawerElement = fixture.debugElement.query(By.css('.ava-drawer'));
    expect(drawerElement).toBeFalsy();
  });

  it('should be visible when isOpen is true', () => {
    component.isOpen = true;
    fixture.detectChanges();
    
    const drawerElement = fixture.debugElement.query(By.css('.ava-drawer'));
    expect(drawerElement).toBeTruthy();
  });

  it('should apply correct position class', () => {
    component.isOpen = true;
    component.position = 'left';
    fixture.detectChanges();
    
    const drawerElement = fixture.debugElement.query(By.css('.ava-drawer'));
    expect(drawerElement.nativeElement.classList).toContain('ava-drawer--left');
  });

  it('should apply correct size class', () => {
    component.isOpen = true;
    component.size = 'large';
    fixture.detectChanges();
    
    const drawerElement = fixture.debugElement.query(By.css('.ava-drawer'));
    expect(drawerElement.nativeElement.classList).toContain('ava-drawer--large');
  });

  it('should show overlay when showOverlay is true', () => {
    component.isOpen = true;
    component.showOverlay = true;
    fixture.detectChanges();
    
    const overlayElement = fixture.debugElement.query(By.css('.ava-drawer-overlay'));
    expect(overlayElement).toBeTruthy();
  });

  it('should not show overlay when showOverlay is false', () => {
    component.isOpen = true;
    component.showOverlay = false;
    fixture.detectChanges();
    
    const overlayElement = fixture.debugElement.query(By.css('.ava-drawer-overlay'));
    expect(overlayElement).toBeFalsy();
  });

  it('should show close button when showCloseButton is true', () => {
    component.isOpen = true;
    component.showCloseButton = true;
    fixture.detectChanges();
    
    const closeButton = fixture.debugElement.query(By.css('.ava-drawer__close-button'));
    expect(closeButton).toBeTruthy();
  });

  it('should emit closed event when close is called', () => {
    spyOn(component.closed, 'emit');
    component.close();
    expect(component.closed.emit).toHaveBeenCalled();
  });

  it('should emit opened event when open is called', () => {
    spyOn(component.opened, 'emit');
    component.open();
    // Wait for animation timeout
    setTimeout(() => {
      expect(component.opened.emit).toHaveBeenCalled();
    }, 350);
  });

  it('should close on overlay click when closeOnOverlayClick is true', () => {
    component.isOpen = true;
    component.closeOnOverlayClick = true;
    component.showOverlay = true;
    fixture.detectChanges();
    
    spyOn(component, 'close');
    
    const overlayElement = fixture.debugElement.query(By.css('.ava-drawer-overlay'));
    overlayElement.nativeElement.click();
    
    expect(component.close).toHaveBeenCalled();
  });

  it('should not close on overlay click when closeOnOverlayClick is false', () => {
    component.isOpen = true;
    component.closeOnOverlayClick = false;
    component.showOverlay = true;
    fixture.detectChanges();
    
    spyOn(component, 'close');
    
    const overlayElement = fixture.debugElement.query(By.css('.ava-drawer-overlay'));
    overlayElement.nativeElement.click();
    
    expect(component.close).not.toHaveBeenCalled();
  });

  it('should close on escape key when closeOnEscape is true', () => {
    component.isOpen = true;
    component.closeOnEscape = true;
    
    spyOn(component, 'close');
    
    const event = new KeyboardEvent('keydown', { key: 'Escape' });
    component.onKeyDown(event);
    
    expect(component.close).toHaveBeenCalled();
  });

  it('should not close when persistent is true', () => {
    component.isOpen = true;
    component.persistent = true;
    
    const initialState = component.isOpen;
    component.close();
    
    expect(component.isOpen).toBe(initialState);
  });

  it('should toggle drawer state', () => {
    component.isOpen = false;
    component.toggle();
    expect(component.isOpen).toBe(true);
    
    component.toggle();
    expect(component.isOpen).toBe(false);
  });

  it('should display title when provided', () => {
    component.isOpen = true;
    component.title = 'Test Title';
    fixture.detectChanges();
    
    const titleElement = fixture.debugElement.query(By.css('.ava-drawer__title'));
    expect(titleElement.nativeElement.textContent.trim()).toBe('Test Title');
  });

  it('should display subtitle when provided', () => {
    component.isOpen = true;
    component.subtitle = 'Test Subtitle';
    fixture.detectChanges();
    
    const subtitleElement = fixture.debugElement.query(By.css('.ava-drawer__subtitle'));
    expect(subtitleElement.nativeElement.textContent.trim()).toBe('Test Subtitle');
  });

  it('should apply custom width style', () => {
    component.isOpen = true;
    component.position = 'right';
    component.width = '600px';
    fixture.detectChanges();
    
    const drawerElement = fixture.debugElement.query(By.css('.ava-drawer'));
    expect(drawerElement.nativeElement.style.width).toBe('600px');
  });

  it('should apply custom z-index', () => {
    component.isOpen = true;
    component.zIndex = 2000;
    fixture.detectChanges();
    
    const drawerElement = fixture.debugElement.query(By.css('.ava-drawer'));
    expect(drawerElement.nativeElement.style.zIndex).toBe('2000');
  });
});
