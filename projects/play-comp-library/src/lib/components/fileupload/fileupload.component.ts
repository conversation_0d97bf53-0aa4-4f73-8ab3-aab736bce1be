import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Output, HostListener, Input, OnInit } from '@angular/core';
import { IconComponent } from '../icon/icon.component';
import { APP_CONSTANTS } from '../../constants/app.constants';
import { LucideAngularModule } from 'lucide-angular';
import { ButtonComponent } from '../button/button.component';
import { AvaTagComponent } from '../tags/tags.component';

@Component({
  selector: 'ava-file-upload',
  standalone: true,
  imports: [CommonModule, IconComponent, LucideAngularModule, ButtonComponent, AvaTagComponent],
  templateUrl: './fileupload.component.html',
  styleUrls: ['./fileupload.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,

})
export class FileUploadComponent implements OnInit {
  @Output() public fileUploaded = new EventEmitter<File>();
  @Output() public filesListChanged = new EventEmitter<File[]>();
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() uploaderId: string = '';
  @Input() enableAnimation: boolean = false;
  @Input() allowedFormats: string[] = [];
  @Input() singleFileMode: boolean = false;
  @Input() maxFiles: number | null = null;
  @Input() componentTitle: string = "Upload File Here";
  @Input() supportedFormatLabel = "Supported file formats";
  @Input() maxFileSize = 3 * 1024 * 1024; //3MB default
  @Input() showSelectedFiles: boolean = true;
  @Input() showDialogCloseIcon: boolean = true;
  @Input() showUploadButton: boolean = true;
  @Input() preview: boolean = false; // New input for preview functionality

  appConstants = APP_CONSTANTS;
  public uploadedFiles: File[] = [];
  public fileUploadedSuccess = false;
  public fileFormatError = false;
  public fileSizeError = false;
  public maxFilesError = false;
  public isUploadActive = false;
  public viewAll: boolean = false;
  public files: File[] = []; // For 'new' variant

  // Public so it's accessible from the template
  public uniqueId: string = '';

  ngOnInit(): void {
    // Generate a unique ID if not provided
    this.uniqueId = this.uploaderId || 'uploader-' + Math.random().toString(36).substring(2, 9);
  }

  get allowedFormatsList(): string[] {
    return this.allowedFormats.length > 0 ? this.allowedFormats : ['jpeg', 'jpg', 'png', 'svg', 'doc', 'docx', 'xlsx', 'txt', 'pdf']
  }

  toggleViewAll() {
    this.viewAll = !this.viewAll;
  }

  sizeFormat(bytes: number, decimals: number = 2): string {
    if (isNaN(bytes) || bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const value = bytes / Math.pow(k, i);

    return `${parseFloat(value.toFixed(decimals))} ${sizes[i]}`;
  }

  public onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFile(input.files[0]);

      // Reset the input value to allow selecting the same file again if needed
      input.value = '';
    }
  }

  // HostListener to handle dragover event
  @HostListener('dragover', ['$event'])
  public onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  // HostListener to handle drop event
  @HostListener('drop', ['$event'])
  public onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFile(files[0]);
    }
  }

  // Centralized method to handle file validation and selection
  private handleFile(file: File): void {
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    // console.log("allowedFormats:", this.allowedFormats);
    if (!fileExtension || !this.allowedFormatsList.includes(fileExtension)) {
      this.fileFormatError = true;
      this.fileSizeError = false;
      this.maxFilesError = false;
      return;
    }

    if (file.size > this.maxFileSize) {
      this.fileSizeError = true;
      this.fileFormatError = false;
      this.maxFilesError = false;
      return;
    }

    // Check for max files limitation
    if (this.maxFiles !== null && !this.singleFileMode && this.uploadedFiles.length >= this.maxFiles) {
      this.maxFilesError = true;
      this.fileFormatError = false;
      this.fileSizeError = false;
      return;
    }

    // If in single file mode, replace the existing file
    if (this.singleFileMode) {
      this.uploadedFiles = [file];
    } else {
      // Add the file to the list of uploaded files
      const isDuplicate = this.uploadedFiles.some(existingFile =>
        existingFile.name === file.name && existingFile.size === file.size);

      if (!isDuplicate) {
        this.uploadedFiles.push(file);
      }
    }

    this.filesListChanged.emit(this.uploadedFiles);
    this.fileFormatError = false;
    this.fileSizeError = false;
    this.maxFilesError = false;
    this.isUploadActive = true;
  }

  public openFileSelector(): void {
    // Check for max files before opening selector
    if (this.maxFiles !== null && !this.singleFileMode && this.uploadedFiles.length >= this.maxFiles) {
      this.maxFilesError = true;
      return;
    }

    const fileInput = document.getElementById('fileInput-' + this.uniqueId) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  public uploadFile(): void {
    if (this.uploadedFiles.length > 0) {
      // Emit each file individually to maintain backward compatibility
      this.uploadedFiles.forEach(file => {
        this.fileUploaded.emit(file);
      });
      this.fileUploadedSuccess = true;
    } else {
      // If no files are selected, open the file dialog
      this.openFileSelector();
    }
  }

  public removeFile(index: number): void {
    if (index >= 0 && index < this.uploadedFiles.length) {
      this.uploadedFiles.splice(index, 1);
      this.filesListChanged.emit(this.uploadedFiles);

      // Reset success state if all files are removed
      if (this.uploadedFiles.length === 0) {
        this.fileUploadedSuccess = false;
      }

      // Reset max files error when a file is removed
      this.maxFilesError = false;
    }
  }

  public resetUpload(): void {
    this.uploadedFiles = [];
    this.fileUploadedSuccess = false;
    this.fileFormatError = false;
    this.fileSizeError = false;
    this.maxFilesError = false;
    this.filesListChanged.emit(this.uploadedFiles);
  }

  public closeUpload(): void {
    this.resetUpload();
  }

  // New methods for preview functionality
  public removeNewFile(file: File): void {
    this.uploadedFiles = this.uploadedFiles.filter(f => f !== file);
    this.filesListChanged.emit(this.uploadedFiles);
  }

  public getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || '';
  }

  allowAccepted(): string {
    return this.allowedFormatsList?.length
      ? this.allowedFormatsList.map(ext => `.${ext}`).join(',')
      : '';
  }
}