// Original default upload container styles
.upload-container.default {
  width: 100%;
  font-family: var(--fileupload-font-family-body);
  border-radius: 0.5rem;
  padding: 2rem 1.5rem;
  box-shadow: 0rem 0.0625rem 0.625rem 0rem var(--fileupload-primary-text-disabled);
  text-align: center;
  background: var(--fileupload-background-default);
  height: auto; // Changed from fixed height to auto to accommodate multiple files
  border: 1px solid var(--fileupload-border-color);

  .file-upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-title {
      width: 100%;
      text-align: center;
      font-family: var(--fileupload-font-family-heading);
      font-size: var(--fileupload-font-size-lg);
      font-weight: var(--fileupload-font-weight-regular);
      color: var(--fileupload-heading-color);
      padding-left: 2.25rem;
    }
  }

  .close-button {
    background: none;
    border: none;

    ava-icon {
      ::ng-deep .ava-icon-container {
        cursor: pointer;
      }
    }
  }

  .error-message {
    color: var(--fileupload-danger-background);
    font-size: var(--fileupload-font-size-xs);
    margin-top: 0.5rem;
    text-align: center;
  }

  .file-formats {
    color: var(--fileupload-text-color-primary);
    font-size: var(--fileupload-font-size-xs);
    line-height: 1.5rem;
    font-family: var(--fileupload-font-family-body);
    font-weight: var(--fileupload-font-weight-regular);
  }

  .upload-area {
    border-radius: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--fileupload-border-color);
    padding: 2rem 3rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
    height: 12.25rem;
    margin-bottom: 1rem;

    &:hover {
      border-color: var(--fileupload-border-color);
    }

    .upload-placeholder {
      text-align: center;

      p.click-here {
        margin: 0.625rem 0;
        color: var(--fileupload-text-color-primary);
        font-size: var(--fileupload-font-size-md);
        font-weight: var(--fileupload-font-weight-regular);

        &.active {
          color: var(--fileupload-highlighted-text-pink);
        }
      }
    }
  }

  .file-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
  }

  .files-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    gap: 0.5rem;
    color: var(--fileupload-text-color-primary);

    span {
      display: flex;
      align-items: center;
      font-size: var(--fileupload-font-size-xs);
    }

    a.viewAll {
      font-size: var(--fileupload-font-size-xs);
      align-self: center;
      cursor: pointer;
      color: var(--fileupload-text-color-link);
      text-decoration: underline;
      text-underline-offset: 0.2rem;
    }
  }

  @media (max-width: 768px) {
    .upload-container {
      width: 100%;
      padding: 1rem 0.75rem;
    }

    .file-upload-header-title {
      font-size: var(--fileupload-font-size-md);
    }

    .upload-area {
      padding: 1.5rem 1.8rem;
      width: 90%;
    }

    .files-list {
      gap: 0.25rem;
    }

    .upload-button,
    .select-files-button {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    .upload-container {
      padding: 0.5rem 0.3rem;
    }

    .file-upload-header {
      flex-direction: column;
      align-items: center;

      &-title {
        font-size: var(--fileupload-font-size-sm);
      }
    }

    .upload-area {
      flex-direction: column;
      padding: 1rem 1.5rem;
    }

    .upload-placeholder {
      p.click-here {
        font-size: var(--fileupload-font-size-sm);
      }
    }

    .files-list {
      gap: 0.25rem;
    }
  }

  .supported-file {
    color: var(--fileupload-text-color-primary);
    font-size: var(--fileupload-font-size-sm);
    font-family: var(--fileupload-font-family-body);
    font-weight: var(--fileupload-font-weight-regular);
  }
}

// New preview layout with flex properties
.custom-file-upload {
  display: flex;
  flex-direction: row;
  gap: 10px;
  width: 100%;
  align-items: stretch;

  .left-box {
    flex: 1 1 60%;
    min-width: 0; // Prevents flex item from overflowing
  }

  .right-box {
    flex: 1 1 40%;
    min-width: 0; // Prevents flex item from overflowing
    border-left: 1px solid #e0e0e0;
    padding-left: 20px;
    padding-right: 20px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;

    .left-box,
    .right-box {
      flex: 1 1 100%;
    }
  }
}

.upload-container.new {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 3px;
  padding: 0; // Remove padding to let inner elements handle spacing
  box-shadow: none; // Remove shadow for cleaner look in preview
  border: none; // Remove border as upload-area has its own border
  background: transparent; // Let background be handled by upload-area

  .upload-area {
    width: 100%;
    height: 343px;
    border: 2px dashed #A3A7C2;
    border-radius: 3px;
    padding: 32px 48px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    background-color: #f9f9f9;
    cursor: pointer;
    padding-top: 60px;
    margin-bottom: 0; // Remove margin as it's handled by parent gap

    .supported-file {
      font-family: 'Inter', sans-serif;
      font-weight: 400;
      font-size: 12px;
      line-height: 1.5;
      color: #52577A;
      margin-bottom: 24px;
    }

    .file-formats {
      font-family: 'Inter', sans-serif;
      font-weight: 300;
      font-size: 10px;
      line-height: 1.5;
      color: #52577A;
      margin-bottom: 24px;
    }

    .click-here {
      color: #666D99;
      margin: 8px 0;
      font-family: Inter;
      font-weight: 400;
      font-style: Regular;
      font-size: 16px;
      line-height: 150%;
      letter-spacing: 0%;
      text-align: center;
    }

    .upload-button-container {
      margin-top: 24px;
    }
  }

  .error-message {
    color: var(--fileupload-danger-background);
    font-size: var(--fileupload-font-size-xs);
    margin-top: 0.5rem;
    text-align: center;
  }
}

/* Option 1: Add border to the right side of left-box */
.left-box {
  padding-right: 10px;
}

.file-review-container {
  width: 100%;
  background-color: #fff;
  height: 343px;
  overflow-y: auto;
  border-radius: 3px;
  padding-right: 10px;


  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 10px;
    border: 2px solid #f1f1f1;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }

  h3 {
    font-family: 'Mulish', sans-serif;
    font-weight: 700;
    font-size: 18px;
    line-height: 1.5;
    color: #000;
    margin-bottom: 16px;
    margin-top: 0px;
  }

  .file-review-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    max-width: 279px;
    height: 60px;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #D1D3D8;
    margin-bottom: 12px;

    .file-icon {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      padding: 10px;
      font-family: 'Mulish', sans-serif;
      font-weight: 700;
      font-size: 16px;
      line-height: 1.5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      color: white;
      flex-shrink: 0; // Prevent icon from shrinking

      &-pdf {
        background-color: #F97070;
      }

      &-docx,
      &-doc {
        background-color: #70BEF9;
      }

      &-xls,
      &-xlsx {
        background-color: #3F9B48;
      }

      &-txt,
      &-html {
        background-color: #8770F9;
      }

      &-ppt,
      &-pptx {
        background-color: #f97316;
      }

      &-png,
      &-svg {
        background-color: palevioletred;
      }

      &-jpeg,
      &-jpg {
        background-color: #A3A7C2;
      }
    }

    .file-info {
      flex: 1;
      overflow: hidden;
      margin-right: 10px;

      p {
        margin: 0;
        font-size: 14px;
        color: #000;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;

        &:last-child {
          color: #6b7280;
          font-family: Mulish;
          font-weight: 400;
          font-style: Regular;
          font-size: 16px;
          line-height: 150%;
          letter-spacing: 0%;
          text-decoration: underline;
          text-decoration-style: solid;
          text-decoration-thickness: 0%;
          text-decoration-skip-ink: auto;
        }
      }
    }

    .file-actions {
      flex-shrink: 0; // Prevent actions from shrinking

      .delete-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;
        color: #ef4444;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

// Original upload container styles (for non-preview mode)
.upload-container {
  width: 100%;
  font-family: var(--fileupload-font-family-body);
  border-radius: 0.5rem;
  padding: 2rem 1.5rem;
  box-shadow: 0rem 0.0625rem 0.625rem 0rem var(--fileupload-primary-text-disabled);
  text-align: center;
  background: var(--fileupload-background-default);
  height: auto;
  border: 1px solid var(--fileupload-border-color);

  .file-upload-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-title {
      width: 100%;
      text-align: center;
      font-family: var(--fileupload-font-family-heading);
      font-size: var(--fileupload-font-size-lg);
      font-weight: var(--fileupload-font-weight-regular);
      color: var(--fileupload-heading-color);
      padding-left: 2.25rem;
    }
  }

  .close-button {
    background: none;
    border: none;

    ava-icon {
      ::ng-deep .ava-icon-container {
        cursor: pointer;
      }
    }
  }

  .error-message {
    color: var(--fileupload-danger-background);
    font-size: var(--fileupload-font-size-xs);
    margin-top: 0.5rem;
    text-align: center;
  }

  .file-formats {
    color: var(--fileupload-text-color-primary);
    font-size: var(--fileupload-font-size-xs);
    line-height: 1.5rem;
    font-family: var(--fileupload-font-family-body);
    font-weight: var(--fileupload-font-weight-regular);
  }

  .upload-area {
    border-radius: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--fileupload-border-color);
    padding: 2rem 3rem;
    cursor: pointer;
    transition: border-color 0.3s ease;
    height: 12.25rem;
    margin-bottom: 1rem;

    &:hover {
      border-color: var(--fileupload-border-color);
    }

    .upload-placeholder {
      text-align: center;
      margin-bottom: 30px;

      p.click-here {
        margin: 0.625rem 0;
        color: #666D99;
        font-size: var(--fileupload-font-size-md);
        font-weight: var(--fileupload-font-weight-regular);

        &.active {
          color: var(--fileupload-highlighted-text-pink);
        }
      }
    }
  }

  .file-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
  }

  .files-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    gap: 0.5rem;
    color: var(--fileupload-text-color-primary);

    span {
      display: flex;
      align-items: center;
      font-size: var(--fileupload-font-size-xs);
    }

    a.viewAll {
      font-size: var(--fileupload-font-size-xs);
      align-self: center;
      cursor: pointer;
      color: var(--fileupload-text-color-link);
      text-decoration: underline;
      text-underline-offset: 0.2rem;
    }
  }

  .supported-file {
    color: var(--fileupload-text-color-primary);
    font-size: var(--fileupload-font-size-sm);
    font-family: var(--fileupload-font-family-body);
    font-weight: var(--fileupload-font-weight-regular);
  }
}

@media (max-width: 768px) {
  .upload-container {
    width: 100%;
    padding: 1rem 0.75rem;
  }

  .file-upload-header-title {
    font-size: var(--fileupload-font-size-md);
  }

  .upload-area {
    padding: 1.5rem 1.8rem;
    width: 90%;
  }

  .files-list {
    gap: 0.25rem;
  }

  .upload-button,
  .select-files-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .upload-container {
    padding: 0.5rem 0.3rem;
  }

  .file-upload-header {
    flex-direction: column;
    align-items: center;

    &-title {
      font-size: var(--fileupload-font-size-sm);
    }
  }

  .upload-area {
    flex-direction: column;
    padding: 1rem 1.5rem;
  }

  .upload-placeholder {
    p.click-here {
      font-size: var(--fileupload-font-size-sm);
    }
  }

  .files-list {
    gap: 0.25rem;
  }
}