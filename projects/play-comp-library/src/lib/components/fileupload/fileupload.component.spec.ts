import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FileUploadComponent } from './fileupload.component';
import { By } from '@angular/platform-browser';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IconComponent } from '../icon/icon.component';

describe('FileUploadComponent', () => {
  let component: FileUploadComponent;
  let fixture: ComponentFixture<FileUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FileUploadComponent, IconComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(FileUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.theme).toBe('light');
    expect(component.uploaderId).toBe('');
    expect(component.enableAnimation).toBeFalse();
    expect(component.allowedFormats).toEqual([]);
    expect(component.singleFileMode).toBeFalse();
    expect(component.maxFiles).toBeNull();
    expect(component.uploadedFiles).toEqual([]);
    expect(component.fileUploadedSuccess).toBeFalse();
    expect(component.fileFormatError).toBeFalse();
    expect(component.fileSizeError).toBeFalse();
    expect(component.maxFilesError).toBeFalse();
  });

  it('should generate unique id if uploaderId not provided', () => {
    component.ngOnInit();
    expect(component.uniqueId).toMatch(/^uploader-[a-z0-9]{7}$/);
  });

  it('should use provided uploaderId if available', () => {
    component.uploaderId = 'test-id';
    component.ngOnInit();
    expect(component.uniqueId).toBe('test-id');
  });

  describe('File Selection', () => {
    let mockFile: File;
    
    beforeEach(() => {
      mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
    });

    it('should handle file selection through input change', () => {
      const inputEl = document.createElement('input');
      inputEl.type = 'file';
      const event = {
        target: inputEl,
        preventDefault: () => {}
      } as unknown as Event;
      
      // Mock the FileList
      Object.defineProperty(inputEl, 'files', {
        value: [mockFile]
      });

      const spy = spyOn(component.filesListChanged, 'emit');
      component.onFileSelected(event);
      
      expect(spy).toHaveBeenCalledWith([mockFile]);
    });

    it('should handle file drop', () => {
      const mockDataTransfer = new DataTransfer();
      mockDataTransfer.items.add(mockFile);

      const dropEvent = new DragEvent('drop', {
        dataTransfer: mockDataTransfer
      });

      const spy = spyOn(component.filesListChanged, 'emit');
      spyOn(dropEvent, 'preventDefault');
      spyOn(dropEvent, 'stopPropagation');
      
      component.onDrop(dropEvent);
      
      expect(dropEvent.preventDefault).toHaveBeenCalled();
      expect(dropEvent.stopPropagation).toHaveBeenCalled();
      expect(spy).toHaveBeenCalledWith([mockFile]);
    });

    it('should prevent default drag over behavior', () => {
      const dragEvent = new DragEvent('dragover');
      spyOn(dragEvent, 'preventDefault');
      spyOn(dragEvent, 'stopPropagation');

      component.onDragOver(dragEvent);
      
      expect(dragEvent.preventDefault).toHaveBeenCalled();
      expect(dragEvent.stopPropagation).toHaveBeenCalled();
    });
  });

  describe('File Validation', () => {
    it('should handle invalid file format', () => {
      const invalidFile = new File(['content'], 'test.invalid', { type: 'application/octet-stream' });
      component['handleFile'](invalidFile);
      
      expect(component.fileFormatError).toBeTrue();
      expect(component.fileSizeError).toBeFalse();
      expect(component.maxFilesError).toBeFalse();
      expect(component.uploadedFiles.length).toBe(0);
    });

    it('should handle file size exceeding limit', () => {
      // Create a file larger than MAX_FILE_SIZE
      const maxSize = component.appConstants.MAX_FILE_SIZE;
      const largeContent = new Uint8Array(maxSize + 1024); // Add 1KB to ensure it's over limit
      const largeFile = new File([largeContent], 'large.txt', { type: 'text/plain' });
      
      // Verify file is actually larger than limit
      expect(largeFile.size).toBeGreaterThan(maxSize);
      
      component['handleFile'](largeFile);
      
      expect(component.fileSizeError).toBeTrue();
      expect(component.fileFormatError).toBeFalse();
      expect(component.maxFilesError).toBeFalse();
      expect(component.uploadedFiles.length).toBe(0);
    });

    it('should enforce max files limit', () => {
      component.maxFiles = 1;
      component.singleFileMode = false;
      
      const file1 = new File(['content1'], 'test1.txt', { type: 'text/plain' });
      const file2 = new File(['content2'], 'test2.txt', { type: 'text/plain' });
      
      component['handleFile'](file1);
      component['handleFile'](file2);
      
      expect(component.maxFilesError).toBeTrue();
      expect(component.uploadedFiles.length).toBe(1);
    });
  });

  describe('File Management', () => {
    let testFile: File;

    beforeEach(() => {
      testFile = new File(['content'], 'test.txt', { type: 'text/plain' });
    });

    it('should handle single file mode', () => {
      component.singleFileMode = true;
      const file1 = new File(['content1'], 'test1.txt', { type: 'text/plain' });
      const file2 = new File(['content2'], 'test2.txt', { type: 'text/plain' });
      
      component['handleFile'](file1);
      component['handleFile'](file2);
      
      expect(component.uploadedFiles.length).toBe(1);
      expect(component.uploadedFiles[0].name).toBe('test2.txt');
    });

    it('should emit files on upload', () => {
      const spy = spyOn(component.fileUploaded, 'emit');
      component['handleFile'](testFile);
      component.uploadFile();
      
      expect(spy).toHaveBeenCalledWith(testFile);
      expect(component.fileUploadedSuccess).toBeTrue();
    });

    it('should remove files correctly', () => {
      const spy = spyOn(component.filesListChanged, 'emit');
      component['handleFile'](testFile);
      component.removeFile(0);
      
      expect(component.uploadedFiles.length).toBe(0);
      expect(spy).toHaveBeenCalledWith([]);
      expect(component.fileUploadedSuccess).toBeFalse();
    });

    it('should reset upload state', () => {
      component['handleFile'](testFile);
      component.fileFormatError = true;
      component.fileSizeError = true;
      
      component.resetUpload();
      
      expect(component.uploadedFiles).toEqual([]);
      expect(component.fileUploadedSuccess).toBeFalse();
      expect(component.fileFormatError).toBeFalse();
      expect(component.fileSizeError).toBeFalse();
    });
  });

  describe('UI Elements', () => {
    it('should display correct button text based on file state', () => {
      const testFile = new File(['content'], 'test.txt', { type: 'text/plain' });
      component['handleFile'](testFile);
      fixture.detectChanges();
      
      const uploadButton = fixture.debugElement.query(By.css('.upload-button'));
      expect(uploadButton.nativeElement.textContent.trim())
        .toBe(component.appConstants.UPLOAD_BUTTON);
    });
  });
});
