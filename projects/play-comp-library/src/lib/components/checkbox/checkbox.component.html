<div class="ava-checkbox"
     [ngClass]="containerClasses"
     (click)="toggleCheckbox()"
     (keydown)="onKeyDown($event)"
     [tabindex]="disable ? -1 : 0"
     [attr.role]="'checkbox'"
     [attr.aria-checked]="indeterminate ? 'mixed' : isChecked"
     [attr.aria-disabled]="disable"
     [attr.aria-label]="label || 'Checkbox'">
  <div class="checkbox" [ngClass]="checkboxClasses">
    @if (showIcon) {
    <svg class="checkbox-icon" viewBox="0 0 24 24">
      @if (showCheckmark) {
      <path class="checkmark-path" [class.unchecking]="isUnchecking" fill="none" stroke="currentColor" stroke-width="2"
        stroke-linecap="round" stroke-linejoin="round" d="M5 12l5 5L20 7" />
      }
      @if (indeterminate) {
      <rect class="indeterminate-rect" fill="currentColor" x="6" y="10" width="12" height="4" rx="2" />
      }
    </svg>
    }
  </div>
  @if (label) {
  <span class="checkbox-label">{{ label }}</span>
  }
</div>