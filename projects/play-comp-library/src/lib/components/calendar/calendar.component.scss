.date-picker {
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  width: var(--calendar-input-width-single);
  position: relative;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--calendar-size-sm-padding);
}

.input-group {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.structured-input {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  padding: var(--calendar-input-padding) var(--calendar-input-padding-right) var(--calendar-input-padding) var(--calendar-input-padding);
  border: var(--calendar-input-border);
  border-radius: var(--calendar-input-border-radius);
  background: var(--calendar-input-background);
  cursor: text;
  width: var(--calendar-input-width-single);
  height: var(--calendar-input-height);
  transition: all 0.2s ease;
  font-size: inherit;
}

.range-structured {
  width: var(--calendar-input-width-range);
  height: var(--calendar-input-height);
  gap: var(--calendar-size-sm-padding);
}

.date-part {
  display: flex;
  align-items: center;
}

.date-segment {
  border: none;
  outline: none;
  background: transparent;
  font-size: inherit;
  font-family: inherit;
  text-align: center;
  color: var(--calendar-input-text);
  transition: background-color 0.2s ease;
}

.day-segment,
.month-segment {
  width: var(--calendar-segment-day-month-width);
}

.year-segment {
  width: var(--calendar-segment-year-width);
}

.separator {
  user-select: none;
  color: var(--calendar-input-text);
}

.range-separator {
  user-select: none;
  color: var(--calendar-input-text);
}

.input-btn {
  flex: 0 0 auto;
  margin-left: var(--calendar-input-btn-margin);
  /* Ensures the icon is at the right end */
  position: absolute;
  right: var(--calendar-input-btn-icon-right);
  background: var(--calendar-input-btn-background);
  border: none;
  cursor: pointer;
  padding: var(--calendar-input-btn-icon-padding);
  border-radius: var(--calendar-input-btn-border-radius);
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--calendar-input-btn-hover-background);
  }
}

.calendar-popup {
  width: var(--calendar-input-width-single);
  min-width: var(--calendar-input-width-single);
  box-sizing: border-box;
  position: absolute;
  margin-top: var(--calendar-popup-margin-top);
  z-index: var(--calendar-popup-z-index);
  background: var(--calendar-popup-background);
  border: var(--calendar-popup-border);
  border-radius: var(--calendar-popup-border-radius);
  box-shadow: var(--calendar-popup-shadow);
  padding: var(--calendar-popup-padding);
  height: auto;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.calendar-popup.surface-glass {
  background: var(--calendar-glass-background-fallback); // fallback, will be overridden by variant
  backdrop-filter: var(--calendar-glass-backdrop-filter);
  -webkit-backdrop-filter: var(--calendar-glass-backdrop-filter);
  border: var(--calendar-glass-border);
  border-radius: var(--calendar-popup-border-radius, 16px);
  box-shadow: var(--calendar-glass-box-shadow);
  color: var(--calendar-glass-text-color);
}
.calendar-popup.surface-glass.medium {
  background: var(--calendar-glass-background-medium);
}
.calendar-popup.surface-glass.strong {
  background: var(--calendar-glass-background-strong);
}
.calendar-popup.surface-glass.max {
  background: var(--calendar-glass-background-max);
}


.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-controls {
  display: flex;
  gap: var(--calendar-nav-controls-gap-large);
}

.month-year-display {
  display: flex;
  gap: var(--calendar-month-year-gap);
  align-items: center;
}

.month-selector,
.year-selector {
  padding: var(--calendar-month-year-selector-padding);
  background: transparent;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-md-font);
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  border: none;
  outline: none;
}

.month-selector {
  color: var(--calendar-month-text);
}

.year-selector {
  color: var(--calendar-year-text);
}

.month-selector.selected {
  color: var(--calendar-month-year-selected-color);
}

.year-selector.selected {
  color: var(--calendar-month-year-selected-color);
}

.month-selector:not(.selected) {
  color: var(--calendar-year-text);
}

.year-selector:not(.selected) {
  color: var(--calendar-year-text);
}

.month-selector:hover,
.year-selector:hover {
  opacity: 0.8;
}

.nav-controls {
  display: flex;
  gap: var(--calendar-nav-controls-gap);
}

.nav-btn {
  background-color: var(--calendar-nav-button-background-color);
  border: none;
  cursor: pointer;
  padding: var(--calendar-nav-button-padding);
  border-radius: var(--calendar-nav-button-border-radius);
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--calendar-nav-button-height-width);
  height: var(--calendar-nav-button-height-width);
  box-shadow: var(--calendar-nav-shadow);

  &:hover {
    background: var(--calendar-nav-button-background);
    box-shadow: var(--calendar-nav-shadow-hover);
  }
}

.calendar-grid {
  margin-top: var(--calendar-grid-margin-top);
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--calendar-size-sm-padding);
  margin-bottom: var(--calendar-size-sm-padding);
}

.weekday {
  height: auto;
  text-align: center;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  color: var(--calendar-day-name-text);
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Replace the existing range-related styles in your SCSS file with this:

.days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--calendar-size-sm-padding);
}

.day {
  aspect-ratio: 1 / 1; // always square, responsive
  width: 100%;         // fill the grid cell
  height: auto;        // let aspect-ratio control height
  padding: 0;          // remove extra padding for perfect shape
  text-align: center;
  cursor: pointer;
  border: none;
  border-radius: var(--calendar-cell-border-radius);
  background: none;
  font-family: var(--calendar-font-family);
  font-size: var(--calendar-size-sm-font);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); // slow, smooth animation
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--calendar-date-text);

  &.circle-selector {
    border-radius: var(--calendar-circle-selector-radius) !important;
  }

  &.square-selector {
    border-radius: var(--calendar-square-selector-radius) !important; // or 0 for perfect square
  }

  &:hover {
    background: var(--calendar-date-hover-background);
    color: var(--calendar-date-selected-text);
  }

  &.other-month {
    color: var(--calendar-date-disabled-text);
  }

  &.today {
    border: var(--calendar-date-today-border);
  }

  &.selected {
    background: var(--calendar-date-selected-background);
    color: var(--calendar-date-selected-text);
  }
}

// Range styling - properly curved like the requirement
.day.in-range {
  background: var(--calendar-date-hover-background);
  color: var(--calendar-range-start-text);
  border-radius: var(--calendar-cell-border-radius);
  position: relative;
  z-index: 1;
}

.day.range-start,
.day.range-end {
  background: var(--calendar-range-start-background) !important;
  color: var(--calendar-range-start-text) !important;
  border-radius: var(--calendar-cell-border-radius) !important;
  position: relative;
  z-index: 2;
}

// Handle adjacent start/end dates (no gap)
.day.range-start.range-end {
  border-radius: var(--calendar-cell-border-radius) !important;
  background: var(--calendar-range-start-background) !important;
  color: var(--calendar-range-start-text) !important;
}

// Remove the continuous background - keep individual rounded cells
.day.in-range::before,
.day.range-start::before,
.day.range-end::before {
  display: none;
}

// Single date range (start and end are the same)
.day.range-start.range-end::before {
  display: none;
}

// Handle first column (Sunday) range styling
.days .day.in-range:nth-child(7n+1)::before,
.days .day.range-start:nth-child(7n+1)::before {
  left: 0;
}

// Handle last column (Saturday) range styling
.days .day.in-range:nth-child(7n)::before,
.days .day.range-end:nth-child(7n)::before {
  right: 0;
}

// Today styling adjustments for range dates
.day.today.in-range,
.day.today.range-start,
.day.today.range-end {
  border: none !important;
  box-shadow: none !important;
}

// Ensure today border shows for single selected dates
.day.today:not(.in-range):not(.range-start):not(.range-end) {
  border: var(--calendar-date-today-border);
}

// Override hover effects for range dates
.day.range-start:hover,
.day.range-end:hover {
  background: var(--calendar-range-start-background) !important;
  color: var(--calendar-range-start-text) !important;
}

.day.in-range:hover {
  background: var(--calendar-date-hover-background);
  color: var(--calendar-range-start-text);
}