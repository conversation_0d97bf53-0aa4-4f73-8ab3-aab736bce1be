import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SpinnerComponent } from './spinner.component';
import { By } from '@angular/platform-browser';

describe('SpinnerComponent', () => {
  let component: SpinnerComponent;
  let fixture: ComponentFixture<SpinnerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SpinnerComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(SpinnerComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should apply correct size class', () => {
    component.size = 'lg';
    expect(component.sizeClass).toBe('size-lg');
  });

  it('should return true for standard spinner types', () => {
    component.type = 'dotted';
    expect(component.isStandardSpinner).toBeTrue();
  });

  it('should return false for non-standard spinner types', () => {
    component.type = 'gradient';
    expect(component.isStandardSpinner).toBeFalse();
  });

  it('should return correct progressClass based on progressIndex', () => {
    component.progressIndex = 20;
    expect(component.progressClass).toBe('rotate-25');

    component.progressIndex = 45;
    expect(component.progressClass).toBe('rotate-50');

    component.progressIndex = 70;
    expect(component.progressClass).toBe('rotate-75');

    component.progressIndex = 90;
    expect(component.progressClass).toBe('rotate-100');
  });

  it('should return empty string if progressIndex is undefined', () => {
    component.progressIndex = undefined;
    expect(component.progressClass).toBe('');
  });

  it('should render standard spinner', () => {
    component.type = 'dotted';
    component.animation = true;
    component.color = 'success';
    component.size = 'md';
    fixture.detectChanges();

    const el = fixture.debugElement.query(By.css('.spinner'));
    expect(el).toBeTruthy();
    expect(el.nativeElement.classList).toContain('dotted');
    expect(el.nativeElement.classList).toContain('animated');
    expect(el.nativeElement.classList).toContain('success');
  });

  it('should render gradient spinner', () => {
    component.type = 'gradient';
    component.size = 'sm';
    component.animation = false;
    component.color = 'danger';
    fixture.detectChanges();

    const el = fixture.debugElement.query(By.css('.gradient'));
    expect(el).toBeTruthy();
    expect(el.nativeElement.classList).toContain('spinner');
    expect(el.nativeElement.classList).toContain('danger');
    expect(el.nativeElement.classList).not.toContain('animated');
  });

  it('should render double spinner', () => {
    component.size = 'xl';
    component.animation = true;
    fixture.detectChanges();

    const outer = fixture.debugElement.query(By.css('.spinner.double .double-outer'));
    const inner = fixture.debugElement.query(By.css('.spinner.double .double-inner'));

    // expect(outer).withContext('double outer ring should exist').toBeTruthy();
    // expect(inner).withContext('double inner ring should exist').toBeTruthy();

    // expect(outer.nativeElement.classList).toContain('purple');
    // expect(inner.nativeElement.classList).toContain('purple');
  });

});
