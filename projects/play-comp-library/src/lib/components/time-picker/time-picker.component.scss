.time-picker-container {
  position: relative;

  .time-picker-input {
    display: flex;
    align-items: center;
    border: var(--timepicker-border);
    border-radius: var(--timepicker-border-radius);
    box-shadow: var(--timepicker-shadow);
    cursor: pointer;
    height: var(--timepicker-input-height);
    border-radius: var(--timepicker-input-border-radius);
    width: 100%;
    min-width: var(--timepicker-input-min-width);
    transition: var(--timepicker-time-item-transition);
    outline: none;
    padding-left: var(--timepicker-input-padding-horizontal);
    padding-right: var(--timepicker-input-padding-horizontal);
    background: var(--timepicker-background);

    .time-display {
      flex: 1;
      text-align: left;
      font: var(--timepicker-display-font);
      color: var(--timepicker-display-text);
      cursor: pointer;
      transition: var(--timepicker-time-item-transition);

      &:hover {
        opacity: 0.8;
      }
    }



    .time-scroll-container {
      display: flex;
      align-items: center;
      flex: 1; // Take remaining space for scroll mode
      height: 100%;
      position: relative;
      z-index: 1;
      outline: none;
      background: var(--timepicker-scroll-background);
      gap: var(--timepicker-scroll-gap-hours-minutes);

      // Inline input overlay for scroll mode
      .inline-input-overlay {
        position: absolute;
        z-index: 10;
        background: var(--timepicker-input-background);
        border: var(--timepicker-input-border-width) solid var(--timepicker-input-border-color);
        border-radius: var(--timepicker-input-border-radius-small);
        padding: var(--timepicker-input-padding-small);
        box-shadow: var(--timepicker-input-shadow-overlay);
        height: var(--timepicker-time-item-height);
        display: flex;
        align-items: center;
        justify-content: center;

        .inline-scroll-input {
          border: none;
          outline: none;
          background: var(--timepicker-input-background);
          text-align: center;
          font: var(--timepicker-time-item-font);
          color: var(--timepicker-input-text);
          width: 100%;
          height: 100%;
        }
      }

      .separator {
        font: var(--timepicker-time-item-font);
        margin: 0;
        flex-shrink: 0;

        &:last-of-type {
          margin-left: calc(var(--timepicker-scroll-gap-minutes-period) - var(--timepicker-scroll-gap-hours-minutes)); // Extra space before AM/PM
        }
      }

      .time-scroll-column {
        flex: 1;
        height: 100%;
        position: relative;

        &.period-column {
          flex: 0.8; // Smaller width for AM/PM
        }

        .scroll-area {
          height: 100%;
          overflow-y: auto;
          scroll-behavior: smooth;
          max-height: var(--timepicker-scroll-max-height);
          scrollbar-width: none;
          -ms-overflow-style: none;

          &::-webkit-scrollbar {
            display: none;
          }

          .time-item {
            height: var(--timepicker-time-item-height);
            display: flex;
            align-items: center;
            justify-content: center;
            font: var(--timepicker-time-item-font);
            cursor: pointer;
            transition: var(--timepicker-time-item-transition);
            color: var(--timepicker-time-item-text);
            background: var(--timepicker-time-item-background);
            padding: var(--timepicker-time-item-padding);
            border-radius: var(--timepicker-time-item-border-radius);

            &:hover {
              background-color: var(--timepicker-time-item-hover-background);
              color: var(--timepicker-time-item-hover-text);
            }

            &.selected {
              color: var(--timepicker-time-item-selected-text);
              font-weight: var(--timepicker-time-item-selected-font-weight);
            }

            &.padding-item {
              pointer-events: none;
              opacity: 0;
              cursor: default;
            }

            &.hidden-item {
              visibility: hidden;
            }
          }
        }
      }
    }

    .icon-wrapper {
      margin-left: var(--timepicker-icon-margin-left);
      flex-shrink: 0;
      width: var(--timepicker-icon-size);
      height: var(--timepicker-icon-size);
      border-radius: var(--timepicker-icon-border-radius);
      background-color: var(--timepicker-icon-background);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: var(--timepicker-time-item-transition);
      cursor: pointer;
      position: relative;
      outline: none;
      padding: var(--timepicker-icon-padding);

      ava-icon {
        pointer-events: none;
        color: var(--timepicker-icon-color);
      }
    }
  }
}