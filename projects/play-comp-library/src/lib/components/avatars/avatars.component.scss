

  .avatar-wrapper {
    display: inline-flex;
    padding: var(--avatar-padding);
    gap: var(--avatar-container-gap);
  }

  .avatar {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    flex-shrink: var(--avatar-flex-shrink);
    aspect-ratio: var(--avatar-aspect-ratio);
    background-color: var(--avatar-background);
    background-size: cover;
    background-position: cover;
    background-repeat: no-repeat;

    position: relative;

    &--active {
      border: var(--avatar-border-thickness) solid rgb(var(--rgb-brand-primary));
    }

    &--processed-done {
      border: var(--avatar-border-thickness) solid transparent;
      background-clip: padding-box;

      &::before {
        content: '';
        position: absolute;
        top: var(--avatar-proccessdone-position);
        left: var(--avatar-proccessdone-position);
        right: var(--avatar-proccessdone-position);
        bottom: var(--avatar-proccessdone-position);
        padding: var(--avatar-processdone-border-thickness);
        -webkit-mask: var(--avatar-webkit-mask);
        -webkit-mask-composite: xor;
        mask: var(--avatar-mask);
        mask-composite: exclude;

        // Default animation properties (will be overridden by dynamic styles)
        animation: continuousRotatingGradient var(--avatar-animation-speed) linear infinite;
      }

      // For pill shape (circular) - use conic gradient with default colors
      &.avatar--pill::before {
        border-radius: var(--avatar-pill-border-radius);
        background: conic-gradient(
          from 0deg,
          #E91E63 0deg,
          #FF9800 180deg,
          #E91E63 360deg
        );
        animation: continuousRotatingGradient var(--avatar-animation-speed) linear infinite;
      }

      // For square shapes - use proper border animation with default colors
      &.avatar--square::before {
        background: linear-gradient(
          45deg,
          #E91E63 0%,
          #FF9800 25%,
          #E91E63 50%,
          #FF9800 75%,
          #E91E63 100%
        );
        background-size: 400% 400%;
        animation: squareBorderFlow var(--avatar-animation-speed) linear infinite;
      }

      // Square border radius for different sizes
      &.avatar--large.avatar--square::before {
        border-radius: var(--avatar-border-radius-lg);
      }

      &.avatar--medium.avatar--square::before {
        border-radius: var(--avatar-border-radius-md);
      }

      &.avatar--small.avatar--square::before {
        border-radius: var(--avatar-border-radius-sm);
      }
    }

    // Large size variations
    &--ultra-large {
      width: var(--avatar-size-ul);
      height: var(--avatar-size-ul);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-ul);
      }
    }
    &--extra-large {
      width: var(--avatar-size-xl);
      height: var(--avatar-size-xl);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-xl);
      }
    }
    &--large {
      width: var(--avatar-size-lg);
      height: var(--avatar-size-lg);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-lg);
      }
    }

    // Medium size variations
    &--medium {
      width: var(--avatar-size-md);
      height: var(--avatar-size-md);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-md);
      }
    }

    // Small size variations
    &--small {
      width: var(--avatar-size-sm);
      height: var(--avatar-size-sm);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-sm);
      }
    }

    &--extra-small {
      width: var(--avatar-size-xs);
      height: var(--avatar-size-xs);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-xs);
      }
    }
     &--ultra-small {
      width: var(--avatar-size-us);
      height: var(--avatar-size-us);

      &.avatar--pill {
        border-radius: var(--avatar-border-radius);
      }

      &.avatar--square {
        border-radius: var(--avatar-border-radius-us);
      }
    }
  }

// Badge positioning
.avatar-badge {
  position: absolute;
  top: var(--avatar-badge-poisition);
  right: var(--avatar-badge-poisition);
  z-index: 1;
}

.avatar-text-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: var(--avatar-container-gap);

  // Size-specific gaps for text container
  &[data-size="ultra-large"] {
    gap: var(--avatar-wrapper-gap-ul);
  }

  &[data-size="extra-large"] {
    gap: var(--avatar-wrapper-gap-xl);
  }

  &[data-size="large"] {
    gap: var(--avatar-wrapper-gap-lg);
  }

  &[data-size="medium"] {
    gap: var(--avatar-wrapper-gap-md);
  }

  &[data-size="small"] {
    gap: var(--avatar-wrapper-gap-sm);
  }

  &[data-size="extra-small"] {
    gap: var(--avatar-wrapper-gap-xs);
  }

  &[data-size="ultra-small"] {
    gap: var(--avatar-wrapper-gap-us);
  }
}

.avatar-text {
  text-align: center;
  font-style: normal;

  // Status text styling (Primary text - e.g., "Profile Name")
  &--status {
    color: var(--avatar-status-color);
    font-family: var(--avatar-status-font-family);
    font-weight: var(--avatar-status-font-weight);
    line-height: var(--avatar-status-line-height);


    // Ultra large size styling
    &[data-size="extra-large"] {
      font-size: var(--avatar-status-font-size-ul);
    }


    // Large size styling
    &[data-size="large"] {
      font-size: var(--avatar-status-font-size-lg);
    }

    // Medium size styling
    &[data-size="medium"] {
      font-size: var(--avatar-status-font-size-md);
    }

    // Small size styling
    &[data-size="small"] {
      font-size: var(--avatar-status-font-size-sm);
    }

  }

  &--profile {
    color: var(--avatar-profile-color);
    font-family: var(--avatar-profile-font-family);
    font-weight: var(--avatar-profile-font-weight);


    // Extra large size styling
    &[data-size="extra-large"] {
      font-size: var(--avatar-profile-font-size-xl);
    }

    // Large size styling
    &[data-size="large"] {
      font-size: var(--avatar-profile-font-size-lg);
    }

    // Medium size styling
    &[data-size="medium"] {
      font-size: var(--avatar-profile-font-size-md);
    }

    // Small size styling
    &[data-size="small"] {
      font-size: var(--avatar-profile-font-size-sm);
    }
  }

  // Additional text styling (Tertiary text - e.g., additional role info)
  &--additional {
    color: var(--avatar-additional-color);
    font-family: var(--avatar-additional-font-family);

    // Extra large size styling
    &[data-size="extra-large"] {
      font-size: var(--avatar-additional-font-size-xl);
    }

    // Large size styling
    &[data-size="large"] {
      font-size: var(--avatar-additional-font-size-lg);
    }

    // Medium size styling
    &[data-size="medium"] {
      font-size: var(--avatar-additional-font-size-md);
    }

    // Small size styling
    &[data-size="small"] {
      font-size: var(--avatar-additional-font-size-sm);
    }
  }
}

// Animation keyframes remain the same
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Continuous rotating gradient animation for circular avatars
@keyframes continuousRotatingGradient {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Animation for square avatars - flows gradient around the border
@keyframes squareBorderFlow {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}
