  <div class="avatar-wrapper">
    <div
      [class]="avatarClasses"
      [style.background-image]="imageUrl ? 'url(' + imageUrl + ')' : 'none'"
      (load)="onImageLoad()"
    >
      <ava-badges
        *ngIf="hasBadge"
        [state]="badgeState!"
        [size]="badgeSize!"
        [count]="badgeCount"
        class="avatar-badge"
      >
      </ava-badges>
    </div>
    <!-- Text labels - can have status, profile, and additional text -->
    <div *ngIf="hasAnyText" class="avatar-text-container" [attr.data-size]="size">
      <div *ngIf="hasStatusText" class="avatar-text avatar-text--status" [attr.data-size]="size">
        {{ statusText }}
      </div>
      <div *ngIf="hasProfileText" class="avatar-text avatar-text--profile" [attr.data-size]="size">
        {{ profileText }}
      </div>
      <div *ngIf="hasAdditionalText" class="avatar-text avatar-text--additional" [attr.data-size]="size">
        {{ additionalText }}
      </div>
    </div>
  </div>
