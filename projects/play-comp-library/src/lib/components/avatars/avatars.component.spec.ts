import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AvatarsComponent } from './avatars.component';

fdescribe('AvatarsComponent', () => {
  let fixture: ComponentFixture<AvatarsComponent>;
  let component: AvatarsComponent;

  const defaultColors = ['#E91E63', '#FF9800'];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AvatarsComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(AvatarsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('avatarClasses: default includes size and shape', () => {
    // default: size=large, shape=pill
    const cls = component.avatarClasses;
    expect(cls).toContain('avatar');
    expect(cls).toContain('avatar--large');
    expect(cls).toContain('avatar--pill');
  });

  it('avatarClasses: adds active and processed classes with unique id', () => {
    component.active = true;
    component.processedanddone = true;
    const cls = component.avatarClasses;
    expect(cls).toContain('avatar--active');
    expect(cls).toContain('avatar--processed-done');
    // uniqueId should be present in class list
    const id = (component as any).uniqueId as string;
    expect(id).toBeTruthy();
    expect(cls).toContain(id);
  });

  it('avatarClasses: shape square reflected', () => {
    component.shape = 'square';
    const cls = component.avatarClasses;
    expect(cls).toContain('avatar--square');
    expect(cls).not.toContain('avatar--pill');
  });

  it('hasBadge: true when badgeState set, or when badgeCount set; false otherwise', () => {
    component.badgeState = undefined as any;
    component.badgeCount = undefined;
    expect(component.hasBadge).toBeFalse();

    component.badgeCount = 0; 
    expect(component.hasBadge).toBeFalse();

    component.badgeCount = 3;
    expect(component.hasBadge).toBeTrue();

    component.badgeCount = undefined;
    (component as any).badgeState = 'success';
    expect(component.hasBadge).toBeTrue();
  });

  it('text flags and hasAnyText aggregate', () => {
    component.statusText = undefined;
    component.profileText = undefined;
    component.additionalText = undefined;
    expect(component.hasStatusText).toBeFalse();
    expect(component.hasProfileText).toBeFalse();
    expect(component.hasAdditionalText).toBeFalse();
    expect(component.hasAnyText).toBeFalse();

    component.statusText = 'Online';
    expect(component.hasStatusText).toBeTrue();
    expect(component.hasAnyText).toBeTrue();

    component.statusText = undefined;
    component.profileText = 'MJ';
    expect(component.hasProfileText).toBeTrue();
    expect(component.hasAnyText).toBeTrue();

    component.profileText = undefined;
    component.additionalText = 'More';
    expect(component.hasAdditionalText).toBeTrue();
    expect(component.hasAnyText).toBeTrue();
  });

  it('hasCustomColors: false for defaults; true for custom', () => {
    component.gradientColors = [...defaultColors];
    const hasDefault = (component as any).hasCustomColors();
    expect(hasDefault).toBeFalse();

    component.gradientColors = ['#111111', '#222222', '#333333'];
    const hasCustom = (component as any).hasCustomColors();
    expect(hasCustom).toBeTrue();
  });

  it('ngOnInit: with default colors does not create style element', () => {
    component.processedanddone = true;
    component.gradientColors = [...defaultColors];
    component.ngOnInit();
    expect((component as any).styleElement).toBeNull();
  });

  it('ngOnInit: with custom colors creates dynamic styles with unique id and speed', () => {
    component.processedanddone = true;
    component.gradientColors = ['#111111', '#222222', '#333333'];
    component.animationSpeed = 3;
    component.ngOnInit();

    const styleEl: HTMLStyleElement | null = (component as any).styleElement;
    expect(styleEl).toBeTruthy();
    expect(document.head.contains(styleEl!)).toBeTrue();

    const css = styleEl!.textContent || '';
    const uniq = (component as any).uniqueId as string;
    expect(css).toContain(uniq);
    expect(css).toContain('animation-duration: 3s');
    expect(css).toContain('conic-gradient');
    expect(css).toContain('linear-gradient');
  });

  it('createDynamicStyles: recreates style element and removes previous one', () => {
    component.processedanddone = true;
    component.gradientColors = ['#abc', '#def', '#123'];
    (component as any).createDynamicStyles();
    const first: HTMLStyleElement = (component as any).styleElement;

    // call again -> old removed, new created
    (component as any).createDynamicStyles();
    const second: HTMLStyleElement = (component as any).styleElement;

    expect(first).not.toBe(second);
    expect(document.head.contains(first)).toBeFalse();
    expect(document.head.contains(second)).toBeTrue();
    expect((second.textContent || '').length).toBeGreaterThan(0);
  });

  it('ngOnDestroy: removes injected style element', () => {
    component.processedanddone = true;
    component.gradientColors = ['#111', '#222'];
    (component as any).createDynamicStyles();

    const styleEl: HTMLStyleElement = (component as any).styleElement;
    expect(document.head.contains(styleEl)).toBeTrue();

    component.ngOnDestroy();
    expect(document.head.contains(styleEl)).toBeFalse();
  });

  it('onImageLoad: logs only when processedanddone is true', () => {
    spyOn(console, 'log');

    component.processedanddone = false;
    component.onImageLoad();
    expect(console.log).not.toHaveBeenCalled();

    component.processedanddone = true;
    component.onImageLoad();
    expect(console.log).toHaveBeenCalledWith(
      'Processed and done state - continuous rotation started'
    );
  });
});
