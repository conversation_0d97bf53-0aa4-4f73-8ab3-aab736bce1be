<nav *ngIf="displayedBreadcrumbs.length">
  <ul class="breadcrumb" [ngClass]="sizeClass">
    <!-- Regular breadcrumbs display -->
    <ng-container *ngFor="let breadcrumb of displayedBreadcrumbs; let last = last; let i = index">
      <li [ngClass]="{
            'active': breadcrumb.active,
            'inactive': !breadcrumb.active
        }">

        <!-- Breadcrumb link (not last item) -->
        <a *ngIf="!last"
           [routerLink]="breadcrumb.url"
           (click)="onBreadcrumbClick($event, getOriginalIndex(i))"
           [attr.tabindex]="0"
           [attr.aria-current]="breadcrumb.active ? 'page' : null"
           class="breadcrumb-item">

          <!-- Optional icon for breadcrumb item -->
          <ava-icon *ngIf="breadcrumb.icon"
                    [iconName]="breadcrumb.icon"
                    [iconSize]="16"
                    [cursor]="false"
                    [iconColor]="getIconColor(false)">
          </ava-icon>

          <!-- Optional text for breadcrumb item -->
          <span *ngIf="breadcrumb.label">{{ breadcrumb.label }}</span>
        </a>

        <!-- Current breadcrumb (last item) -->
        <span *ngIf="last" class="breadcrumb-item">
          <!-- Optional icon for current item -->
          <ava-icon *ngIf="breadcrumb.icon"
                    [iconName]="breadcrumb.icon"
                    [iconSize]="16"
                    [cursor]="false"
                    [iconColor]="'currentColor'">
          </ava-icon>

          <!-- Optional text for current item -->
          <span *ngIf="breadcrumb.label">{{ breadcrumb.label }}</span>
        </span>

        <!-- Separator icon (not after last item) -->
        <ava-icon *ngIf="!last"
                  [iconName]="separatorIcon"
                  [iconSize]="separatorSize"
                  [cursor]="false"
                  class="breadcrumb-separator">
        </ava-icon>
      </li>

      <!-- Show ellipsis after first item if needed -->
      <li *ngIf="shouldShowEllipsis && i === 0" class="breadcrumb-ellipsis">
        <span class="ellipsis">...</span>
        <!-- Separator after ellipsis -->
        <ava-icon [iconName]="separatorIcon"
                  [iconSize]="separatorSize"
                  [cursor]="false"
                  class="breadcrumb-separator">
        </ava-icon>
      </li>
    </ng-container>
  </ul>
</nav>
