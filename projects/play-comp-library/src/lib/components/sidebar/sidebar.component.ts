import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { ButtonComponent } from '../button/button.component';

@Component({
  selector: 'ava-sidebar',
  imports: [CommonModule, ButtonComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class SidebarComponent implements OnInit, OnChanges {
  @Input() width: string = '260px';
  @Input() collapsedWidth: string = '50px';
  @Input() height: string = '100vh';
  @Input() hoverAreaWidth: string = '10px';
  @Input() showCollapseButton: boolean = false;
  @Input() buttonVariant: 'inside' | 'outside' = 'inside';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() isCollapsed: boolean = false;
  @Input() position: 'left' | 'right' = 'left';

  @Output() collapseToggle = new EventEmitter<boolean>();

  private _isCollapsed = false;

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this._isCollapsed = this.isCollapsed;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isCollapsed'] && !changes['isCollapsed'].firstChange) {
      this._isCollapsed = this.isCollapsed;
      this.cdr.markForCheck();
    }
  }

  toggleCollapse(): void {
    this._isCollapsed = !this._isCollapsed;
    this.collapseToggle.emit(this._isCollapsed);
    this.cdr.markForCheck();
  }

  get sidebarWidth(): string {
    return this._isCollapsed ? this.collapsedWidth : this.width;
  }

  get collapsed(): boolean {
    return this._isCollapsed;
  }

  get isRightPositioned(): boolean {
    return this.position === 'right';
  }

  get collapseButtonIcon(): string {
    if (this.position === 'right') {
      return this._isCollapsed ? 'ArrowLeft' : 'ArrowRight';
    }
    return this._isCollapsed ? 'ArrowRight' : 'ArrowLeft';
  }
}