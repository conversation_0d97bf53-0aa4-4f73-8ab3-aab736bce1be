import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SidebarComponent } from './sidebar.component';
import { Component } from '@angular/core';
import { By } from '@angular/platform-browser';

@Component({
  selector: 'ava-button',
  template: '<button (click)="click.emit()"></button>',
  standalone: true
})
class MockButtonComponent {
  click = jasmine.createSpyObj('EventEmitter', ['emit']);
}

describe('SidebarComponent', () => {
  let component: SidebarComponent;
  let fixture: ComponentFixture<SidebarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SidebarComponent, MockButtonComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(SidebarComponent);
    component = fixture.componentInstance;
  });

  it('should create the component', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should calculate sidebarWidth based on collapse state', () => {
    component.isCollapsed = false;
    component.width = '300px';
    component.collapsedWidth = '50px';
    component.ngOnInit();
    expect(component.sidebarWidth).toBe('300px');
    component.toggleCollapse();
    expect(component.sidebarWidth).toBe('50px');
  });

  it('should render header, content, and footer when visible', () => {
    component.showHeader = true;
    component.showFooter = true;
    fixture.detectChanges();
    const header = fixture.debugElement.query(By.css('.sidebar-header'));
    const content = fixture.debugElement.query(By.css('.sidebar-content'));
    const footer = fixture.debugElement.query(By.css('.sidebar-footer'));
    expect(header).toBeTruthy();
    expect(content).toBeTruthy();
    expect(footer).toBeTruthy();
  });

  it('should not render header and footer if disabled', () => {
    component.showHeader = false;
    component.showFooter = false;
    fixture.detectChanges();
    const header = fixture.debugElement.query(By.css('.sidebar-header'));
    const footer = fixture.debugElement.query(By.css('.sidebar-footer'));
    expect(header).toBeFalsy();
    expect(footer).toBeFalsy();
  });

  describe('Position functionality', () => {
    it('should default to left position', () => {
      expect(component.position).toBe('left');
      expect(component.isRightPositioned).toBe(false);
    });

    it('should detect right position correctly', () => {
      component.position = 'right';
      expect(component.isRightPositioned).toBe(true);
    });

    it('should apply right-positioned class when position is right', () => {
      component.position = 'right';
      fixture.detectChanges();
      const container = fixture.debugElement.query(By.css('.ava-sidebar-container'));
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(container.nativeElement.classList.contains('right-positioned')).toBe(true);
      expect(sidebar.nativeElement.classList.contains('right-positioned')).toBe(true);
    });

    it('should not apply right-positioned class when position is left', () => {
      component.position = 'left';
      fixture.detectChanges();
      const container = fixture.debugElement.query(By.css('.ava-sidebar-container'));
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(container.nativeElement.classList.contains('right-positioned')).toBe(false);
      expect(sidebar.nativeElement.classList.contains('right-positioned')).toBe(false);
    });

    it('should return correct collapse button icon for left position', () => {
      component.position = 'left';
      component.ngOnInit();

      // When not collapsed, should show ArrowLeft
      expect(component.collapseButtonIcon).toBe('ArrowLeft');

      // When collapsed, should show ArrowRight
      component.toggleCollapse();
      expect(component.collapseButtonIcon).toBe('ArrowRight');
    });

    it('should return correct collapse button icon for right position', () => {
      component.position = 'right';
      component.ngOnInit();

      // When not collapsed, should show ArrowRight
      expect(component.collapseButtonIcon).toBe('ArrowRight');

      // When collapsed, should show ArrowLeft
      component.toggleCollapse();
      expect(component.collapseButtonIcon).toBe('ArrowLeft');
    });
  });

  describe('Collapse functionality', () => {
    it('should emit collapseToggle event when toggled', () => {
      spyOn(component.collapseToggle, 'emit');
      component.toggleCollapse();
      expect(component.collapseToggle.emit).toHaveBeenCalledWith(true);

      component.toggleCollapse();
      expect(component.collapseToggle.emit).toHaveBeenCalledWith(false);
    });

    it('should update collapsed state when isCollapsed input changes', () => {
      component.isCollapsed = true;
      component.ngOnChanges({
        isCollapsed: {
          currentValue: true,
          previousValue: false,
          firstChange: false,
          isFirstChange: () => false
        }
      });
      expect(component.collapsed).toBe(true);
    });

    it('should apply collapsed class when sidebar is collapsed', () => {
      component.toggleCollapse();
      fixture.detectChanges();
      const sidebar = fixture.debugElement.query(By.css('.ava-sidebar'));
      expect(sidebar.nativeElement.classList.contains('collapsed')).toBe(true);
    });
  });

});