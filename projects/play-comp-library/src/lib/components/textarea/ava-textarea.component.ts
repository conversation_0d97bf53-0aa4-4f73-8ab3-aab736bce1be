import {
  ChangeDetectionStrategy,
  Component,
  Input,
  forwardRef,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  CUSTOM_ELEMENTS_SCHEMA,
  AfterViewInit,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export type TextareaVariant =
  | 'default'
  | 'primary'
  | 'success'
  | 'error'
  | 'warning'
  | 'info'
  | 'processing';
export type TextareaSize = 'sm' | 'md' | 'lg';

@Component({
  selector: 'ava-textarea',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './ava-textarea.component.html',
  styleUrl: './ava-textarea.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AvaTextareaComponent),
      multi: true,
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AvaTextareaComponent
  implements ControlValueAccessor, AfterViewInit
{
  @Input() label = '';
  @Input() placeholder = '';
  @Input() variant: TextareaVariant = 'default';
  @Input() size: TextareaSize = 'md';
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() error = '';
  @Input() helper = '';
  @Input() rows = 3;
  @Input() id = '';
  @Input() name = '';
  @Input() maxlength?: number;
  @Input() minlength?: number;
  @Input() required = false;
  @Input() fullWidth = false;
  @Input() style?: Record<string, string>;
  @Input() resizable = true;
  @Input() autoResize = false;
  @Input() processing = false;
  @Input() processingGradientBorder = false;
  @Input() processingGradientColors: string[] = [
    '#e91e63',
    '#fee140',
    '#ff9800',
    '#047857',
    '#ff9800',
    '#fee140',
    '#e91e63',
  ];

  @Output() textareaBlur = new EventEmitter<Event>();
  @Output() textareaFocus = new EventEmitter<Event>();
  @Output() textareaInput = new EventEmitter<Event>();
  @Output() textareaChange = new EventEmitter<Event>();
  @Output() iconStartClick = new EventEmitter<Event>();
  @Output() iconEndClick = new EventEmitter<Event>();

  value = '';
  isFocused = false;

  @ViewChild('textareaElement')
  textareaElement!: ElementRef<HTMLTextAreaElement>;

  constructor(private cdr: ChangeDetectorRef) {}

  ngAfterViewInit(): void {
    // Initialize auto-resize for existing content
    if (this.autoResize && this.textareaElement) {
      setTimeout(() => {
        this.adjustTextareaHeight(this.textareaElement.nativeElement);
      });
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this.value = value || '';
    this.cdr.markForCheck();
  }
  // These are set by Angular forms
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onChange: (value: string) => void = () => {};
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onTouched: () => void = () => {};
  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }
  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.cdr.markForCheck();
  }

  // Event handlers
  onInput(event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.value = target.value;
    this.onChange(this.value);

    // Auto-resize functionality
    if (this.autoResize) {
      this.adjustTextareaHeight(target);
    }

    this.textareaInput.emit(event);
  }
  onFocus(event: Event): void {
    this.isFocused = true;
    this.textareaFocus.emit(event);
  }
  onBlur(event: Event): void {
    this.isFocused = false;
    this.onTouched();
    this.textareaBlur.emit(event);
  }
  onChange_(event: Event): void {
    this.textareaChange.emit(event);
  }

  // Auto-resize functionality
  private adjustTextareaHeight(textarea: HTMLTextAreaElement): void {
    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Set height to scrollHeight to fit content
    textarea.style.height = textarea.scrollHeight + 'px';
  }

  // Icon click handlers
  onIconStartClick(event: Event): void {
    if (this.disabled || this.readonly) return;
    event.stopPropagation();
    this.iconStartClick.emit(event);
  }
  onIconEndClick(event: Event): void {
    if (this.disabled || this.readonly) return;
    event.stopPropagation();
    this.iconEndClick.emit(event);
  }
  onIconKeydown(event: KeyboardEvent, position: 'start' | 'end'): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (position === 'start') this.onIconStartClick(event);
      else this.onIconEndClick(event);
    }
  }

  // Computed properties
  get hasError(): boolean {
    return !!this.error;
  }
  get hasHelper(): boolean {
    return !!this.helper;
  }
  get inputId(): string {
    return this.id || `ava-textarea-${Math.random().toString(36).substr(2, 9)}`;
  }
  get errorId(): string {
    return `${this.inputId}-error`;
  }
  get helperId(): string {
    return `${this.inputId}-helper`;
  }
  get ariaDescribedBy(): string {
    const ids: string[] = [];
    if (this.hasError) ids.push(this.errorId);
    if (this.hasHelper) ids.push(this.helperId);
    return ids.join(' ') || '';
  }
  get inputClasses(): string {
    const classes = ['ava-textarea__input'];
    if (this.size) classes.push(`ava-textarea__input--${this.size}`);
    if (this.variant) classes.push(`ava-textarea__input--${this.variant}`);
    if (this.hasError) classes.push('ava-textarea__input--error');
    if (this.disabled) classes.push('ava-textarea__input--disabled');
    if (this.readonly) classes.push('ava-textarea__input--readonly');
    if (this.isFocused) classes.push('ava-textarea__input--focused');
    if (this.fullWidth) classes.push('ava-textarea__input--full-width');
    return classes.join(' ');
  }
  get wrapperClasses(): string {
    const classes = ['ava-textarea'];
    if (this.size) classes.push(`ava-textarea--${this.size}`);
    if (this.variant) classes.push(`ava-textarea--${this.variant}`);
    if (this.hasError) classes.push('ava-textarea--error');
    if (this.disabled) classes.push('ava-textarea--disabled');
    if (this.readonly) classes.push('ava-textarea--readonly');
    if (this.isFocused) classes.push('ava-textarea--focused');
    if (this.fullWidth) classes.push('ava-textarea--full-width');
    if (this.autoResize) classes.push('ava-textarea--auto-resize');
    if (this.processing) classes.push('ava-textarea--processing');
    if (this.processingGradientBorder) {
      classes.push('ava-textarea--processing-gradient-border');
    }
    return classes.join(' ');
  }
  get computedStyles(): Record<string, string> {
    const styles: Record<string, string> = { ...this.style };
    if (
      this.processingGradientColors &&
      this.processingGradientColors.length > 0
    ) {
      styles['--processing-gradient-colors'] =
        this.processingGradientColors.join(', ');
    }
    return styles;
  }
}
