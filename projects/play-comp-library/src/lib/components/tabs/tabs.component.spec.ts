import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';

import { TabsComponent, TabItem } from './tabs.component';
import { IconComponent } from '../icon/icon.component';

describe('TabsComponent', () => {
  let component: TabsComponent;
  let fixture: ComponentFixture<TabsComponent>;

  const mockTabs: TabItem[] = [
    { id: 'tab1', label: 'Tab 1', content: 'Content 1' },
    { id: 'tab2', label: 'Tab 2', content: 'Content 2', iconName: 'home' },
    { id: 'tab3', label: 'Tab 3', content: 'Content 3', disabled: true },
    { id: 'tab4', label: 'Tab 4', content: 'Content 4', subtitle: 'Subtitle' },
    {
      id: 'tab5',
      label: 'Tab 5',
      content: 'Content 5',
      badge: '5',
      closeable: true,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TabsComponent, IconComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TabsComponent);
    component = fixture.componentInstance;
    component.tabs = mockTabs;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default properties', () => {
      expect(component.variant).toBe('default');
      expect(component.size).toBe('md');
      expect(component.disabled).toBeFalse();
      expect(component.scrollable).toBeFalse();
      expect(component.showDropdown).toBeFalse();
      expect(component.maxVisibleTabs).toBe(5);
      expect(component.allowTabClose).toBeFalse();
    });

    it('should set active tab to first non-disabled tab when no activeTabId is provided', () => {
      component.activeTabId = '';
      component.ngOnInit();
      expect(component.activeTab?.id).toBe('tab1');
    });

    it('should set active tab based on activeTabId', () => {
      component.activeTabId = 'tab2';
      component.ngOnInit();
      expect(component.activeTab?.id).toBe('tab2');
    });

    it('should handle empty tabs array', () => {
      component.tabs = [];
      component.ngOnInit();
      expect(component.activeTab).toBeNull();
    });
  });

  describe('Tab Rendering', () => {
    it('should render all visible tabs', () => {
      const tabButtons = fixture.debugElement.queryAll(
        By.css('.ava-tabs__tab')
      );
      expect(tabButtons.length).toBe(mockTabs.length);
    });

    it('should display tab labels correctly', () => {
      const tabButtons = fixture.debugElement.queryAll(
        By.css('.ava-tabs__tab')
      );
      expect(tabButtons[0].nativeElement.textContent).toContain('Tab 1');
      expect(tabButtons[1].nativeElement.textContent).toContain('Tab 2');
    });

    it('should render icons when provided', () => {
      const iconElements = fixture.debugElement.queryAll(
        By.css('.ava-tabs__tab-icon')
      );
      expect(iconElements.length).toBeGreaterThan(0);
    });

    it('should render subtitles when provided', () => {
      const subtitleElements = fixture.debugElement.queryAll(
        By.css('.ava-tabs__tab-subtitle')
      );
      expect(subtitleElements.length).toBeGreaterThan(0);
      expect(subtitleElements[0].nativeElement.textContent).toContain(
        'Subtitle'
      );
    });

    it('should render badges when provided', () => {
      const badgeElements = fixture.debugElement.queryAll(
        By.css('.ava-tabs__tab-badge')
      );
      expect(badgeElements.length).toBeGreaterThan(0);
      expect(badgeElements[0].nativeElement.textContent).toContain('5');
    });

    it('should render close buttons for closeable tabs when allowTabClose is true', () => {
      component.allowTabClose = true;
      fixture.detectChanges();

      const closeButtons = fixture.debugElement.queryAll(
        By.css('.ava-tabs__tab-close')
      );
      expect(closeButtons.length).toBeGreaterThan(0);
    });
  });

  describe('Tab States', () => {
    it('should apply active class to active tab', () => {
      component.activeTabId = 'tab1';
      component.ngOnInit();
      fixture.detectChanges();

      const activeTab = fixture.debugElement.query(
        By.css('.ava-tabs__tab--active')
      );
      expect(activeTab).toBeTruthy();
    });

    it('should apply disabled class to disabled tabs', () => {
      const disabledTab = fixture.debugElement.query(
        By.css('.ava-tabs__tab--disabled')
      );
      expect(disabledTab).toBeTruthy();
    });

    it('should show active tab indicator for default variant', () => {
      component.variant = 'default';
      component.activeTabId = 'tab1';
      component.ngOnInit();
      fixture.detectChanges();

      const indicator = fixture.debugElement.query(
        By.css('.ava-tabs__tab-indicator')
      );
      expect(indicator).toBeTruthy();
    });
  });

  describe('Tab Interaction', () => {
    it('should emit tabChange when tab is clicked', () => {
      spyOn(component.tabChange, 'emit');

      const tabButton = fixture.debugElement.query(By.css('.ava-tabs__tab'));
      tabButton.nativeElement.click();

      expect(component.tabChange.emit).toHaveBeenCalledWith(mockTabs[0]);
    });

    it('should not emit tabChange when disabled tab is clicked', () => {
      spyOn(component.tabChange, 'emit');

      const disabledTab = fixture.debugElement.query(
        By.css('.ava-tabs__tab--disabled')
      );
      disabledTab.nativeElement.click();

      expect(component.tabChange.emit).not.toHaveBeenCalled();
    });

    it('should emit tabClose when close button is clicked', () => {
      component.allowTabClose = true;
      fixture.detectChanges();

      spyOn(component.tabClose, 'emit');

      const closeButton = fixture.debugElement.query(
        By.css('.ava-tabs__tab-close')
      );
      closeButton.nativeElement.click();

      expect(component.tabClose.emit).toHaveBeenCalled();
    });

    it('should handle keyboard navigation', () => {
      spyOn(component, 'onTabClick');

      const tabButton = fixture.debugElement.query(By.css('.ava-tabs__tab'));
      const event = new KeyboardEvent('keydown', { key: 'Enter' });

      tabButton.triggerEventHandler('keydown', event);

      expect(component.onTabClick).toHaveBeenCalledWith(mockTabs[0]);
    });
  });

  describe('Variants', () => {
    it('should apply button variant styles', () => {
      component.variant = 'button';
      fixture.detectChanges();

      const hostElement = fixture.debugElement.nativeElement;
      expect(hostElement.getAttribute('data-variant')).toBe('button');
    });

    it('should apply icon variant styles', () => {
      component.variant = 'icon';
      fixture.detectChanges();

      const hostElement = fixture.debugElement.nativeElement;
      expect(hostElement.getAttribute('data-variant')).toBe('icon');
    });

    it('should hide text content in icon variant', () => {
      component.variant = 'icon';
      fixture.detectChanges();

      const hostElement = fixture.debugElement.nativeElement;
      expect(hostElement.getAttribute('data-variant')).toBe('icon');
    });
  });

  describe('Size Variants', () => {
    it('should apply small size', () => {
      component.size = 'sm';
      fixture.detectChanges();

      const hostElement = fixture.debugElement.nativeElement;
      expect(hostElement.getAttribute('data-size')).toBe('sm');
    });

    it('should apply large size', () => {
      component.size = 'lg';
      fixture.detectChanges();

      const hostElement = fixture.debugElement.nativeElement;
      expect(hostElement.getAttribute('data-size')).toBe('lg');
    });

    it('should return correct icon size based on tab size', () => {
      component.size = 'sm';
      expect(component.getIconSize()).toBe(16);

      component.size = 'md';
      expect(component.getIconSize()).toBe(20);

      component.size = 'lg';
      expect(component.getIconSize()).toBe(24);
    });
  });

  describe('Scrollable Functionality', () => {
    beforeEach(() => {
      component.scrollable = true;
      fixture.detectChanges();
    });

    it('should show scroll buttons when scrollable is enabled', () => {
      component.canScrollLeft = true;
      component.canScrollRight = true;
      fixture.detectChanges();

      const leftButton = fixture.debugElement.query(
        By.css('.ava-tabs__scroll-btn--left')
      );
      const rightButton = fixture.debugElement.query(
        By.css('.ava-tabs__scroll-btn--right')
      );

      expect(leftButton).toBeTruthy();
      expect(rightButton).toBeTruthy();
    });

    it('should call scrollLeft when left scroll button is clicked', () => {
      spyOn(component, 'scrollLeft');
      component.canScrollLeft = true;
      fixture.detectChanges();

      const leftButton = fixture.debugElement.query(
        By.css('.ava-tabs__scroll-btn--left')
      );
      leftButton.nativeElement.click();

      expect(component.scrollLeft).toHaveBeenCalled();
    });

    it('should call scrollRight when right scroll button is clicked', () => {
      spyOn(component, 'scrollRight');
      component.canScrollRight = true;
      fixture.detectChanges();

      const rightButton = fixture.debugElement.query(
        By.css('.ava-tabs__scroll-btn--right')
      );
      rightButton.nativeElement.click();

      expect(component.scrollRight).toHaveBeenCalled();
    });
  });

  describe('Dropdown Functionality', () => {
    beforeEach(() => {
      component.showDropdown = true;
      component.maxVisibleTabs = 3;
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should show dropdown trigger when there are hidden tabs', () => {
      const dropdownTrigger = fixture.debugElement.query(
        By.css('.ava-tabs__dropdown-trigger')
      );
      expect(dropdownTrigger).toBeTruthy();
    });

    it('should toggle dropdown when trigger is clicked', () => {
      spyOn(component, 'toggleDropdown');

      const dropdownTrigger = fixture.debugElement.query(
        By.css('.ava-tabs__dropdown-trigger')
      );
      dropdownTrigger.nativeElement.click();

      expect(component.toggleDropdown).toHaveBeenCalled();
    });

    it('should show dropdown menu when open', () => {
      component.isDropdownOpen = true;
      fixture.detectChanges();

      const dropdownMenu = fixture.debugElement.query(
        By.css('.ava-tabs__dropdown')
      );
      expect(dropdownMenu).toBeTruthy();
    });

    it('should emit dropdownToggle when dropdown is toggled', () => {
      spyOn(component.dropdownToggle, 'emit');

      component.toggleDropdown();

      expect(component.dropdownToggle.emit).toHaveBeenCalledWith(true);
    });
  });

  describe('Content Panels', () => {
    it('should render tab content panels', () => {
      const panels = fixture.debugElement.queryAll(By.css('.ava-tabs__panel'));
      expect(panels.length).toBe(mockTabs.length);
    });

    it('should show active panel content', () => {
      component.activeTabId = 'tab1';
      component.ngOnInit();
      fixture.detectChanges();

      const activePanel = fixture.debugElement.query(
        By.css('.ava-tabs__panel--active')
      );
      expect(activePanel).toBeTruthy();
    });

    it('should return correct tab content', () => {
      component.activeTab = mockTabs[0];
      expect(component.getActiveTabContent()).toBe('Content 1');
    });
  });

  describe('Utility Methods', () => {
    it('should correctly identify active tab', () => {
      component.activeTab = mockTabs[0];
      expect(component.isTabActive(mockTabs[0])).toBeTruthy();
      expect(component.isTabActive(mockTabs[1])).toBeFalsy();
    });

    it('should generate correct tab IDs', () => {
      expect(component.getTabId(mockTabs[0])).toBe('tab-tab1');
      expect(component.getPanelId(mockTabs[0])).toBe('panel-tab1');
    });

    it('should track tabs by ID', () => {
      expect(component.trackByTabId(0, mockTabs[0])).toBe('tab1');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      const tabList = fixture.debugElement.query(By.css('.ava-tabs__list'));
      const firstTab = fixture.debugElement.query(By.css('.ava-tabs__tab'));
      const firstPanel = fixture.debugElement.query(By.css('.ava-tabs__panel'));

      expect(tabList.nativeElement.getAttribute('role')).toBe('tablist');
      expect(firstTab.nativeElement.getAttribute('role')).toBe('tab');
      expect(firstPanel.nativeElement.getAttribute('role')).toBe('tabpanel');
    });

    it('should have correct tabindex for active and inactive tabs', () => {
      component.activeTabId = 'tab1';
      component.ngOnInit();
      fixture.detectChanges();

      const tabs = fixture.debugElement.queryAll(By.css('.ava-tabs__tab'));
      expect(tabs[0].nativeElement.getAttribute('tabindex')).toBe('0');
      expect(tabs[1].nativeElement.getAttribute('tabindex')).toBe('-1');
    });

    it('should have aria-selected attribute', () => {
      component.activeTabId = 'tab1';
      component.ngOnInit();
      fixture.detectChanges();

      const tabs = fixture.debugElement.queryAll(By.css('.ava-tabs__tab'));
      expect(tabs[0].nativeElement.getAttribute('aria-selected')).toBe('true');
      expect(tabs[1].nativeElement.getAttribute('aria-selected')).toBe('false');
    });
  });

  describe('Component State Changes', () => {
    it('should update when tabs input changes', () => {
      const newTabs: TabItem[] = [
        { id: 'new1', label: 'New Tab 1', content: 'New Content 1' },
      ];

      component.tabs = newTabs;
      component.ngOnChanges({
        tabs: {
          currentValue: newTabs,
          previousValue: mockTabs,
          firstChange: false,
          isFirstChange: () => false,
        },
      });

      expect(component.visibleTabs.length).toBe(1);
    });

    it('should update when activeTabId changes', () => {
      component.activeTabId = 'tab2';
      component.ngOnChanges({
        activeTabId: {
          currentValue: 'tab2',
          previousValue: 'tab1',
          firstChange: false,
          isFirstChange: () => false,
        },
      });

      expect(component.activeTab?.id).toBe('tab2');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null tabs gracefully', () => {
      component.tabs = [] as TabItem[];
      expect(() => component.ngOnInit()).not.toThrow();
    });

    it('should handle invalid activeTabId', () => {
      component.activeTabId = 'nonexistent';
      component.ngOnInit();
      expect(component.activeTab?.id).toBe('tab1');
    });

    it('should handle all tabs being disabled', () => {
      const disabledTabs: TabItem[] = [
        { id: 'tab1', label: 'Tab 1', content: 'Content 1', disabled: true },
        { id: 'tab2', label: 'Tab 2', content: 'Content 2', disabled: true },
      ];

      component.tabs = disabledTabs;
      component.ngOnInit();
      expect(component.activeTab?.id).toBe('tab1');
    });
  });
});
