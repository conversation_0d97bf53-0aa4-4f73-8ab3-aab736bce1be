import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  Output,
  EventEmitter,
  ViewEncapsulation,
  OnInit,
  OnChanges,
  SimpleChanges,
  ElementRef,
  ViewChild,
  AfterViewInit,
  ViewChildren,
  QueryList,
} from '@angular/core';
import { IconComponent } from '../icon/icon.component';

export interface TabItem {
  id: string;
  label: string;
  content?: string;
  iconName?: string;
  subtitle?: string;
  disabled?: boolean;
  badge?: string | number;
  closeable?: boolean;
}

export type TabVariant = 'default' | 'button' | 'icon' | 'iconOnlySquare' | 'iconOnlyCircle';
export type TabSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

@Component({
  selector: 'ava-tabs',
  imports: [CommonModule, IconComponent],
  templateUrl: './tabs.component.html',
  styleUrl: './tabs.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    class: 'ava-tabs',
    '[attr.data-variant]': 'variant',
    '[attr.data-size]': 'size',
    '[class.ava-tabs--disabled]': 'disabled',
    '[class.ava-tabs--scrollable]': 'scrollable',
    '[class.ava-tabs--vertical]': "orientation === 'vertical'",
  },
})
export class TabsComponent implements OnInit, OnChanges, AfterViewInit {

  @ViewChild('tabsList', { static: false }) tabsList!: ElementRef<HTMLElement>;
  @ViewChild('scrollContainer', { static: false })
  scrollContainer!: ElementRef<HTMLElement>;
  @ViewChildren('tabButton') tabButtons!: QueryList<
    ElementRef<HTMLButtonElement>
  >;

  @Input() tabs: TabItem[] = [];
  @Input() activeTabId = '';
  @Input() variant: TabVariant = 'default';
  /**
   * If true, adds a border around icon-only tabs (for iconOnlySquare and iconOnlyCircle variants).
   */
  @Input() bordered = false;
  @Input() size: TabSize = 'md'; // Now supports 'xs', 'sm', 'md', 'lg', 'xl'
  @Input() disabled = false;
  @Input() scrollable = false;
  @Input() showDropdown = false;
  @Input() maxVisibleTabs = 5;
  @Input() allowTabClose = false;
  @Input() centeredTabs = false;
  @Input() fullWidth = false;
  @Input() animateTransitions = true;
  @Input() lazyLoadContent = false;
  @Input() persistActiveTab = true;
  @Input() ariaLabel = 'Tabs navigation';
  @Input() iconPosition: 'start' | 'end' = 'start';

  /**
   * Custom styles for the active tab in the button variant.
   * Example: { background: '#fff', color: '#e91e63', borderColor: '#e91e63' }
   */
  @Input() activeButtonTabStyles: Record<string, string> = {};
  /**
   * Shape for button variant: 'rounded' (default) or 'pill'.
   */
  @Input() buttonShape: 'rounded' | 'pill' = 'rounded';
  /**
   * Whether to show content panels below the tabs. Set to false for navigation-only tabs.
   * @default true
   */
  @Input() showContentPanels = true;
  /**
   * Custom styles for the tab row wrapper (the row containing the tabs/buttons/icons and scroll buttons).
   * Example: { borderRadius: '9999px', background: '#f5f5f5', border: '1px solid #eee' }
   */
  @Input() tabRowWrapperStyles: Record<string, string> = {};
  /**
   * Custom styles for the tab row background (the area directly behind the tabs/buttons/icons).
   * Example: { background: '#f5f5f5', borderRadius: '9999px' }
   */
  @Input() tabRowBackgroundStyles: Record<string, string> = {};
  /**
   * Orientation of the tabs: 'horizontal' (default) or 'vertical'.
   */
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';

  @Output() tabChange = new EventEmitter<TabItem>();
  @Output() tabClose = new EventEmitter<TabItem>();
  @Output() tabsReorder = new EventEmitter<TabItem[]>();
  @Output() dropdownToggle = new EventEmitter<boolean>();

  // Internal state
  activeTab: TabItem | null = null;
  visibleTabs: TabItem[] = [];
  hiddenTabs: TabItem[] = [];
  isDropdownOpen = false;
  canScrollLeft = false;
  canScrollRight = false;
  scrollOffset = 0;
  liquidAnimatingTabId: string | null = null;
  liquidAnimationDirection: 'ltr' | 'rtl' = 'ltr';
  liquidExitingTabId: string | null = null;
  liquidExitDirection: 'ltr' | 'rtl' = 'ltr';
  private lastActiveTabIndex = 0;

  // Underline animation properties
  underlineStyles = {
    left: '0px',
    width: '0px',
    opacity: 0,
  };

  private previousUnderlinePosition: { left: number; width: number } | null =
    null;
  private isUnderlineAnimating = false;

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.initializeTabs();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['tabs'] || changes['activeTabId']) {
      this.initializeTabs();
    }
    if (changes['scrollable'] || changes['maxVisibleTabs']) {
      this.updateVisibleTabs();
    }
  }

  ngAfterViewInit(): void {
    if (this.scrollable) {
      this.updateScrollButtons();
    }
    // Initialize underline position after view is ready
    setTimeout(() => this.updateUnderlinePosition(), 0);
  }

  private initializeTabs(): void {
    if (!this.tabs || this.tabs.length === 0) {
      this.activeTab = null;
      return;
    }

    // Set active tab
    if (this.activeTabId) {
      this.activeTab =
        this.tabs.find((tab) => tab.id === this.activeTabId) || null;
    }

    // If no active tab set, use first non-disabled tab
    if (!this.activeTab) {
      this.activeTab = this.tabs.find((tab) => !tab.disabled) || this.tabs[0];
      // Update activeTabId to match the selected tab
      if (this.activeTab) {
        this.activeTabId = this.activeTab.id;
      }
    }

    this.updateVisibleTabs();

    // Force change detection to ensure proper rendering
    this.cdr.markForCheck();
  }

  private updateVisibleTabs(): void {
    if (!this.scrollable || !this.showDropdown) {
      this.visibleTabs = this.tabs;
      this.hiddenTabs = [];
      return;
    }

    if (this.tabs.length > this.maxVisibleTabs) {
      this.visibleTabs = this.tabs.slice(0, this.maxVisibleTabs - 1);
      this.hiddenTabs = this.tabs.slice(this.maxVisibleTabs - 1);
    } else {
      this.visibleTabs = this.tabs;
      this.hiddenTabs = [];
    }
  }

  private updateUnderlinePosition(): void {
    if (
      this.variant !== 'default' ||
      this.orientation === 'vertical' ||
      !this.activeTab ||
      !this.tabButtons
    ) {
      this.underlineStyles.opacity = 0;
      return;
    }

    const activeTabIndex = this.visibleTabs.findIndex(
      (tab) => tab.id === this.activeTab?.id
    );
    const activeTabButton = this.tabButtons.toArray()[activeTabIndex];

    if (activeTabButton) {
      const buttonElement = activeTabButton.nativeElement;

      if (buttonElement) {
        const newLeft = buttonElement.offsetLeft;
        const newWidth = buttonElement.offsetWidth;

        // If we have a previous position, animate the flow
        if (this.previousUnderlinePosition && !this.isUnderlineAnimating) {
          this.animateUnderlineFlow(this.previousUnderlinePosition, {
            left: newLeft,
            width: newWidth,
          });
        } else {
          // First time or immediate update
          this.underlineStyles = {
            left: `${newLeft}px`,
            width: `${newWidth}px`,
            opacity: 1,
          };
        }

        // Store current position for next animation
        this.previousUnderlinePosition = { left: newLeft, width: newWidth };
        this.cdr.markForCheck();
      }
    }
  }

  private animateUnderlineFlow(
    from: { left: number; width: number },
    to: { left: number; width: number }
  ): void {
    this.isUnderlineAnimating = true;

    // Calculate the direction and create a flowing effect
    const direction = to.left > from.left ? 'ltr' : 'rtl';
    const totalDistance = Math.abs(to.left - from.left);
    const maxWidth = Math.max(from.width, to.width);

    // Phase 1: Stretch the underline to cover both positions
    const stretchWidth = totalDistance + maxWidth;
    const stretchLeft = direction === 'ltr' ? from.left : to.left;

    this.underlineStyles = {
      left: `${stretchLeft}px`,
      width: `${stretchWidth}px`,
      opacity: 1,
    };
    this.cdr.markForCheck();

    // Phase 2: Contract to the final position
    setTimeout(() => {
      this.underlineStyles = {
        left: `${to.left}px`,
        width: `${to.width}px`,
        opacity: 1,
      };
      this.cdr.markForCheck();

      // Reset animation flag
      setTimeout(() => {
        this.isUnderlineAnimating = false;
      }, 400);
    }, 250);
  }

  onTabClick(tab: TabItem): void {
    if (tab.disabled || this.disabled) return;

    this.activeTab = tab;
    this.activeTabId = tab.id;
    this.tabChange.emit(tab);

    if (this.isDropdownOpen) {
      this.toggleDropdown();
    }

    // Update underline position for default variant
    if (this.variant === 'default') {
      setTimeout(() => this.updateUnderlinePosition(), 0);
    }
  }

  onTabClickWithAnimation(tab: TabItem): void {
    if (tab.disabled || this.disabled || this.isTabActive(tab)) return;
    if (this.variant === 'button' && this.hasCustomActiveButtonTabStyles) {
      this.onTabClick(tab);
      return;
    }
    const currentIndex = this.visibleTabs.findIndex(
      (t) => t.id === this.activeTabId
    );
    const nextIndex = this.visibleTabs.findIndex((t) => t.id === tab.id);

    // Determine direction based on orientation
    let direction: 'ltr' | 'rtl';
    if (this.orientation === 'vertical') {
      // For vertical: top-to-bottom = 'ltr', bottom-to-top = 'rtl'
      direction = nextIndex > currentIndex ? 'ltr' : 'rtl';
    } else {
      // For horizontal: left-to-right = 'ltr', right-to-left = 'rtl'
      direction = nextIndex > currentIndex ? 'ltr' : 'rtl';
    }

    // Set up animations
    this.liquidAnimationDirection = direction;
    this.liquidAnimatingTabId = tab.id;
    this.liquidExitingTabId = this.activeTabId;
    this.liquidExitDirection = direction;

    // Force change detection to apply classes
    this.cdr.markForCheck();

    // Switch active tab and clear animations after animation completes
    setTimeout(() => {
      this.onTabClick(tab);
      this.liquidAnimatingTabId = null;
      this.liquidExitingTabId = null;
      this.lastActiveTabIndex = nextIndex;
      this.cdr.markForCheck();
    }, 500); // Match the CSS transition duration
  }

  onTabClose(tab: TabItem, event: Event): void {
    event.stopPropagation();
    if (tab.disabled || this.disabled) return;

    this.tabClose.emit(tab);
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    this.dropdownToggle.emit(this.isDropdownOpen);
  }

  scrollLeft(): void {
    if (!this.scrollContainer) return;

    const container = this.scrollContainer.nativeElement;
    const scrollAmount = 200;
    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });

    setTimeout(() => this.updateScrollButtons(), 150);
  }

  scrollRight(): void {
    if (!this.scrollContainer) return;

    const container = this.scrollContainer.nativeElement;
    const scrollAmount = 200;
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });

    setTimeout(() => this.updateScrollButtons(), 150);
  }

  private updateScrollButtons(): void {
    if (!this.scrollContainer) return;

    const container = this.scrollContainer.nativeElement;
    this.canScrollLeft = container.scrollLeft > 0;
    this.canScrollRight =
      container.scrollLeft < container.scrollWidth - container.clientWidth;
  }

  onScroll(): void {
    this.updateScrollButtons();
  }

  // Keyboard navigation
  onKeyDown(event: KeyboardEvent, tab: TabItem): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onTabClick(tab);
    }
  }

  // Utility methods
  isTabActive(tab: TabItem): boolean {
    return this.activeTab?.id === tab.id;
  }

  isTabInDropdown(tab: TabItem): boolean {
    return this.hiddenTabs.includes(tab);
  }

  getActiveTabContent(): string {
    return this.activeTab?.content || '';
  }

  // Icon size mapping based on tab size
  getIconSize(): number {
    // For iconOnlySquare and iconOnlyCircle, use larger icons for better visuals
    if (this.variant === 'iconOnlySquare' || this.variant === 'iconOnlyCircle') {
      switch (this.size) {
        case 'xs': return 20;
        case 'sm': return 24;
        case 'md': return 28;
        case 'lg': return 32;
        case 'xl': return 36;
        default: return 28;
      }
    }
    // Default icon size for other variants
    switch (this.size) {
      case 'xs': return 12;
      case 'sm': return 16;
      case 'md': return 20;
      case 'lg': return 24;
      case 'xl': return 28;
      default: return 20;
    }
  }

  // Generate unique ID for accessibility
  getTabId(tab: TabItem): string {
    return `tab-${tab.id}`;
  }

  getPanelId(tab: TabItem): string {
    return `panel-${tab.id}`;
  }

  // TrackBy function for ngFor optimization
  trackByTabId(index: number, tab: TabItem): string {
    return tab.id;
  }

  // Generate selector for content projection
  getTabContentSelector(tab: TabItem): string {
    return `[data-tab-id="${tab.id}"]`;
  }

  hasTabRowBackgroundStyles(): boolean {
    return (
      !!this.tabRowBackgroundStyles &&
      Object.keys(this.tabRowBackgroundStyles).length > 0
    );
  }

  get hasCustomActiveButtonTabStyles(): boolean {
    return (
      this.activeButtonTabStyles &&
      Object.keys(this.activeButtonTabStyles).length > 0
    );
  }
}
