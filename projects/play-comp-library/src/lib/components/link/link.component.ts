import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, output, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'ava-link',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './link.component.html',
  styleUrls: ['./link.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class LinkComponent {
  @Input() label = 'Action Link';
  @Input() color: 'success' | 'warning' | 'danger' | 'info' | 'default' | 'primary' | string = 'default';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() underline: boolean = false;
  @Input() href: string = '';
  @Input() addIcon: boolean = false;
  @Input() arrowDirection: 'right' | 'left' = 'left';
  @Output() userClick = new EventEmitter<Event>();

  separatorIcon: string = 'chevron-right';
  separatorSize: number = 18;

  constructor(private sanitizer: DomSanitizer) { }

  ngOnChanges() {
    this.separatorSize = { small: 16, medium: 18, large: 20 }[this.size];
  }

  isHexColor(color: string): boolean {
    return /^#([0-9A-F]{3}){1,2}$/i.test(color);
  }
  anchorClick(event: Event) {
    this.userClick.emit(event);
  }
  get safeHref(): SafeUrl {
    return this.sanitizer.bypassSecurityTrustUrl(this.href || 'javascript:void(0)');
  }
}

