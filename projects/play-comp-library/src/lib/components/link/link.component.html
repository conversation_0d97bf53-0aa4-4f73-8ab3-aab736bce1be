<a class="action-link" [ngClass]="[color, size, underline ? 'underline' : '']"
    [ngStyle]="isHexColor(color) ? {'color': color} : {}" [attr.href]="safeHref" (click)="anchorClick($event)"
    (keydown.enter)="anchorClick($event)" (keydown.space)="anchorClick($event)">
    <span class="link-content">
        @if(arrowDirection === 'right'){
            {{ label }}
        }
        @if(addIcon && !underline){
            <ava-icon [iconName]="separatorIcon" [iconSize]="separatorSize" [cursor]="false" class="action-link-separator">
            </ava-icon>
        }
        @if(arrowDirection === 'left'){
            {{ label }}
        }
    </span>
</a>