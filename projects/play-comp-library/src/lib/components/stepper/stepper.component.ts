import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
} from '@angular/core';
import { IconComponent } from '../icon/icon.component';

export interface StepperStep {
  label: string;
  iconName?: string;
}

@Component({
  selector: 'ava-stepper',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './stepper.component.html',
  styleUrls: ['./stepper.component.scss'],
})
export class AvaStepperComponent implements OnInit, OnChanges {
  @Input() steps: (string | StepperStep)[] = [];
  @Input() currentStep = 0;
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() showNavigation = true;
  @Input() interactive = true;
  @Input() size: 'xsmall'|'small' | 'medium' | 'large' = 'medium';
  @Input() disabledSteps: number[] = [];
  @Input() iconColor = '#fff';
  @Input() iconSize = '20';
  @Input() stepVariant: 'default' | 'icon' = 'default';

  isAnimating = false;
  previousStep = 0;
  visualCurrentStep = 0;
  animationDirection: 'forward' | 'backward' | null = null;
  targetStep = 0;
  currentAnimatingStep = 0;

  @Output() stepChange = new EventEmitter<number>();
  @Output() stepperComplete = new EventEmitter<void>();

  ngOnInit() {
    this.previousStep = this.currentStep;
    this.visualCurrentStep = this.currentStep;
    this.targetStep = this.currentStep;
    this.currentAnimatingStep = this.currentStep;
  }

  ngOnChanges() {
    // Trigger sequential animation for both forward and backward navigation
    if (this.currentStep !== this.previousStep) {
      this.targetStep = this.currentStep;
      this.animationDirection = this.currentStep > this.previousStep ? 'forward' : 'backward';
      this.triggerSequentialAnimation();
    }
    this.previousStep = this.currentStep;
  }

  isDisabled(i: number): boolean {
    return this.disabledSteps.includes(i);
  }

  goToStep(index: number): void {
    if (this.interactive && !this.isDisabled(index)) {
      this.stepChange.emit(index);
    }
  }

  getStepLabel(step: string | StepperStep): string {
    return typeof step === 'string' ? step : step.label;
  }

  getStepIcon(step: string | StepperStep): string | undefined {
    return typeof step === 'string' ? undefined : step.iconName;
  }

  private triggerSequentialAnimation(): void {
    if (this.isAnimating) {
      return; // Prevent overlapping animations
    }

    this.isAnimating = true;
    this.currentAnimatingStep = this.visualCurrentStep;

    this.animateNextStep();
  }

  private animateNextStep(): void {
    // Check if we've reached the target
    if (this.currentAnimatingStep === this.targetStep) {
      this.isAnimating = false;
      this.animationDirection = null;
      return;
    }

    // Determine the next step to animate
    const nextStep = this.animationDirection === 'forward'
      ? this.currentAnimatingStep + 1
      : this.currentAnimatingStep - 1;

    // Update the animating step
    this.currentAnimatingStep = nextStep;

    setTimeout(() => {
      this.visualCurrentStep = nextStep;
      setTimeout(() => {
        this.animateNextStep();
      }, 100); 
    }, 800); 
  }
}
