<div class="ava-stepper" [ngClass]="[orientation, size]">
  <div class="step-wrapper" *ngFor="let step of steps; let i = index">
    <div class="step-circle-wrapper">
      <div
        class="step-circle"
        [ngClass]="{
          active: i === visualCurrentStep,
          completed: i < visualCurrentStep,
          incomplete: i > visualCurrentStep,
          'animating-forward': isAnimating && animationDirection === 'forward' && i === currentAnimatingStep,
          'animating-backward': isAnimating && animationDirection === 'backward' && i === currentAnimatingStep
        }"
        (click)="goToStep(i)"
        tabindex="0"
        (keydown.enter)="goToStep(i)"
        (keydown.space)="goToStep(i)"
        role="button"
        [attr.aria-label]="'Go to step ' + (i + 1)"
      >
        <!-- Icon variant: always show icon if provided (no checkmark overlay) -->
        <ng-container *ngIf="stepVariant === 'icon' && getStepIcon(step)">
          <ava-icon
            [iconName]="getStepIcon(step) || ''"
            [iconColor]="i <= visualCurrentStep ? iconColor : '#d1d5db'"
            [iconSize]="iconSize"
          ></ava-icon>
        </ng-container>
        <!-- Completed: checkmark for default variant only -->
        <ava-icon
          *ngIf="
            stepVariant !== 'icon' &&
            (i < visualCurrentStep ||
              (i === visualCurrentStep &&
                visualCurrentStep === steps.length - 1))
          "
          iconName="circle-check"
          [iconColor]="iconColor"
          [iconSize]="iconSize"
        ></ava-icon>
        <!-- Step number if not completed and not on last step, or fallback for icon variant -->
        <span
          *ngIf="
            (stepVariant !== 'icon' &&
              i >= visualCurrentStep &&
              !(
                i === visualCurrentStep &&
                visualCurrentStep === steps.length - 1
              )) ||
            (stepVariant === 'icon' && !getStepIcon(step))
          "
          >{{ i + 1 }}</span
        >
      </div>
      <!-- Connector line with progress animation -->
      <div *ngIf="i < steps.length - 1" class="step-line-wrapper">
        <div
          class="step-line"
          [ngClass]="{
            completed: i < visualCurrentStep - 1,
            'animating-forward': isAnimating && animationDirection === 'forward' && i === currentAnimatingStep - 1,
            'animating-backward': isAnimating && animationDirection === 'backward' && i === currentAnimatingStep - 1,
            incomplete: i >= visualCurrentStep
          }"
        ></div>
        <!-- Animated progress dot for forward animation -->
        <div
          *ngIf="isAnimating && animationDirection === 'forward' && i === currentAnimatingStep - 1"
          class="progress-dot forward"
        ></div>
        <!-- Animated progress dot for backward animation -->
        <div
          *ngIf="isAnimating && animationDirection === 'backward' && i === currentAnimatingStep - 1"
          class="progress-dot backward"
        ></div>
      </div>
    </div>
    <div
      class="step-label"
      [ngClass]="{
        active: i === visualCurrentStep,
        completed: i < visualCurrentStep
      }"
    >
      {{ getStepLabel(step) }}
    </div>
  </div>
</div>
