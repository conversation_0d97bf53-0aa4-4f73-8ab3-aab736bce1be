<div #tooltipW class="tooltip-wrapper" [class.show]="visible"
  [ngClass]="[config.type, config.arrow, config.size, config.position, config.variant]" [attr.data-arrow]="config.arrow"
  [attr.data-position]="config.position">
  <div #tooltip class="tooltip stretch">
    <div>
      <!-- Title and Description Content -->
      <div *ngIf="config?.title && config.title.trim() !== ''" class="tooltip-title">
        <ava-icon
          *ngIf="config?.icon"
          [iconName]="config.icon"
          [iconColor]="config?.iconColor || '#000'"
          [iconSize]="12"
          class="tooltip-icon">
        </ava-icon>
        <span class="tooltip-title-text">{{ config.title }}</span>
      </div>
      <div *ngIf="config?.description && config.description.trim() !== ''" class="tooltip-description"
           [class.has-title-with-icon]="config?.title && config.title.trim() !== '' && config?.icon"
           [class.has-icon-only]="(!config?.title || config.title.trim() === '') && config?.icon">
        <ava-icon
          *ngIf="config?.icon && (!config?.title || config.title.trim() === '')"
          [iconName]="config.icon"
          [iconColor]="config?.iconColor || '#000'"
          [iconSize]="12"
          class="tooltip-icon">
        </ava-icon>
        <span class="tooltip-description-text">{{ config.description }}</span>
      </div>
      <!-- Fallback for simple text content -->
      <div
        *ngIf="(!config?.title || config.title.trim() === '') && (!config?.description || config.description.trim() === '') && config?.content"
        class="tooltip-description"
        [class.has-icon-only]="config?.icon">
        <ava-icon
          *ngIf="config?.icon"
          [iconName]="config.icon"
          [iconColor]="config?.iconColor || '#000'"
          [iconSize]="12"
          class="tooltip-icon">
        </ava-icon>
        <span class="tooltip-description-text">{{ config.content }}</span>
      </div>
    </div>
    <!-- Simple Tooltip -->
    <!-- Card Tooltip -->
    <!-- <ng-container *ngIf="config.type === 'card'">
    <h4>{{ config.content.title }}</h4>
    <p>{{ config.content.description }}</p>
    <a [href]="config.content.linkHref">{{ config.content.linkText }}</a>
  </ng-container> -->
    <!-- Guided Tooltip -->
    <!-- <ng-container *ngIf="config.type === 'guided'">
    <h4>{{ config.content.title }}</h4>
    <p>{{ config.content.description }}</p>
    <div class="actions">
      <button *ngFor="let action of config.content.actions"
              [class.primary]="action.primary">
        {{ action.label }}
      </button>
    </div>
  </ng-container> -->
  </div>
</div> 