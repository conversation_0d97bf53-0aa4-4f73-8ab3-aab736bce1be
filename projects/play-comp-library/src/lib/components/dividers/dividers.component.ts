import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input} from '@angular/core';

@Component({
  selector: 'ava-dividers',
  standalone: true, // Mark as standalone
  imports: [CommonModule],
  templateUrl: './dividers.component.html',
  styleUrls: ['./dividers.component.scss'], // Corrected from styleUrl to styleUrls
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DividersComponent {
  @Input() variant: 'solid' | 'dashed' | 'dotted' | 'gradient' = 'solid';
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() color: string = '#000000';

  
}
