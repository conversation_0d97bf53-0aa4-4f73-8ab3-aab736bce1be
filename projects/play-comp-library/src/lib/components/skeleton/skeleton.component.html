<ng-container [ngSwitch]="skeletonType">
  <div
    *ngSwitchCase="'tableList'"
    class="shimmer-container"
    [style.width]="width"
    [style.height]="height"
    [ngClass]="[
      'shimmer-item',
      'shimmer-' + shape,
      'shimmer-animation-' + animation
    ]"
    [style.background-color]="backgroundColor"
  ></div>

  <div *ngSwitchCase="'table'">
    <div *ngIf="isLoading" class="table-skeleton">
      <div class="table-container">
        <div class="skeleton-header">
          <div
            class="skeleton-cell header-cell"
            *ngFor="let _ of colArray"
          ></div>
        </div>
        <div class="skeleton-row" *ngFor="let _ of rowArray">
          <div class="skeleton-cell body-cell" *ngFor="let _ of colArray"></div>
        </div>
      </div>
    </div>
  </div>
</ng-container>
