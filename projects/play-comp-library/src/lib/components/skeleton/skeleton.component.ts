import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

export type ShimmerShape = 'rectangle' | 'circle' | 'rounded' | 'square';
export type ShimmerAnimation = 'wave' | 'pulse';

@Component({
  selector: 'ava-skeleton',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './skeleton.component.html',
  styleUrls: ['./skeleton.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SkeletonComponent {
  @Input() width: string = '100%';
  @Input() height: string = '20px';
  @Input() shape: ShimmerShape = 'rectangle';
  @Input() animation: ShimmerAnimation = 'wave';
  @Input() backgroundColor: string = '#e0e0e0';
  @Input() skeletonType: 'tableList' | 'table' = 'tableList';
  @Input() rows: number = 5;
  @Input() columns: number = 5;
  @Input() isLoading: boolean = true;

  get rowArray() {
    return Array(this.rows);
  }

  get colArray() {
    return Array(this.columns);
  }
}
