.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
 
  .dots {
    padding: var(--pagination-dots-padding);
    color: var(--pagination-item-text);
  }
}


.pagination-container.page {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--pagination-container-gap);
}

.page-label {
  color: var(--pagination-page-label-color);
  font-family: var(--pagination-page-label-font-family);
  font-style: normal;
  font-weight: var(--pagination-page-label-font-weight);
  line-height: var(--pagination-page-label-line-height);
  display: flex;
  align-items: center;
  gap: var(--pagination-page-label-gap);

  .current-page-button {
    display: inline-flex;
  }
}

