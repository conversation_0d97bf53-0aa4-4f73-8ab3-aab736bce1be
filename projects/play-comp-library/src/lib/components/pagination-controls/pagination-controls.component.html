<!-- TYPE: basic -->
<ng-container *ngIf="type === 'basic'">
  <div class="pagination-container">
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-left"
      [iconPosition]="iconOnly ? 'only' : 'left'"
      [iconColor]="iconColor"
      variant="secondary"
      [label]="iconOnly ? '' : 'Previous'"
      (userClick)="prevPage()"
      [disabled]="currentPage === 1"
      [size]="size"
    >
    </ava-button>
    <div class="pagination">
    <ng-container *ngFor="let page of pages; trackBy: trackByPage">
      <ava-button
        [label]="page.toString()"
        *ngIf="page !== '...'; else dots"
        (userClick)="goToPage(page)"
        [variant]="page === currentPage ? 'primary' : 'secondary'"
        [outlined]="false"
        [pill]="true"
        [rounded]="true"
        [size]="size"
      ></ava-button>

      <ng-template #dots>
        <span class="dots">...</span>
      </ng-template>
    </ng-container>
    </div>
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-right"
      [iconPosition]="iconOnly ? 'only' : 'right'"
      [iconColor]="iconColor"
      [label]="iconOnly ? '' : 'Next'"
      variant="secondary"
      (userClick)="nextPage()"
      [disabled]="currentPage === totalPages"
      [size]="size"
    >
    </ava-button>
  </div>
</ng-container>

<ng-container *ngIf="type === 'unfilled'">
  <div class="pagination-container">
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-left"
      [iconPosition]="iconOnly ? 'only' : 'left'"
      [iconColor]="iconColor"
      variant="secondary"
      [label]="iconOnly ? '' : 'Previous'"
      (userClick)="prevPage()"
      [disabled]="currentPage === 1"
      [size]="size"
      [clear]="true"
    >
    </ava-button>
        <div class="pagination">

    <ng-container *ngFor="let page of pages; trackBy: trackByPage">
      <ava-button
        [label]="page.toString()"
        *ngIf="page !== '...'; else dots"
        (userClick)="goToPage(page)"
        [variant]="page === currentPage ? 'secondary' : 'primary'"
        [outlined]="false"
        [pill]="true"
        [rounded]="true"
        [size]="size"
        [clear]="page !== currentPage"
      ></ava-button>

      <ng-template #dots>
        <span class="dots">...</span>
      </ng-template>
    </ng-container>
        </div>
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-right"
      [iconPosition]="iconOnly ? 'only' : 'right'"
      [iconColor]="iconColor"
      [label]="iconOnly ? '' : 'Next'"
      variant="secondary"
      (userClick)="nextPage()"
      [disabled]="currentPage === totalPages"
      [size]="size"
      [clear]="true"
    >
    </ava-button>
  </div>
</ng-container>

<!-- TYPE: basicunfilled -->
<ng-container *ngIf="type === 'basicunfilled'">
  <div class="pagination-container">
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-left"
      iconPosition="only"
      [iconColor]="iconColor"
      variant="secondary"
      (userClick)="prevPage()"
      [disabled]="currentPage === 1"
      [size]="size"
      [pill]="true"
    >
    </ava-button>
        <div class="pagination">

    <ng-container *ngFor="let page of pages; trackBy: trackByPage">
      <ava-button
        [label]="page.toString()"
        *ngIf="page !== '...'; else dots"
        (userClick)="goToPage(page)"
        [variant]="page === currentPage ? 'primary' : 'secondary'"
        [outlined]="false"
        [pill]="true"
        [rounded]="true"
        [size]="size"
      ></ava-button>

      <ng-template #dots>
        <span class="dots">...</span>
      </ng-template>
    </ng-container>
        </div>
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-right"
      iconPosition="only"
      [iconColor]="iconColor"
      variant="secondary"
      (userClick)="nextPage()"
      [disabled]="currentPage === totalPages"
      [size]="size"
      [pill]="true"

    >
    </ava-button>
  </div>
</ng-container>

<!-- TYPE: pageinfo -->
<ng-container *ngIf="type === 'pageinfo'">
  <div class="pagination">
    <ava-button
      *ngIf="showNavigationButtons"
      class="prev"
      iconName="chevron-left"
      iconPosition="only"
      [iconColor]="iconColor"
      variant="secondary"
      [label]="iconOnly ? '' : 'Previous'"
      (userClick)="prevPage()"
      [pill]="true"
      [size]="size"
        [clear]="true"

      [disabled]="currentPage === 1"
    >
    </ava-button>

    <div class="page-label" [style.font-size]="getFontSize()">
      Page
      <ava-button
        [label]="currentPage.toString()"
        variant="secondary"
        [outlined]="false"
        [pill]="true"
        [rounded]="true"
        [size]="size"
        [clear]="true"
      ></ava-button>
      of {{ totalPages }}
    </div>

    <ava-button
      *ngIf="showNavigationButtons"
      class="prev"
      iconName="chevron-right"
      iconPosition="only"
      [iconColor]="iconColor"
      [label]="iconOnly ? '' : 'Next'"
      variant="secondary"
      (userClick)="nextPage()"
      [disabled]="currentPage === totalPages"
      [clear]="true"
      [pill]="true"
      [size]="size"
    >
    </ava-button>
  </div>
</ng-container>

<!-- TYPE: pageinfofilled -->
<ng-container *ngIf="type === 'pageinfofilled'">
  <div class="pagination">
    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-left"
      iconPosition="only"
      [iconColor]="iconColor"
      variant="secondary"
      (userClick)="prevPage()"
      [disabled]="currentPage === 1"
      [size]="size"
      [pill]="true"
    >
    </ava-button>

    <div class="page-label" [style.font-size]="getFontSize()">
      {{currentPage}}
      / {{ totalPages }}
    </div>

    <ava-button
      *ngIf="showNavigationButtons"
      iconName="chevron-right"
      iconPosition="only"
      [iconColor]="iconColor"
      variant="secondary"
      (userClick)="nextPage()"
      [disabled]="currentPage === totalPages"
      [size]="size"
      [pill]="true"
    >
    </ava-button>
  </div>
</ng-container>
