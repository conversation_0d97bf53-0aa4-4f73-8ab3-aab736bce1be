import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, OnChanges, AfterViewInit, HostListener, forwardRef, ChangeDetectionStrategy } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'ava-progressbar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './progressbar.component.html',
  styleUrl: './progressbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ProgressComponent),
      multi: true,
    },
  ]
})
export class ProgressComponent implements OnInit, OnChanges, AfterViewInit, ControlValueAccessor {
  @Input() percentage: number = 0;
  @Input() bufferValue: number = 0;
  @Input() label: string = '';
  @Input() type: 'circular' | 'linear' = 'circular';
  @Input() color: string = '#2E308E';
  @Input() mode: 'determinate' | 'indeterminate' | 'buffer' | 'query' = 'determinate';
  @Input() svgSize?: number; // Allow the parent to override the size
  @Input() position: '12' | '3' | '6' | '9' | number = '12'; // New input for start position
  @Input() size: 'small' | 'medium' | 'large' = 'medium';

  // Generate unique progress ID
  progressId: string = `progress-${Math.random().toString(36).substring(2, 9)}`;

  // Add computed stroke width based on size
  get strokeWidth(): number {
    const sizeMap = {
      small: 4,
      medium: 12,
      large: 16
    };
    return sizeMap[this.size] || 12;
  }

  // Add getter for linear progress height
  get linearHeight(): number {
    const sizeMap = {
      small: 4,
      medium: 8,
      large: 12
    };
    return sizeMap[this.size] || 8;
  }

  // Update radius calculation to account for stroke width
  get radius(): number {
    return 45 - (this.strokeWidth / 2);
  }
  public readonly circumference = 2 * Math.PI * this.radius;
  dashOffset = this.circumference;
  errorMessage: string = '';

  // Track the display percentage for animation
  displayPercentage: number = 0;

  // Rotation angle for the progress arc
  public rotationAngle: number = 0;

  private onChange = (value: number) => { };
  private onTouched = () => { };

  ngOnInit(): void {
    this.updateSvgSize();
    this.dashOffset = this.circumference;
    this.updateRotationAngle();
  }

  ngAfterViewInit(): void {
    setTimeout(() => this.updateProgress(), 100);
  }

  ngOnChanges(): void {
    this.validateInputs();
    this.updateSvgSize();
    this.updateProgress();
    this.updateRotationAngle();

    // For linear progress, start animation from 0 to target percentage
    if (this.type === 'linear' && this.mode === 'determinate') {
      this.animateLinearProgress();
    }
  }

  private animateLinearProgress(): void {
    // Save target percentage and reset display to 0
    const targetPercentage = this.percentage;
    this.displayPercentage = 0;

    // Calculate animation duration based on percentage
    const duration = Math.max(1, targetPercentage / 100 * 1.5);
    document.documentElement.style.setProperty('--progress-duration', `${duration}s`);

    // Start with 0 and animate to target percentage
    setTimeout(() => {
      const element = document.getElementById(this.progressId) as HTMLElement;
      if (element) {
        element.style.width = '0%';
        element.style.transition = `width var(--progress-duration, ${duration}s) ease-in-out`;

        // Force reflow
        void element.offsetWidth;

        // Set to target width
        element.style.width = `${targetPercentage}%`;

        // Update display percentage during animation
        const startTime = performance.now();
        const updateDisplay = (currentTime: number) => {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / (duration * 1000), 1);
          this.displayPercentage = Math.round(targetPercentage * progress);

          if (progress < 1) {
            requestAnimationFrame(updateDisplay);
          } else {
            this.displayPercentage = targetPercentage;
          }
        };

        requestAnimationFrame(updateDisplay);
      }
    }, 10);
  }

  public updateProgress(): void {
    if (this.mode === 'determinate' || this.mode === 'buffer') {
      this.dashOffset = this.circumference * (1 - this.percentage / 100);

      // Calculate duration dynamically (scale with percentage)
      const duration = (this.percentage / 100) * 1.5; // Scale transition time
      document.documentElement.style.setProperty('--progress-duration', `${duration}s`);

      if (this.type === 'circular') {
        setTimeout(() => {
          const circle = document.getElementById(this.progressId) as HTMLElement;
          if (circle) {
            // Force reflow before applying transition
            circle.style.strokeDashoffset = this.circumference.toString();
            void circle.offsetWidth; // Forces reflow
            circle.style.transition = `stroke-dashoffset var(--progress-duration, 1.5s) ease-in-out`;
            circle.style.strokeDashoffset = this.dashOffset.toString();
          }
        }, 10);
      }
    }
  }

  private validateInputs(): void {
    if (this.percentage < 0 || this.percentage > 100) {
      this.errorMessage = 'Percentage value must be between 0 and 100.';
    } else {
      this.errorMessage = '';
    }
  }

  // Dynamically update SVG size if not set by the parent component
  private updateSvgSize(): void {
    if (!this.svgSize) { // Only update if svgSize is NOT set by the parent
      this.svgSize = window.innerWidth < 480 ? 50 : window.innerWidth < 768 ? 75 : 100;
    }
  }

  // Calculate rotation angle based on position input
  private updateRotationAngle(): void {
    // Map clock positions to degrees (SVG 0deg is 3 o'clock, so adjust accordingly)
    let angle = 0;
    switch (this.position) {
      case '12':
        angle = 0;
        break;
      case '3':
        angle = 90;
        break;
      case '6':
        angle = 180;
        break;
      case '9':
        angle = -90;
        break;
      default:
        angle = typeof this.position === 'number' ? this.position : -90;
    }
    this.rotationAngle = angle;
  }

  @HostListener('window:resize')
  onResize(): void {
    if (!this.svgSize) {
      this.updateSvgSize();
    }
  }

  writeValue(value: number): void {
    this.percentage = value;
    this.validateInputs();
    this.updateProgress();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }
}