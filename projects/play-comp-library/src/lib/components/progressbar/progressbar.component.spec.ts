import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ProgressComponent } from './progressbar.component';
import { By } from '@angular/platform-browser';

describe('ProgressComponent', () => {
  let component: ProgressComponent;
  let fixture: ComponentFixture<ProgressComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ProgressComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(ProgressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create component', () => {
    expect(component).toBeTruthy();
  });

  it('should update svg size on resize when svgSize is undefined', () => {
    component.svgSize = undefined;
    spyOn(component as any, 'updateSvgSize');
    component.onResize();
    expect(component['updateSvgSize']).toHaveBeenCalled();
  });

  it('should not update svg size on resize when svgSize is set', () => {
    component.svgSize = 80;
    spyOn(component as any, 'updateSvgSize');
    component.onResize();
    expect(component['updateSvgSize']).not.toHaveBeenCalled();
  });

  it('should validate percentage input correctly', () => {
    component.percentage = 120;
    component['validateInputs']();
    expect(component.errorMessage).toBe('Percentage value must be between 0 and 100.');

    component.percentage = 50;
    component['validateInputs']();
    expect(component.errorMessage).toBe('');
  });

  it('should call updateSvgSize, validateInputs and updateProgress on ngOnChanges', () => {
    spyOn(component as any, 'validateInputs');
    spyOn(component as any, 'updateSvgSize');
    spyOn(component, 'updateProgress');
    spyOn(component as any, 'animateLinearProgress');

    component.type = 'linear';
    component.mode = 'determinate';

    component.ngOnChanges();

    expect(component['validateInputs']).toHaveBeenCalled();
    expect(component['updateSvgSize']).toHaveBeenCalled();
    expect(component.updateProgress).toHaveBeenCalled();
    expect(component['animateLinearProgress']).toHaveBeenCalled();
  });

  it('should not call animateLinearProgress when not linear or not determinate', () => {
    spyOn(component as any, 'animateLinearProgress');

    component.type = 'circular';
    component.mode = 'determinate';
    component.ngOnChanges();
    expect(component['animateLinearProgress']).not.toHaveBeenCalled();

    component.type = 'linear';
    component.mode = 'indeterminate';
    component.ngOnChanges();
    expect(component['animateLinearProgress']).not.toHaveBeenCalled();
  });

  it('should update dashOffset and set transition styles in updateProgress (circular)', fakeAsync(() => {
    component.type = 'circular';
    component.mode = 'determinate';
    component.percentage = 50;
    const dummy = document.createElement('div');
    dummy.id = component.progressId;
    document.body.appendChild(dummy);

    component.updateProgress();
    tick(11);
    const el = document.getElementById(component.progressId)!;
    expect(el).toBeTruthy();
    document.body.removeChild(dummy);
  }));

  it('should update linear progress style and animate value', fakeAsync(() => {
    component.type = 'linear';
    component.mode = 'determinate';
    component.percentage = 40;
    const div = document.createElement('div');
    div.id = component.progressId;
    document.body.appendChild(div);

    component['animateLinearProgress']();
    tick(1000);
    expect(component.displayPercentage).toBeLessThanOrEqual(component.percentage);
    document.body.removeChild(div);
  }));

  it('should call updateProgress, validateInputs when writeValue is invoked', () => {
    spyOn(component, 'updateProgress');
    spyOn<any>(component, 'validateInputs');
    component.writeValue(75);
    expect(component.percentage).toBe(75);
    expect(component.updateProgress).toHaveBeenCalled();
    expect(component['validateInputs']).toHaveBeenCalled();
  });

  it('should render circular progressbar when type is circular', () => {
    component.type = 'circular';
    fixture.detectChanges();
    const svg = fixture.debugElement.query(By.css('svg'));
    expect(svg).toBeTruthy();
  });

});
