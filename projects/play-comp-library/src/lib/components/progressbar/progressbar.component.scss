// 🔹 Circular Progress
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 100%;

  svg {
    max-width: 100%;
  }

  .progress-background {
    stroke: var(--progress-linear-background);
  }

  .progress-bar {
    stroke-linecap: round;
    transform: rotate(-90deg);
    transform-origin: center;
    transition: var(--progress-transition);
    stroke: url(#progressGradient);

    &.indeterminate {
      animation: var(--progress-indeterminate-animation);
    }
  }

  .progress-text {
    fill: var(--progress-text-fill);
    font-weight: var(--progress-text-weight);
    color: var(--progress-text-color);
  }

  .progress-label {
    margin-top: var(--progress-container-gap);
    font-size: var(--progress-label-font);
    color: var(--progress-label-color);
    font-weight: var(--progress-label-weight);
    line-height: var(--progress-label-line-height);
    text-align: center;
  }

  // Size variants
  &[data-size="small"] svg {
    width: var(--progress-size-sm-circular-size);
    height: var(--progress-size-sm-circular-size);

    .progress-text {
      font-size: var(--progress-size-sm-text);
    }
  }

  &[data-size="medium"] svg {
    width: var(--progress-size-md-circular-size);
    height: var(--progress-size-md-circular-size);

    .progress-text {
      font-size: var(--progress-size-md-text);
    }
  }

  &[data-size="large"] svg {
    width: var(--progress-size-lg-circular-size);
    height: var(--progress-size-lg-circular-size);

    .progress-text {
      font-size: var(--progress-size-lg-text);
    }
  }
}

// 🔹 Linear Progress
.linear-progress-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--progress-container-gap);

  .progress-label {
    font-size: var(--progress-label-font);
    color: var(--progress-label-color);
    font-weight: var(--progress-label-weight);
    line-height: var(--progress-label-line-height);
  }

  .progress-percentage {
    font-size: var(--progress-size-md-percentage);
    color: var(--progress-text-color);
    font-weight: var(--progress-text-weight);
    text-align: right;
  }

  .linear-bar {
    width: 100%;
    background: var(--progress-linear-background);
    border-radius: var(--progress-linear-border-radius);
    overflow: hidden;
    position: relative;

    .linear-progress {
      height: 100%;
      border-radius: inherit;
      transition: var(--progress-linear-fill-transition);
      background: var(--progress-linear-gradient);
      border-radius: var(--progress-linear-border-radius);
    }

    .buffer-bar {
      height: 100%;
      background: var(--progress-linear-gradient);
      transition: var(--progress-linear-fill-transition);
      border-radius: var(--progress-linear-border-radius);
    }

    .indeterminate-bar {
      width: 100%;
      height: 100%;
      position: absolute;
      overflow: hidden;
      background: var(--progress-linear-gradient);

      &::before {
        content: '';
        display: block;
        width: 30%;
        height: 100%;
        background: var(--progress-linear-gradient);
        position: absolute;
        left: -30%;
        animation: var(--progress-indeterminate-animation);
      }
    }
  }
}

// 🔹 Animations
@keyframes indeterminate-move {
  0% {
    left: -30%;
    width: 30%;
  }

  50% {
    left: 50%;
    width: 60%;
  }

  100% {
    left: 100%;
    width: 30%;
  }
}

@keyframes spin {
  0% {
    stroke-dashoffset: 100;
  }

  50% {
    stroke-dashoffset: 50;
  }

  100% {
    stroke-dashoffset: 100;
  }
}

// Progress error message
.progress-error {
  color: var(--progress-error-fill);
  font-size: var(--progress-size-md-percentage);
  margin-top: var(--progress-container-gap);
}

// Responsive sizes
@media (max-width: 768px) {
  .progress-container svg {
    width: var(--progress-size-md-circular-size);
    height: var(--progress-size-md-circular-size);
  }
}