@if (type === 'circular') {
<div class="progress-container" [attr.data-size]="size" role="progressbar" [attr.aria-valuenow]="percentage"
  aria-valuemin="0" aria-valuemax="100">
  <svg [attr.width]="svgSize" [attr.height]="svgSize" viewBox="0 0 100 100">
    <!-- Linear gradient definition -->
    <defs>
      <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" [attr.stop-color]="'var(--progress-gradient-start)'" />
        <stop offset="100%" [attr.stop-color]="'var(--progress-gradient-end)'" />
      </linearGradient>
    </defs>
    <circle class="progress-background" cx="50" cy="50" [attr.r]="radius" [attr.stroke-width]="strokeWidth" fill="none">
    </circle>
    <g [attr.transform]="'rotate(' + rotationAngle + ' 50 50)'">
      <circle class="progress-bar" cx="50" cy="50" [attr.r]="radius" [attr.stroke-width]="strokeWidth" fill="none"
        [attr.id]="progressId" stroke="url(#progressGradient)" [attr.stroke-dasharray]="circumference"
        [attr.stroke-dashoffset]="dashOffset" [class.indeterminate]="mode === 'indeterminate'"
        [style.opacity]="percentage === 0 ? 0 : 1"></circle>
    </g>

    <!-- Centered percentage text inside the circle -->
    <text x="50" y="50" text-anchor="middle" dy="6" transform="translate(0, 2)" class="progress-text">
      {{ percentage }}%
    </text>
  </svg>

  <div class="progress-label">{{ label }}</div>
</div>
}

@if (type === 'linear') {
<div class="linear-progress-container" role="progressbar" [attr.aria-valuenow]="percentage" aria-valuemin="0"
  aria-valuemax="100">
  <div class="progress-label">{{ label }}</div>

  @if (mode === 'determinate' || mode === 'buffer') {
  <div class="progress-percentage">{{ percentage }}%</div>
  }

  <div class="linear-bar" [style.height.px]="linearHeight">
    @if (mode === 'determinate') {
    <div class="linear-progress" [attr.id]="progressId" [style.width.%]="displayPercentage"></div>
    }

    @if (mode === 'buffer') {
    <div class="buffer-bar" [style.width.%]="bufferValue"></div>
    }

    @if (mode === 'indeterminate' || mode === 'query') {
    <div class="indeterminate-bar"></div>
    }
  </div>
</div>
}
<div *ngIf="errorMessage" class="progress-error">{{ errorMessage }}</div>