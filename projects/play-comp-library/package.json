{"name": "@ava/play-comp-library", "version": "1.0.31", "description": "Angular component library with reusable UI components", "author": "Ascendion", "peerDependencies": {"@angular/common": ">=16.0.0 <21.0.0", "@angular/core": ">=16.0.0 <21.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "sideEffects": false, "publishConfig": {"access": "restricted", "registry": "https://pkgs.dev.azure.com/ascendionava/_packaging/ava-platform/npm/registry/"}, "repository": {"type": "git", "url": "git+https://dev.azure.com/ascendionava/ava-platform/_git/ava-component-library"}, "keywords": ["angular", "components", "ui-library"], "license": "MIT", "scripts": {"build": "ng build play-comp-library --configuration production", "prepublishOnly": "npm run build", "test": "ng test play-comp-library --code-coverage --watch=false", "test:watch": "ng test play-comp-library --code-coverage", "test:ci": "ng test play-comp-library --code-coverage --watch=false --browsers=ChromeHeadless", "test:unit": "ng test play-comp-library --code-coverage --watch=false --include=**/*.spec.ts", "test:integration": "ng test play-comp-library --code-coverage --watch=false --include=**/*.integration.spec.ts", "test:accessibility": "ng test play-comp-library --code-coverage --watch=false --include=**/*.a11y.spec.ts", "test:performance": "ng test play-comp-library --code-coverage --watch=false --include=**/*.perf.spec.ts", "test:coverage": "ng test play-comp-library --code-coverage --watch=false --browsers=ChromeHeadless", "test:coverage:report": "npm run test:coverage && open coverage/play-comp-library/index.html", "test:high-contrast": "node scripts/test-high-contrast.js", "test:high-contrast:interactive": "node scripts/test-high-contrast.js --interactive", "test:all": "npm run test:unit && npm run test:integration && npm run test:accessibility && npm run test:performance"}}