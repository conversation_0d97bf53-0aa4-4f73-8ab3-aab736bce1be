/* Header */
.landing-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem 0;
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;

    .logo-section {
      h1 {
        font-size: 2rem;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 1rem;
        color: #64748b;
        margin: 0.5rem 0 0 0;
      }
    }

    .theme-controls {
      display: flex;
      gap: 0.5rem;

      button {
        padding: 0.5rem 1rem;
        border: 1px solid #d1d5db;
        background: #fff;
        border-radius: 0.5rem;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s;

        &:hover {
          background: #f3f4f6;
        }

        &.active {
          background: #6366f1;
          color: white;
          border-color: #6366f1;
        }
      }
    }
  }
}

.layout {
  display: flex;
  height: 100vh; // full height layout

  .sidebar {
    width: 250px; // adjust as needed
    background-color: #f0f0f0; // example color
    padding: 1rem;
    overflow-y: scroll;

    ul {
      margin-top: 75px;
      margin-left: 0;
      padding: 0;

      li {
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid #c3bebe;
        padding: 10px 10px;

        a {
          text-decoration: none;
          color: #596b56;

          &:hover {
            font-weight: bold;
          }
        }
      }
    }
  }

  .content {
    flex: 1;
    background-color: #ffffff; // main content background
    padding: 1rem;
    overflow-y: auto;
  }
}

html[data-theme="dark"] {
  background: #000000;

  body {
    background-color: transparent;
  }

  .layout {
    background-color: transparent;

    .sidebar {
      background-color: #1f1e1e;

      h2 {
        color: #fff !important;
      }

      ul {
        li {
          a {
            color: #fff;
          }
        }
      }
    }

    .content {
      h1,
      h2,
      p {
        color: #fff !important;
      }

      background-color: #151414;
    }
  }
}
