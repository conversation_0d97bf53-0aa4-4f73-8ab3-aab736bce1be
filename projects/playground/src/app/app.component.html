<!-- Header
<header class="landing-header">
    <div class="container">
        <div class="header-content">
            <div class="logo-section">
                <h1>Ava Component Library</h1>
                <p>A comprehensive collection of modern, accessible UI components</p>
            </div>
            <div class="theme-controls">
                <button [ngClass]="{ active: !active }" (click)="dark()">
                    Dark Theme
                </button>
                <button [ngClass]="{ active: active }" (click)="light()">
                    Light Theme
                </button>
            </div>
        </div>
    </div>
</header> -->

<!-- Snackbar for notifications -->
<router-outlet></router-outlet>
<ava-snackbar></ava-snackbar>